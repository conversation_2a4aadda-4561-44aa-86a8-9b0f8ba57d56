// 使用内置的fetch API (Node.js 18+)

// 生成验证载荷的函数
const generateValidationPayload = (score, board) => {
  const data = `${score}-${JSON.stringify(board)}-${Date.now()}`;
  let hash = 0;
  for (let i = 0; i < data.length; i++) {
    const char = data.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash;
  }
  return Math.abs(hash).toString(16);
};

const testBoard = [
  [2, 4, 8, 16],
  [32, 64, 128, 256],
  [512, 1024, 2048, 4096],
  [0, 0, 0, 0]
];

const testScore = 51032;
const validationPayload = generateValidationPayload(testScore, testBoard);

console.log('🧪 测试提交高分数...');
console.log(`玩家: mk99`);
console.log(`分数: ${testScore.toLocaleString()}`);
console.log(`验证载荷: ${validationPayload}`);

fetch('http://localhost:3001/api/scores', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    playerName: 'mk99',
    score: testScore,
    validationPayload
  })
}).then(res => res.json()).then(data => {
  console.log('\n📊 提交结果:', data);
  if (data.success) {
    console.log('✅ 分数提交成功！');
    if (data.isNewRecord) {
      console.log('🎉 这是新纪录！');
    }
  } else {
    console.log('❌ 分数提交失败:', data.error);
  }
}).catch(err => {
  console.error('❌ 网络错误:', err.message);
});
