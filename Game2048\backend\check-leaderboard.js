const sqlite3 = require('sqlite3').verbose();

const db = new sqlite3.Database('game2048.db');

console.log('🏆 当前排行榜:');

db.all('SELECT playerName, score, createdAt FROM scores ORDER BY score DESC LIMIT 5', (err, rows) => {
  if (err) {
    console.error('❌ 查询失败:', err);
  } else {
    rows.forEach((row, index) => {
      console.log(`${index + 1}. ${row.playerName}: ${row.score.toLocaleString()} 分 (${row.createdAt})`);
    });
  }
  db.close();
});
