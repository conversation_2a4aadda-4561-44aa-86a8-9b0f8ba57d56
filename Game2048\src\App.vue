<script setup lang="ts">
import { ref } from 'vue'
import GameBoard from './components/GameBoard.vue'
import Leaderboard from './components/Leaderboard.vue'

const currentView = ref('game')

const showGame = () => {
  currentView.value = 'game'
}

const showLeaderboard = () => {
  currentView.value = 'leaderboard'
}
</script>

<template>
  <div id="app">
    <header>
      <h1>2048 游戏</h1>
      <p>使用方向键移动方块，合并相同数字达到2048！</p>

      <nav class="main-nav">
        <button
          :class="['nav-btn', { active: currentView === 'game' }]"
          @click="showGame"
        >
          游戏
        </button>
        <button
          :class="['nav-btn', { active: currentView === 'leaderboard' }]"
          @click="showLeaderboard"
        >
          排行榜
        </button>
      </nav>
    </header>

    <main>
      <GameBoard v-if="currentView === 'game'" />
      <Leaderboard v-if="currentView === 'leaderboard'" />
    </main>
  </div>
</template>

<style scoped>
#app {
  min-height: 100vh;
  background: #faf8ef;
  padding: 20px;
}

header {
  text-align: center;
  margin-bottom: 30px;
}

header h1 {
  color: #776e65;
  font-size: 48px;
  font-weight: bold;
  margin: 0 0 10px 0;
}

header p {
  color: #776e65;
  font-size: 16px;
  margin: 0;
}

main {
  display: flex;
  justify-content: center;
}

.main-nav {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
}

.nav-btn {
  padding: 10px 20px;
  border: 2px solid #bbada0;
  background: white;
  color: #776e65;
  border-radius: 6px;
  cursor: pointer;
  font-weight: bold;
  font-size: 16px;
  transition: all 0.2s;
}

.nav-btn:hover {
  background: #f9f6f2;
}

.nav-btn.active {
  background: #8f7a66;
  color: white;
  border-color: #8f7a66;
}
</style>
