/**
 * 压力测试 - 模拟并发请求
 */

const API_BASE_URL = 'http://localhost:3001/api';

// 生成验证载荷的函数
const generateValidationPayload = (score, board) => {
  const data = `${score}-${JSON.stringify(board)}-${Date.now()}-${Math.random()}`;
  let hash = 0;
  for (let i = 0; i < data.length; i++) {
    const char = data.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash;
  }
  return Math.abs(hash).toString(16);
};

// 模拟单个用户提交分数
async function simulateScoreSubmission(userId) {
  const testBoard = [
    [2, 4, 8, 16],
    [32, 64, 128, 256],
    [512, 1024, 0, 0],
    [0, 0, 0, 0]
  ];
  
  const testScore = Math.floor(Math.random() * 10000) + 1000;
  const playerName = `压力测试用户_${userId}`;
  const validationPayload = generateValidationPayload(testScore, testBoard);
  
  const startTime = Date.now();
  
  try {
    const response = await fetch(`${API_BASE_URL}/scores`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        playerName,
        score: testScore,
        validationPayload
      })
    });
    
    const data = await response.json();
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    return {
      success: response.ok,
      responseTime,
      status: response.status,
      userId,
      score: testScore,
      error: response.ok ? null : data.error
    };
  } catch (error) {
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    return {
      success: false,
      responseTime,
      status: 0,
      userId,
      score: testScore,
      error: error.message
    };
  }
}

// 模拟获取排行榜
async function simulateLeaderboardRequest() {
  const startTime = Date.now();
  
  try {
    const response = await fetch(`${API_BASE_URL}/leaderboard?period=alltime&limit=10`);
    const data = await response.json();
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    return {
      success: response.ok,
      responseTime,
      status: response.status,
      dataSize: JSON.stringify(data).length,
      error: response.ok ? null : data.error
    };
  } catch (error) {
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    return {
      success: false,
      responseTime,
      status: 0,
      dataSize: 0,
      error: error.message
    };
  }
}

// 运行压力测试
async function runStressTest() {
  console.log('🚀 开始压力测试...\n');
  
  // 测试1: 并发分数提交
  console.log('📊 测试1: 并发分数提交 (20个并发请求)');
  const concurrentUsers = 20;
  const scorePromises = [];
  
  for (let i = 0; i < concurrentUsers; i++) {
    scorePromises.push(simulateScoreSubmission(i));
  }
  
  const scoreResults = await Promise.all(scorePromises);
  
  const successfulSubmissions = scoreResults.filter(r => r.success).length;
  const failedSubmissions = scoreResults.filter(r => !r.success).length;
  const avgResponseTime = scoreResults.reduce((sum, r) => sum + r.responseTime, 0) / scoreResults.length;
  const maxResponseTime = Math.max(...scoreResults.map(r => r.responseTime));
  const minResponseTime = Math.min(...scoreResults.map(r => r.responseTime));
  
  console.log(`✅ 成功: ${successfulSubmissions}/${concurrentUsers}`);
  console.log(`❌ 失败: ${failedSubmissions}/${concurrentUsers}`);
  console.log(`⏱️  平均响应时间: ${avgResponseTime.toFixed(2)}ms`);
  console.log(`⏱️  最大响应时间: ${maxResponseTime}ms`);
  console.log(`⏱️  最小响应时间: ${minResponseTime}ms`);
  
  if (failedSubmissions > 0) {
    console.log('\n❌ 失败的请求:');
    scoreResults.filter(r => !r.success).forEach(r => {
      console.log(`   用户${r.userId}: ${r.error} (${r.responseTime}ms)`);
    });
  }
  
  // 测试2: 并发排行榜请求
  console.log('\n📊 测试2: 并发排行榜请求 (50个并发请求)');
  const concurrentReads = 50;
  const leaderboardPromises = [];
  
  for (let i = 0; i < concurrentReads; i++) {
    leaderboardPromises.push(simulateLeaderboardRequest());
  }
  
  const leaderboardResults = await Promise.all(leaderboardPromises);
  
  const successfulReads = leaderboardResults.filter(r => r.success).length;
  const failedReads = leaderboardResults.filter(r => !r.success).length;
  const avgReadTime = leaderboardResults.reduce((sum, r) => sum + r.responseTime, 0) / leaderboardResults.length;
  const maxReadTime = Math.max(...leaderboardResults.map(r => r.responseTime));
  const minReadTime = Math.min(...leaderboardResults.map(r => r.responseTime));
  
  console.log(`✅ 成功: ${successfulReads}/${concurrentReads}`);
  console.log(`❌ 失败: ${failedReads}/${concurrentReads}`);
  console.log(`⏱️  平均响应时间: ${avgReadTime.toFixed(2)}ms`);
  console.log(`⏱️  最大响应时间: ${maxReadTime}ms`);
  console.log(`⏱️  最小响应时间: ${minReadTime}ms`);
  
  // 测试3: 混合负载测试
  console.log('\n📊 测试3: 混合负载测试 (10个写入 + 30个读取)');
  const mixedPromises = [];
  
  // 10个分数提交
  for (let i = 0; i < 10; i++) {
    mixedPromises.push(simulateScoreSubmission(`mixed_${i}`));
  }
  
  // 30个排行榜请求
  for (let i = 0; i < 30; i++) {
    mixedPromises.push(simulateLeaderboardRequest());
  }
  
  const mixedResults = await Promise.all(mixedPromises);
  const mixedSuccessful = mixedResults.filter(r => r.success).length;
  const mixedFailed = mixedResults.filter(r => !r.success).length;
  const avgMixedTime = mixedResults.reduce((sum, r) => sum + r.responseTime, 0) / mixedResults.length;
  
  console.log(`✅ 成功: ${mixedSuccessful}/${mixedPromises.length}`);
  console.log(`❌ 失败: ${mixedFailed}/${mixedPromises.length}`);
  console.log(`⏱️  平均响应时间: ${avgMixedTime.toFixed(2)}ms`);
  
  // 总结
  console.log('\n🎯 压力测试总结:');
  const totalRequests = concurrentUsers + concurrentReads + mixedPromises.length;
  const totalSuccessful = successfulSubmissions + successfulReads + mixedSuccessful;
  const totalFailed = failedSubmissions + failedReads + mixedFailed;
  const successRate = (totalSuccessful / totalRequests * 100).toFixed(2);
  
  console.log(`📈 总请求数: ${totalRequests}`);
  console.log(`✅ 总成功数: ${totalSuccessful}`);
  console.log(`❌ 总失败数: ${totalFailed}`);
  console.log(`📊 成功率: ${successRate}%`);
  
  if (successRate >= 95) {
    console.log('🎉 压力测试通过！服务器性能良好。');
    return true;
  } else if (successRate >= 90) {
    console.log('⚠️  压力测试基本通过，但有一些性能问题需要关注。');
    return true;
  } else {
    console.log('❌ 压力测试失败！服务器性能需要优化。');
    return false;
  }
}

// 运行测试
runStressTest().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('❌ 压力测试出错:', error);
  process.exit(1);
});
