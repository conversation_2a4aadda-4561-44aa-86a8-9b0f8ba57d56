/**
 * 部署状态检查脚本
 */

const https = require('https');
const http = require('http');

console.log('🔍 检查部署状态...\n');

// 检查URL是否可访问
function checkURL(url, name) {
  return new Promise((resolve) => {
    const protocol = url.startsWith('https:') ? https : http;
    
    const req = protocol.get(url, (res) => {
      if (res.statusCode >= 200 && res.statusCode < 400) {
        console.log(`✅ ${name}: ${url} (状态码: ${res.statusCode})`);
        resolve(true);
      } else {
        console.log(`⚠️  ${name}: ${url} (状态码: ${res.statusCode})`);
        resolve(false);
      }
    });
    
    req.on('error', (error) => {
      console.log(`❌ ${name}: ${url} (错误: ${error.message})`);
      resolve(false);
    });
    
    req.setTimeout(10000, () => {
      console.log(`⏰ ${name}: ${url} (超时)`);
      req.destroy();
      resolve(false);
    });
  });
}

// 检查API端点
async function checkAPI(baseURL) {
  console.log(`🔧 检查API端点: ${baseURL}`);
  
  const endpoints = [
    { path: '/api/health', name: '健康检查' },
    { path: '/api/leaderboard', name: '排行榜' },
    { path: '/api/stats', name: '统计信息' }
  ];
  
  for (const endpoint of endpoints) {
    const url = baseURL + endpoint.path;
    await checkURL(url, endpoint.name);
  }
}

// 主函数
async function main() {
  console.log('请在部署完成后运行此脚本来验证部署状态。\n');
  
  // 示例URL - 用户需要替换为实际的部署URL
  const frontendURL = 'https://your-app.vercel.app';
  const backendURL = 'https://your-app.railway.app';
  
  console.log('📝 请将以下URL替换为您的实际部署URL：');
  console.log(`前端URL: ${frontendURL}`);
  console.log(`后端URL: ${backendURL}\n`);
  
  console.log('🔍 如果您已经部署，请修改此脚本中的URL并重新运行。\n');
  
  // 检查本地开发服务器（如果正在运行）
  console.log('🏠 检查本地开发服务器...');
  await checkURL('http://localhost:5173', '前端开发服务器');
  await checkURL('http://localhost:3001/api/health', '后端开发服务器');
  
  console.log('\n📋 部署检查清单：');
  console.log('□ 代码已推送到GitHub');
  console.log('□ 后端已部署到Railway');
  console.log('□ 前端已部署到Vercel');
  console.log('□ 环境变量已正确配置');
  console.log('□ CORS设置已更新');
  console.log('□ 游戏功能测试通过');
  console.log('□ 排行榜功能测试通过');
  
  console.log('\n🎯 下一步：');
  console.log('1. 完成部署后，更新此脚本中的URL');
  console.log('2. 重新运行此脚本验证部署');
  console.log('3. 分享您的游戏链接！');
}

main().catch(console.error);
