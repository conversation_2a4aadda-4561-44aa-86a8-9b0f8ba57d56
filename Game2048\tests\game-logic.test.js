/**
 * 2048游戏核心逻辑单元测试
 */

// 简单的测试框架
class TestRunner {
  constructor() {
    this.tests = [];
    this.passed = 0;
    this.failed = 0;
  }

  test(name, fn) {
    this.tests.push({ name, fn });
  }

  async run() {
    console.log('🧪 开始运行2048游戏逻辑测试...\n');
    
    for (const { name, fn } of this.tests) {
      try {
        await fn();
        console.log(`✅ ${name}`);
        this.passed++;
      } catch (error) {
        console.log(`❌ ${name}: ${error.message}`);
        this.failed++;
      }
    }

    console.log(`\n📊 测试结果: ${this.passed} 通过, ${this.failed} 失败`);
    return this.failed === 0;
  }
}

// 断言函数
function assertEqual(actual, expected, message = '') {
  if (JSON.stringify(actual) !== JSON.stringify(expected)) {
    throw new Error(`${message}\n期望: ${JSON.stringify(expected)}\n实际: ${JSON.stringify(actual)}`);
  }
}

function assertTrue(condition, message = '') {
  if (!condition) {
    throw new Error(message || '断言失败');
  }
}

// 游戏逻辑函数（从GameBoard.vue中提取）
function initializeBoard() {
  return Array(4).fill().map(() => Array(4).fill(0));
}

function addRandomTile(board) {
  const emptyCells = [];
  for (let i = 0; i < 4; i++) {
    for (let j = 0; j < 4; j++) {
      if (board[i][j] === 0) {
        emptyCells.push({ row: i, col: j });
      }
    }
  }
  
  if (emptyCells.length > 0) {
    const randomCell = emptyCells[Math.floor(Math.random() * emptyCells.length)];
    board[randomCell.row][randomCell.col] = Math.random() < 0.9 ? 2 : 4;
  }
  
  return board;
}

function moveLeft(board) {
  let moved = false;
  let score = 0;
  const newBoard = board.map(row => [...row]);

  for (let i = 0; i < 4; i++) {
    const row = newBoard[i].filter(cell => cell !== 0);
    const merged = [];
    let j = 0;

    while (j < row.length) {
      if (j < row.length - 1 && row[j] === row[j + 1]) {
        merged.push(row[j] * 2);
        score += row[j] * 2;
        j += 2;
      } else {
        merged.push(row[j]);
        j++;
      }
    }

    while (merged.length < 4) {
      merged.push(0);
    }

    for (let k = 0; k < 4; k++) {
      if (newBoard[i][k] !== merged[k]) {
        moved = true;
      }
      newBoard[i][k] = merged[k];
    }
  }

  return { board: newBoard, moved, score };
}

function moveRight(board) {
  const reversedBoard = board.map(row => [...row].reverse());
  const result = moveLeft(reversedBoard);
  result.board = result.board.map(row => row.reverse());
  return result;
}

function moveUp(board) {
  const transposedBoard = transpose(board);
  const result = moveLeft(transposedBoard);
  result.board = transpose(result.board);
  return result;
}

function moveDown(board) {
  const transposedBoard = transpose(board);
  const result = moveRight(transposedBoard);
  result.board = transpose(result.board);
  return result;
}

function transpose(board) {
  return board[0].map((_, colIndex) => board.map(row => row[colIndex]));
}

function checkGameOver(board) {
  // 检查是否有空格
  for (let i = 0; i < 4; i++) {
    for (let j = 0; j < 4; j++) {
      if (board[i][j] === 0) return false;
    }
  }

  // 检查是否可以合并
  for (let i = 0; i < 4; i++) {
    for (let j = 0; j < 4; j++) {
      const current = board[i][j];
      if (
        (i < 3 && board[i + 1][j] === current) ||
        (j < 3 && board[i][j + 1] === current)
      ) {
        return false;
      }
    }
  }

  return true;
}

// 测试用例
const runner = new TestRunner();

// 测试初始化
runner.test('初始化棋盘', () => {
  const board = initializeBoard();
  assertEqual(board.length, 4, '棋盘应该有4行');
  assertEqual(board[0].length, 4, '每行应该有4列');
  assertTrue(board.every(row => row.every(cell => cell === 0)), '初始棋盘应该全为0');
});

// 测试左移
runner.test('左移 - 基本合并', () => {
  const board = [
    [2, 2, 0, 0],
    [4, 4, 8, 0],
    [0, 0, 0, 0],
    [2, 0, 2, 0]
  ];
  
  const result = moveLeft(board);
  const expected = [
    [4, 0, 0, 0],
    [8, 8, 0, 0],
    [0, 0, 0, 0],
    [4, 0, 0, 0]
  ];
  
  assertEqual(result.board, expected, '左移合并结果不正确');
  assertEqual(result.score, 16, '分数计算不正确'); // 4 + 8 + 4 = 16
  assertTrue(result.moved, '应该检测到移动');
});

// 测试右移
runner.test('右移 - 基本合并', () => {
  const board = [
    [0, 0, 2, 2],
    [0, 8, 4, 4],
    [0, 0, 0, 0],
    [0, 2, 0, 2]
  ];
  
  const result = moveRight(board);
  const expected = [
    [0, 0, 0, 4],
    [0, 0, 8, 8],
    [0, 0, 0, 0],
    [0, 0, 0, 4]
  ];
  
  assertEqual(result.board, expected, '右移合并结果不正确');
  assertTrue(result.moved, '应该检测到移动');
});

// 测试上移
runner.test('上移 - 基本合并', () => {
  const board = [
    [2, 4, 0, 2],
    [2, 4, 0, 0],
    [0, 8, 0, 2],
    [0, 0, 0, 0]
  ];
  
  const result = moveUp(board);
  const expected = [
    [4, 8, 0, 4],
    [0, 8, 0, 0],
    [0, 0, 0, 0],
    [0, 0, 0, 0]
  ];
  
  assertEqual(result.board, expected, '上移合并结果不正确');
  assertTrue(result.moved, '应该检测到移动');
});

// 测试下移
runner.test('下移 - 基本合并', () => {
  const board = [
    [0, 0, 0, 0],
    [0, 8, 0, 2],
    [2, 4, 0, 0],
    [2, 4, 0, 2]
  ];
  
  const result = moveDown(board);
  const expected = [
    [0, 0, 0, 0],
    [0, 0, 0, 0],
    [0, 8, 0, 0],
    [4, 8, 0, 4]
  ];
  
  assertEqual(result.board, expected, '下移合并结果不正确');
  assertTrue(result.moved, '应该检测到移动');
});

// 测试无法移动的情况
runner.test('无法移动检测', () => {
  const board = [
    [4, 0, 0, 0],
    [0, 0, 0, 0],
    [0, 0, 0, 0],
    [0, 0, 0, 0]
  ];
  
  const result = moveLeft(board);
  assertTrue(!result.moved, '不应该检测到移动');
  assertEqual(result.score, 0, '无移动时分数应该为0');
});

// 测试游戏结束检测
runner.test('游戏结束检测 - 有空格', () => {
  const board = [
    [2, 4, 8, 16],
    [4, 8, 16, 32],
    [8, 16, 32, 64],
    [16, 32, 64, 0]
  ];
  
  assertTrue(!checkGameOver(board), '有空格时游戏不应该结束');
});

runner.test('游戏结束检测 - 可以合并', () => {
  const board = [
    [2, 4, 8, 16],
    [4, 8, 16, 32],
    [8, 16, 32, 64],
    [16, 32, 64, 64]
  ];
  
  assertTrue(!checkGameOver(board), '可以合并时游戏不应该结束');
});

runner.test('游戏结束检测 - 真正结束', () => {
  const board = [
    [2, 4, 8, 16],
    [4, 8, 16, 32],
    [8, 16, 32, 64],
    [16, 32, 64, 128]
  ];
  
  assertTrue(checkGameOver(board), '无空格且无法合并时游戏应该结束');
});

// 运行测试
runner.run().then(success => {
  process.exit(success ? 0 : 1);
});
