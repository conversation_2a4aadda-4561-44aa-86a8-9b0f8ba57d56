const crypto = require('crypto');

// 测试API的简单脚本
const API_BASE_URL = 'http://localhost:3001/api';

// 生成验证载荷
function generateValidationPayload(score, board) {
  const data = `${score}-${JSON.stringify(board)}-${Date.now()}`;
  return crypto.createHash('sha256').update(data).digest('hex');
}

// 测试提交分数
async function testSubmitScore() {
  const testBoard = [
    [2, 4, 8, 16],
    [32, 64, 128, 256],
    [512, 1024, 2048, 0],
    [0, 0, 0, 0]
  ];
  
  const testScore = 25000;
  const validationPayload = generateValidationPayload(testScore, testBoard);
  
  try {
    const response = await fetch(`${API_BASE_URL}/scores`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        playerName: '测试玩家',
        score: testScore,
        validationPayload
      })
    });
    
    const data = await response.json();
    console.log('Submit Score Response:', data);
    
    if (response.ok) {
      console.log('✅ 分数提交成功');
    } else {
      console.log('❌ 分数提交失败:', data.error);
    }
  } catch (error) {
    console.error('❌ 网络错误:', error.message);
  }
}

// 测试获取排行榜
async function testGetLeaderboard() {
  try {
    const response = await fetch(`${API_BASE_URL}/leaderboard?period=alltime&limit=5`);
    const data = await response.json();
    
    console.log('Leaderboard Response:', data);
    
    if (response.ok) {
      console.log('✅ 排行榜获取成功');
      console.log('排行榜数据:', data.leaderboard);
    } else {
      console.log('❌ 排行榜获取失败:', data.error);
    }
  } catch (error) {
    console.error('❌ 网络错误:', error.message);
  }
}

// 测试获取统计信息
async function testGetStats() {
  try {
    const response = await fetch(`${API_BASE_URL}/stats`);
    const data = await response.json();
    
    console.log('Stats Response:', data);
    
    if (response.ok) {
      console.log('✅ 统计信息获取成功');
    } else {
      console.log('❌ 统计信息获取失败:', data.error);
    }
  } catch (error) {
    console.error('❌ 网络错误:', error.message);
  }
}

// 运行所有测试
async function runTests() {
  console.log('🚀 开始API测试...\n');
  
  console.log('1. 测试健康检查...');
  try {
    const response = await fetch(`${API_BASE_URL}/health`);
    const data = await response.json();
    console.log('Health Check:', data);
    console.log('✅ 健康检查通过\n');
  } catch (error) {
    console.error('❌ 健康检查失败:', error.message);
    return;
  }
  
  console.log('2. 测试分数提交...');
  await testSubmitScore();
  console.log('');
  
  console.log('3. 测试排行榜获取...');
  await testGetLeaderboard();
  console.log('');
  
  console.log('4. 测试统计信息获取...');
  await testGetStats();
  console.log('');
  
  console.log('🎉 API测试完成！');
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testSubmitScore,
  testGetLeaderboard,
  testGetStats,
  runTests
};
