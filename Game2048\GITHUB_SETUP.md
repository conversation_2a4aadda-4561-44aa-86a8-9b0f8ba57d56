# 🚀 GitHub设置和推送指南

## 第1步：创建GitHub仓库

1. 访问 https://github.com/new
2. 设置仓库信息：
   - **Repository name**: `2048-game` (或您喜欢的名字)
   - **Description**: `🎮 Modern 2048 game with leaderboard - Vue 3 + TypeScript + Node.js`
   - **Visibility**: Public (推荐，可免费使用部署服务)
   - **不要**勾选任何初始化选项 (README, .gitignore, license)

3. 点击 "Create repository"

## 第2步：获取仓库URL

创建仓库后，GitHub会显示仓库URL，类似于：
```
https://github.com/YOUR_USERNAME/2048-game.git
```

## 第3步：推送代码

在项目目录中运行以下命令（替换为您的实际仓库URL）：

```bash
# 添加远程仓库
git remote add origin https://github.com/YOUR_USERNAME/2048-game.git

# 设置主分支
git branch -M main

# 推送代码
git push -u origin main
```

## 第4步：验证推送

推送成功后，您应该能在GitHub上看到所有文件，包括：
- ✅ 前端代码 (src/, public/, package.json 等)
- ✅ 后端代码 (backend/ 文件夹)
- ✅ 部署配置 (vercel.json, netlify.toml 等)
- ✅ 文档文件 (README.md, DEPLOYMENT.md 等)
- ✅ 测试文件 (tests/ 文件夹)

## 第5步：准备部署

推送成功后，您就可以开始部署了：

### 部署后端到Railway
1. 访问 https://railway.app
2. 使用GitHub账号登录
3. 选择 "Deploy from GitHub repo"
4. 选择您刚创建的仓库
5. **重要**: 设置根目录为 `backend`
6. 设置环境变量：
   ```
   NODE_ENV=production
   PORT=3001
   CORS_ORIGIN=https://your-frontend-domain.vercel.app
   ```

### 部署前端到Vercel
1. 访问 https://vercel.com
2. 使用GitHub账号登录
3. 选择 "New Project"
4. 选择您的仓库
5. 根目录保持默认 (项目根目录)
6. 设置环境变量：
   ```
   VITE_API_BASE_URL=https://your-backend-domain.railway.app/api
   ```

## 🔧 故障排除

### 如果推送失败：

1. **认证问题**：
   - 确保您已登录GitHub
   - 可能需要设置Personal Access Token

2. **网络问题**：
   - 检查网络连接
   - 尝试使用VPN

3. **权限问题**：
   - 确保仓库是您创建的
   - 检查仓库URL是否正确

### 手动推送步骤：

如果自动脚本不工作，请手动执行：

```bash
# 检查当前状态
git status

# 如果有未提交的更改
git add .
git commit -m "Final commit before deployment"

# 添加远程仓库 (替换为您的URL)
git remote add origin https://github.com/YOUR_USERNAME/YOUR_REPO.git

# 推送
git branch -M main
git push -u origin main
```

## 📞 需要帮助？

如果遇到问题，请：
1. 检查错误信息
2. 确认GitHub仓库已创建
3. 验证仓库URL正确
4. 确保网络连接正常

---

**下一步**: 推送成功后，继续按照 `DEPLOYMENT.md` 进行部署！
