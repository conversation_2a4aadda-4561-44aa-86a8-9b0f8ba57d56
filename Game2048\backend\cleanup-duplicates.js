const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 数据库路径
const dbPath = path.join(__dirname, 'game2048.db');
const db = new sqlite3.Database(dbPath);

console.log('开始清理重复玩家数据...');

// 清理重复数据，只保留每个玩家的最高分
db.serialize(() => {
  // 创建临时表存储每个玩家的最高分记录
  db.run(`
    CREATE TEMPORARY TABLE temp_best_scores AS
    SELECT 
      playerName,
      MAX(score) as bestScore,
      MIN(id) as keepId
    FROM scores 
    GROUP BY playerName
  `);

  // 获取需要保留的记录ID
  db.run(`
    UPDATE temp_best_scores 
    SET keepId = (
      SELECT id 
      FROM scores 
      WHERE scores.playerName = temp_best_scores.playerName 
        AND scores.score = temp_best_scores.bestScore 
      ORDER BY createdAt DESC 
      LIMIT 1
    )
  `);

  // 删除不是最高分的重复记录
  db.run(`
    DELETE FROM scores 
    WHERE id NOT IN (
      SELECT keepId FROM temp_best_scores
    )
  `, function(err) {
    if (err) {
      console.error('清理失败:', err);
    } else {
      console.log(`✅ 清理完成！删除了 ${this.changes} 条重复记录`);
    }
    
    // 验证清理结果
    db.all(`
      SELECT playerName, COUNT(*) as count 
      FROM scores 
      GROUP BY playerName 
      HAVING count > 1
    `, (err, rows) => {
      if (err) {
        console.error('验证失败:', err);
      } else if (rows.length > 0) {
        console.log('⚠️  仍有重复数据:', rows);
      } else {
        console.log('✅ 验证通过：没有重复的玩家名字');
      }
      
      // 显示当前排行榜
      db.all(`
        SELECT playerName, score, createdAt 
        FROM scores 
        ORDER BY score DESC 
        LIMIT 10
      `, (err, rows) => {
        if (err) {
          console.error('获取排行榜失败:', err);
        } else {
          console.log('\n当前排行榜:');
          rows.forEach((row, index) => {
            console.log(`${index + 1}. ${row.playerName}: ${row.score.toLocaleString()}`);
          });
        }
        
        db.close();
      });
    });
  });
});
