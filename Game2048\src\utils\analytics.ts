/**
 * 轻量级分析工具
 * 注重隐私，只收集必要的游戏数据
 */

interface AnalyticsEvent {
  event: string
  properties?: Record<string, any>
  timestamp: number
}

interface GameSession {
  sessionId: string
  startTime: number
  endTime?: number
  events: AnalyticsEvent[]
}

class Analytics {
  private session: GameSession | null = null
  private isEnabled: boolean = false
  private apiEndpoint: string = ''

  constructor() {
    // 只在生产环境启用
    this.isEnabled = import.meta.env.PROD && !import.meta.env.VITE_DISABLE_ANALYTICS
    this.apiEndpoint = import.meta.env.VITE_ANALYTICS_ENDPOINT || ''
  }

  // 开始新的游戏会话
  startSession() {
    if (!this.isEnabled) return

    this.session = {
      sessionId: this.generateSessionId(),
      startTime: Date.now(),
      events: []
    }

    this.track('session_start')
  }

  // 结束游戏会话
  endSession() {
    if (!this.isEnabled || !this.session) return

    this.session.endTime = Date.now()
    this.track('session_end', {
      duration: this.session.endTime - this.session.startTime,
      eventCount: this.session.events.length
    })

    // 发送会话数据
    this.sendSessionData()
    this.session = null
  }

  // 追踪事件
  track(event: string, properties?: Record<string, any>) {
    if (!this.isEnabled || !this.session) return

    const analyticsEvent: AnalyticsEvent = {
      event,
      properties,
      timestamp: Date.now()
    }

    this.session.events.push(analyticsEvent)

    // 对于重要事件，立即发送
    if (this.isImportantEvent(event)) {
      this.sendEvent(analyticsEvent)
    }
  }

  // 游戏相关事件追踪
  trackGameStart() {
    this.track('game_start')
  }

  trackGameEnd(score: number, moves: number, duration: number) {
    this.track('game_end', {
      final_score: score,
      total_moves: moves,
      game_duration: duration,
      reached_2048: score >= 2048
    })
  }

  trackMove(direction: string, scoreGained: number) {
    this.track('move', {
      direction,
      score_gained: scoreGained
    })
  }

  trackScoreSubmission(score: number, success: boolean) {
    this.track('score_submission', {
      score,
      success
    })
  }

  trackLeaderboardView(period: string) {
    this.track('leaderboard_view', {
      period
    })
  }

  // 生成会话ID
  private generateSessionId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  // 判断是否为重要事件
  private isImportantEvent(event: string): boolean {
    const importantEvents = ['game_end', 'score_submission', 'error']
    return importantEvents.includes(event)
  }

  // 发送单个事件
  private async sendEvent(event: AnalyticsEvent) {
    if (!this.apiEndpoint) return

    try {
      await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: 'event',
          sessionId: this.session?.sessionId,
          ...event
        })
      })
    } catch (error) {
      // 静默失败，不影响游戏体验
      console.debug('Analytics event failed to send:', error)
    }
  }

  // 发送会话数据
  private async sendSessionData() {
    if (!this.apiEndpoint || !this.session) return

    try {
      await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: 'session',
          ...this.session
        })
      })
    } catch (error) {
      // 静默失败，不影响游戏体验
      console.debug('Analytics session failed to send:', error)
    }
  }
}

// 创建全局分析实例
export const analytics = new Analytics()

// 页面加载时开始会话
if (typeof window !== 'undefined') {
  analytics.startSession()

  // 页面卸载时结束会话
  window.addEventListener('beforeunload', () => {
    analytics.endSession()
  })

  // 页面隐藏时也结束会话（移动端）
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'hidden') {
      analytics.endSession()
    } else if (document.visibilityState === 'visible') {
      analytics.startSession()
    }
  })
}
