/**
 * GitHub推送脚本
 */

const { execSync } = require('child_process');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('🚀 GitHub推送助手\n');

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

async function main() {
  try {
    console.log('📋 请确保您已经在GitHub上创建了仓库\n');
    
    // 获取GitHub仓库URL
    const repoUrl = await askQuestion('请输入您的GitHub仓库URL (例如: https://github.com/username/repo-name.git): ');
    
    if (!repoUrl) {
      console.log('❌ 请提供有效的仓库URL');
      process.exit(1);
    }
    
    console.log('\n🔧 配置Git远程仓库...');
    
    // 检查是否已经有远程仓库
    try {
      const remotes = execSync('git remote -v', { encoding: 'utf8' });
      if (remotes.includes('origin')) {
        console.log('⚠️  检测到已存在的origin远程仓库，将其移除...');
        execSync('git remote remove origin');
      }
    } catch (error) {
      // 没有远程仓库，继续
    }
    
    // 添加远程仓库
    execSync(`git remote add origin ${repoUrl}`);
    console.log('✅ 远程仓库已添加');
    
    // 设置主分支
    console.log('\n🌿 设置主分支...');
    execSync('git branch -M main');
    console.log('✅ 主分支已设置为 main');
    
    // 推送代码
    console.log('\n📤 推送代码到GitHub...');
    console.log('这可能需要几分钟时间，请耐心等待...\n');
    
    try {
      execSync('git push -u origin main', { stdio: 'inherit' });
      console.log('\n🎉 代码推送成功！');
      
      // 显示仓库信息
      const repoName = repoUrl.replace('.git', '').replace('https://github.com/', '');
      console.log('\n📊 仓库信息:');
      console.log(`🔗 仓库地址: https://github.com/${repoName}`);
      console.log(`📱 在线预览: https://github.com/${repoName}#readme`);
      
      console.log('\n🚀 下一步部署指南:');
      console.log('1. 部署后端到Railway:');
      console.log('   - 访问 https://railway.app');
      console.log('   - 选择 "Deploy from GitHub repo"');
      console.log(`   - 选择仓库: ${repoName}`);
      console.log('   - 根目录设置为: backend');
      
      console.log('\n2. 部署前端到Vercel:');
      console.log('   - 访问 https://vercel.com');
      console.log('   - 选择 "New Project"');
      console.log(`   - 选择仓库: ${repoName}`);
      console.log('   - 根目录保持默认');
      
      console.log('\n📋 记住设置环境变量:');
      console.log('Railway (后端):');
      console.log('  - NODE_ENV=production');
      console.log('  - PORT=3001');
      console.log('  - CORS_ORIGIN=https://your-frontend.vercel.app');
      
      console.log('\nVercel (前端):');
      console.log('  - VITE_API_BASE_URL=https://your-backend.railway.app/api');
      
    } catch (error) {
      console.log('\n❌ 推送失败，可能的原因:');
      console.log('1. 网络连接问题');
      console.log('2. GitHub认证问题');
      console.log('3. 仓库权限问题');
      console.log('\n💡 解决方案:');
      console.log('1. 检查网络连接');
      console.log('2. 确保已登录GitHub');
      console.log('3. 检查仓库URL是否正确');
      console.log('4. 尝试使用SSH URL而不是HTTPS');
      
      throw error;
    }
    
  } catch (error) {
    console.error('\n❌ 推送过程中出错:', error.message);
    console.log('\n🔧 手动推送命令:');
    console.log('git remote add origin <your-repo-url>');
    console.log('git branch -M main');
    console.log('git push -u origin main');
  } finally {
    rl.close();
  }
}

main();
