<template>
  <div class="game-container">
    <div class="score-container">
      <div class="score-box">
        <div class="score-label">分数</div>
        <div class="score-value" ref="scoreValue">{{ score }}</div>
      </div>
      <div class="score-box">
        <div class="score-label">最高分</div>
        <div class="score-value">{{ bestScore }}</div>
      </div>
    </div>

    <div class="game-board"
         ref="gameBoard"
         @touchstart="handleTouchStart"
         @touchend="handleTouchEnd">
      <div class="grid-container">
        <div class="grid-row" v-for="row in 4" :key="row">
          <div class="grid-cell" v-for="col in 4" :key="col"></div>
        </div>
      </div>
      
      <TransitionGroup name="tile" tag="div" class="tile-container">
        <div
          v-for="tile in tiles"
          :key="tile.id"
          :data-id="tile.id"
          :class="['tile', `tile-${tile.value}`, `tile-position-${tile.row}-${tile.col}`]"
        >
          {{ tile.value }}
        </div>
      </TransitionGroup>
    </div>
    
    <div class="game-controls">
      <button @click="restartGame" class="restart-btn">新游戏</button>
      <p class="auto-save-hint">游戏进度自动保存</p>

      <!-- 开发测试按钮 -->
      <div class="test-controls" v-if="isDevelopment">
        <h4>🎨 动态颜色测试</h4>
        <div class="test-buttons">
          <button @click="setTestScore(500)" class="test-btn">500分</button>
          <button @click="setTestScore(2000)" class="test-btn">2K分</button>
          <button @click="setTestScore(8000)" class="test-btn">8K分</button>
          <button @click="setTestScore(15000)" class="test-btn">15K分</button>
          <button @click="setTestScore(30000)" class="test-btn">30K分</button>
          <button @click="runColorTest" class="test-btn-auto">自动测试</button>
        </div>
      </div>
    </div>

    <!-- 游戏恢复提示 -->
    <Transition name="notification">
      <div v-if="showRestoreNotification" class="restore-notification">
        <div class="notification-content">
          <span class="notification-icon">🎮</span>
          <span class="notification-text">游戏进度已恢复</span>
        </div>
      </div>
    </Transition>
    
    <div v-if="isGameOver" class="game-over-overlay">
      <div class="game-over-message">
        <h2>游戏结束!</h2>
        <p>最终分数: {{ score }}</p>

        <div v-if="!showScoreSubmission" class="game-over-actions">
          <button @click="showScoreSubmission = true" class="submit-score-btn">提交分数</button>
          <button @click="restartGame" class="restart-btn">再来一局</button>
        </div>

        <div v-else class="score-submission">
          <input
            v-model="playerName"
            type="text"
            placeholder="输入你的昵称"
            maxlength="20"
            class="player-name-input"
            @keyup.enter="submitScore"
          />
          <div class="submission-actions">
            <button @click="submitScore" :disabled="submitting || !playerName.trim()" class="submit-btn">
              {{ submitting ? '提交中...' : '提交' }}
            </button>
            <button @click="showScoreSubmission = false" class="cancel-btn">取消</button>
          </div>
          <div v-if="submissionError" class="submission-error">{{ submissionError }}</div>
          <div v-if="submissionSuccess" class="submission-success">{{ submissionSuccessMessage }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

interface Tile {
  id: number
  value: number
  row: number
  col: number
}

// 响应式数据
const board = ref<number[][]>([])
const tiles = ref<Tile[]>([])
const score = ref(0)
const bestScore = ref(0)
const isGameOver = ref(false)
const gameBoard = ref<HTMLElement>()
const scoreValue = ref<HTMLElement>()
let nextTileId = 1

// 触摸事件相关
let touchStartX = 0
let touchStartY = 0

// 分数提交相关
const showScoreSubmission = ref(false)
const playerName = ref('')
const submitting = ref(false)
const submissionError = ref('')
const submissionSuccess = ref(false)
const submissionSuccessMessage = ref('分数提交成功！')

// 游戏恢复提示
const showRestoreNotification = ref(false)

// 动态颜色系统
const scoreIntensity = ref(0) // 0-1 的强度值，用于控制颜色变化

// 开发模式检测
const isDevelopment = ref(import.meta.env.DEV)

// 计算分数强度（0-1之间）
const calculateScoreIntensity = (currentScore: number) => {
  // 分数阶段定义：
  // 0-1000: 冷静期 (0-0.2)
  // 1000-5000: 升温期 (0.2-0.5)
  // 5000-15000: 紧张期 (0.5-0.8)
  // 15000+: 极限期 (0.8-1.0)

  if (currentScore <= 1000) {
    return Math.min(currentScore / 1000 * 0.2, 0.2)
  } else if (currentScore <= 5000) {
    return 0.2 + (currentScore - 1000) / 4000 * 0.3
  } else if (currentScore <= 15000) {
    return 0.5 + (currentScore - 5000) / 10000 * 0.3
  } else {
    return Math.min(0.8 + (currentScore - 15000) / 20000 * 0.2, 1.0)
  }
}

// 根据强度生成动态颜色
const getDynamicScoreColor = (intensity: number) => {
  // 颜色从冷色调（蓝紫）逐渐变为暖色调（红橙）
  const hue = 240 - (intensity * 180) // 240度(蓝) -> 60度(黄) -> 0度(红)
  const saturation = 60 + (intensity * 40) // 饱和度从60%增加到100%
  const lightness = 50 + (intensity * 10) // 亮度从50%增加到60%

  return `hsl(${hue}, ${saturation}%, ${lightness}%)`
}

// 根据强度生成背景渐变
const getDynamicBackgroundGradient = (intensity: number) => {
  if (intensity < 0.3) {
    // 冷静期：蓝紫色渐变
    return `linear-gradient(135deg,
      hsl(240, 60%, 70%) 0%,
      hsl(260, 50%, 75%) 100%)`
  } else if (intensity < 0.6) {
    // 升温期：紫红色渐变
    return `linear-gradient(135deg,
      hsl(280, 70%, 65%) 0%,
      hsl(320, 60%, 70%) 100%)`
  } else if (intensity < 0.8) {
    // 紧张期：红橙色渐变
    return `linear-gradient(135deg,
      hsl(20, 80%, 60%) 0%,
      hsl(40, 75%, 65%) 100%)`
  } else {
    // 极限期：激烈红色渐变 + 脉冲效果
    return `linear-gradient(135deg,
      hsl(0, 90%, 55%) 0%,
      hsl(15, 85%, 60%) 50%,
      hsl(30, 80%, 65%) 100%)`
  }
}

const API_BASE_URL = 'http://localhost:3001/api'

// 初始化游戏
const initGame = (forceNew = false) => {
  // 加载最高分
  loadBestScore()

  // 尝试加载保存的游戏状态
  if (!forceNew && loadGameState()) {
    // 显示游戏恢复提示
    showRestoreNotification.value = true
    setTimeout(() => {
      showRestoreNotification.value = false
    }, 3000)
    // 恢复游戏时也要更新动态颜色
    updateScoreIntensity()
    return
  }

  // 创建新游戏
  board.value = Array(4).fill(null).map(() => Array(4).fill(0))
  tiles.value = []
  score.value = 0
  isGameOver.value = false
  nextTileId = 1

  // 添加两个初始方块
  addRandomTile()
  addRandomTile()

  // 初始化动态颜色
  updateScoreIntensity()

  // 保存初始状态
  saveGameState()
}

// 添加随机方块
const addRandomTile = () => {
  const emptyCells = []
  for (let row = 0; row < 4; row++) {
    for (let col = 0; col < 4; col++) {
      if (board.value[row][col] === 0) {
        emptyCells.push({ row, col })
      }
    }
  }
  
  if (emptyCells.length > 0) {
    const randomCell = emptyCells[Math.floor(Math.random() * emptyCells.length)]
    const value = Math.random() < 0.9 ? 2 : 4
    
    board.value[randomCell.row][randomCell.col] = value
    tiles.value.push({
      id: nextTileId++,
      value,
      row: randomCell.row,
      col: randomCell.col
    })
  }
}

// 移动逻辑 - 向左移动
const moveLeft = (): boolean => {
  let moved = false
  const newBoard = board.value.map(row => [...row])
  
  for (let row = 0; row < 4; row++) {
    const line = newBoard[row].filter(val => val !== 0)
    const merged = []
    let i = 0
    
    while (i < line.length) {
      if (i < line.length - 1 && line[i] === line[i + 1]) {
        // 合并相同的方块
        merged.push(line[i] * 2)
        score.value += line[i] * 2
        i += 2
      } else {
        merged.push(line[i])
        i++
      }
    }
    
    // 填充剩余位置为0
    while (merged.length < 4) {
      merged.push(0)
    }
    
    // 检查是否有移动
    for (let col = 0; col < 4; col++) {
      if (newBoard[row][col] !== merged[col]) {
        moved = true
      }
    }
    
    newBoard[row] = merged
  }
  
  if (moved) {
    board.value = newBoard
    updateTiles()
    return true
  }
  
  return false
}

// 旋转棋盘（用于实现其他方向的移动）
const rotateBoard = (board: number[][]): number[][] => {
  const size = board.length
  const rotated = Array(size).fill(null).map(() => Array(size).fill(0))
  
  for (let row = 0; row < size; row++) {
    for (let col = 0; col < size; col++) {
      rotated[col][size - 1 - row] = board[row][col]
    }
  }
  
  return rotated
}

// 移动方向处理
const move = (direction: string): boolean => {
  let moved = false
  let rotations = 0
  
  // 根据方向确定需要旋转的次数
  switch (direction) {
    case 'left':
      rotations = 0
      break
    case 'up':
      rotations = 1
      break
    case 'right':
      rotations = 2
      break
    case 'down':
      rotations = 3
      break
  }
  
  // 旋转棋盘到左移位置
  let currentBoard = board.value
  for (let i = 0; i < rotations; i++) {
    currentBoard = rotateBoard(currentBoard)
  }
  
  board.value = currentBoard
  moved = moveLeft()
  
  // 旋转回原来的方向
  for (let i = 0; i < (4 - rotations) % 4; i++) {
    board.value = rotateBoard(board.value)
  }
  
  return moved
}

// 更新方块显示
const updateTiles = () => {
  const newTiles: Tile[] = []

  for (let row = 0; row < 4; row++) {
    for (let col = 0; col < 4; col++) {
      if (board.value[row][col] !== 0) {
        newTiles.push({
          id: nextTileId++,
          value: board.value[row][col],
          row,
          col
        })
      }
    }
  }

  tiles.value = newTiles
}

// 检查游戏是否结束
const checkGameOver = (): boolean => {
  // 检查是否还有空格
  for (let row = 0; row < 4; row++) {
    for (let col = 0; col < 4; col++) {
      if (board.value[row][col] === 0) {
        return false
      }
    }
  }

  // 检查是否还能合并
  for (let row = 0; row < 4; row++) {
    for (let col = 0; col < 4; col++) {
      const current = board.value[row][col]

      // 检查右边
      if (col < 3 && board.value[row][col + 1] === current) {
        return false
      }

      // 检查下面
      if (row < 3 && board.value[row + 1][col] === current) {
        return false
      }
    }
  }

  return true
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (isGameOver.value) return

  let moved = false

  switch (event.key) {
    case 'ArrowLeft':
      event.preventDefault()
      moved = move('left')
      break
    case 'ArrowUp':
      event.preventDefault()
      moved = move('up')
      break
    case 'ArrowRight':
      event.preventDefault()
      moved = move('right')
      break
    case 'ArrowDown':
      event.preventDefault()
      moved = move('down')
      break
  }

  if (moved) {
    addRandomTile()
    triggerScoreAnimation()
    updateBestScore()
    saveGameState()

    if (checkGameOver()) {
      isGameOver.value = true
      saveGameState()
    }
  } else {
    // 无法移动时触发震动效果
    triggerShakeAnimation()
  }
}

// 触摸事件处理
const handleTouchStart = (event: TouchEvent) => {
  if (event.touches.length === 1) {
    touchStartX = event.touches[0].clientX
    touchStartY = event.touches[0].clientY
  }
}

const handleTouchEnd = (event: TouchEvent) => {
  if (isGameOver.value || event.changedTouches.length !== 1) return

  const touchEndX = event.changedTouches[0].clientX
  const touchEndY = event.changedTouches[0].clientY

  const deltaX = touchEndX - touchStartX
  const deltaY = touchEndY - touchStartY

  const minSwipeDistance = 30

  if (Math.abs(deltaX) < minSwipeDistance && Math.abs(deltaY) < minSwipeDistance) {
    return
  }

  let moved = false

  if (Math.abs(deltaX) > Math.abs(deltaY)) {
    // 水平滑动
    if (deltaX > 0) {
      moved = move('right')
    } else {
      moved = move('left')
    }
  } else {
    // 垂直滑动
    if (deltaY > 0) {
      moved = move('down')
    } else {
      moved = move('up')
    }
  }

  if (moved) {
    addRandomTile()
    triggerScoreAnimation()
    updateBestScore()
    updateScoreIntensity() // 更新动态颜色
    saveGameState()

    if (checkGameOver()) {
      isGameOver.value = true
      saveGameState()
    }
  } else {
    // 无法移动时触发震动效果
    triggerShakeAnimation()
  }
}

// 生成验证载荷
const generateValidationPayload = (score: number, board: number[][]) => {
  const data = `${score}-${JSON.stringify(board)}-${Date.now()}`
  // 简单的哈希函数（在实际应用中应该使用更安全的方法）
  let hash = 0
  for (let i = 0; i < data.length; i++) {
    const char = data.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  return Math.abs(hash).toString(16)
}

// 提交分数
const submitScore = async () => {
  if (!playerName.value.trim()) {
    submissionError.value = '请输入昵称'
    return
  }

  submitting.value = true
  submissionError.value = ''
  submissionSuccess.value = false

  try {
    const validationPayload = generateValidationPayload(score.value, board.value)

    const response = await fetch(`${API_BASE_URL}/scores`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        playerName: playerName.value.trim(),
        score: score.value,
        validationPayload
      })
    })

    const data = await response.json()

    if (!response.ok) {
      throw new Error(data.error || '提交失败')
    }

    submissionSuccess.value = true
    submissionError.value = '' // 清除错误信息

    // 根据是否是新纪录显示不同的成功消息
    if (data.isNewRecord) {
      submissionSuccessMessage.value = '🎉 新纪录！分数更新成功！'
      setTimeout(() => {
        showScoreSubmission.value = false
        submissionSuccess.value = false
      }, 4000) // 新纪录显示更长时间
    } else {
      submissionSuccessMessage.value = '分数提交成功！'
      setTimeout(() => {
        showScoreSubmission.value = false
        submissionSuccess.value = false
      }, 3000)
    }

  } catch (error) {
    let errorMessage = '网络错误'
    if (error instanceof Error) {
      errorMessage = error.message
      // 特殊处理存储空间不足的错误
      if (errorMessage.includes('服务器存储空间不足')) {
        errorMessage = '🚫 服务器存储空间不足，请稍后再试或联系管理员'
      }
    }
    submissionError.value = errorMessage
  } finally {
    submitting.value = false
  }
}

// 重新开始游戏
const restartGame = () => {
  clearGameState()
  showScoreSubmission.value = false
  playerName.value = ''
  submissionError.value = ''
  submissionSuccess.value = false
  initGame(true)
}

// 测试函数
const setTestScore = (testScore: number) => {
  score.value = testScore
  updateScoreIntensity()
  triggerScoreAnimation()
  console.log(`🎨 设置测试分数: ${testScore}, 强度: ${scoreIntensity.value.toFixed(2)}`)
}

const runColorTest = () => {
  console.log('🎨 开始自动颜色测试...')
  const testScores = [0, 500, 1500, 3000, 8000, 12000, 20000, 50000]
  let index = 0

  const testInterval = setInterval(() => {
    if (index >= testScores.length) {
      clearInterval(testInterval)
      console.log('✅ 动态颜色测试完成')
      return
    }

    setTestScore(testScores[index])
    index++
  }, 2000)
}

// 保存和加载游戏状态
const saveGameState = () => {
  const gameState = {
    board: board.value,
    tiles: tiles.value,
    score: score.value,
    isGameOver: isGameOver.value,
    nextTileId
  }
  localStorage.setItem('game2048-state', JSON.stringify(gameState))
}

const loadGameState = (): boolean => {
  const saved = localStorage.getItem('game2048-state')
  if (saved) {
    try {
      const gameState = JSON.parse(saved)
      board.value = gameState.board
      tiles.value = gameState.tiles
      score.value = gameState.score
      isGameOver.value = gameState.isGameOver
      nextTileId = gameState.nextTileId
      return true
    } catch (e) {
      console.error('Failed to load game state:', e)
      return false
    }
  }
  return false
}

const clearGameState = () => {
  localStorage.removeItem('game2048-state')
}

// 保存和加载最高分
const saveBestScore = () => {
  localStorage.setItem('game2048-best-score', bestScore.value.toString())
}

const loadBestScore = () => {
  const saved = localStorage.getItem('game2048-best-score')
  if (saved) {
    bestScore.value = parseInt(saved, 10)
  }
}

const updateBestScore = () => {
  if (score.value > bestScore.value) {
    bestScore.value = score.value
    saveBestScore()
  }
}

// 更新分数强度和动态样式
const updateScoreIntensity = () => {
  scoreIntensity.value = calculateScoreIntensity(score.value)

  // 更新分数容器的动态样式
  const scoreContainer = document.querySelector('.score-container') as HTMLElement
  const gameContainer = document.querySelector('.game-container') as HTMLElement

  if (scoreContainer && gameContainer) {
    const intensity = scoreIntensity.value
    const dynamicColor = getDynamicScoreColor(intensity)
    const dynamicBackground = getDynamicBackgroundGradient(intensity)

    // 应用动态颜色到分数值和标签
    const scoreValues = scoreContainer.querySelectorAll('.score-value')
    const scoreLabels = scoreContainer.querySelectorAll('.score-label')

    scoreValues.forEach(element => {
      (element as HTMLElement).style.color = dynamicColor
    })

    // 分数标签也随强度变化颜色
    const labelColor = intensity > 0.5 ? 'rgba(255, 255, 255, 0.9)' : 'rgba(102, 126, 234, 0.8)'
    scoreLabels.forEach(element => {
      (element as HTMLElement).style.color = labelColor
    })

    // 应用动态背景到分数框
    const scoreBoxes = scoreContainer.querySelectorAll('.score-box')
    scoreBoxes.forEach(element => {
      (element as HTMLElement).style.background = dynamicBackground

      // 高强度时添加脉冲效果
      if (intensity > 0.8) {
        element.classList.add('score-pulse-intense')
      } else if (intensity > 0.6) {
        element.classList.add('score-pulse-medium')
      } else {
        element.classList.remove('score-pulse-intense', 'score-pulse-medium')
      }
    })

    // 为整个游戏容器添加动态背景氛围
    if (intensity > 0.7) {
      gameContainer.classList.add('game-intense')
    } else if (intensity > 0.4) {
      gameContainer.classList.add('game-heated')
    } else {
      gameContainer.classList.remove('game-intense', 'game-heated')
    }
  }
}

// 动画效果函数
const triggerScoreAnimation = () => {
  if (scoreValue.value) {
    // 根据分数强度选择不同的动画效果
    const intensity = scoreIntensity.value

    if (intensity > 0.8) {
      scoreValue.value.classList.add('score-increase-intense')
    } else if (intensity > 0.5) {
      scoreValue.value.classList.add('score-increase-heated')
    } else {
      scoreValue.value.classList.add('score-increase')
    }

    setTimeout(() => {
      scoreValue.value?.classList.remove('score-increase', 'score-increase-heated', 'score-increase-intense')
    }, 500)
  }
}

const triggerShakeAnimation = () => {
  if (gameBoard.value) {
    gameBoard.value.classList.add('shake')
    setTimeout(() => {
      gameBoard.value?.classList.remove('shake')
    }, 500)
  }
}

const triggerMergeAnimation = (tileId: number) => {
  const tileElement = document.querySelector(`.tile[data-id="${tileId}"]`)
  if (tileElement) {
    tileElement.classList.add('tile-merge')
    setTimeout(() => {
      tileElement.classList.remove('tile-merge')
    }, 300)
  }
}

// 生命周期钩子
onMounted(() => {
  initGame()
  window.addEventListener('keydown', handleKeydown)

  // 开发测试：添加全局测试函数
  if (typeof window !== 'undefined') {
    (window as any).testDynamicColors = () => {
      console.log('🎨 测试动态颜色系统...')
      const testScores = [0, 500, 1500, 3000, 8000, 12000, 20000, 50000]
      let index = 0

      const testInterval = setInterval(() => {
        if (index >= testScores.length) {
          clearInterval(testInterval)
          console.log('✅ 动态颜色测试完成')
          return
        }

        score.value = testScores[index]
        updateScoreIntensity()
        triggerScoreAnimation()
        console.log(`分数: ${score.value}, 强度: ${scoreIntensity.value.toFixed(2)}`)
        index++
      }, 2000)
    }
  }
})

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.game-container {
  max-width: 500px;
  margin: 0 auto;
  padding: 30px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  min-height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 游戏容器动态背景状态 */
.game-container.game-heated {
  background: linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%);
}

.game-container.game-intense {
  background: linear-gradient(135deg, #ff4757 0%, #ff3838 100%);
  animation: container-pulse 2s infinite;
}

@keyframes container-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.005);
  }
}

.score-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
  gap: 15px;
}

.score-box {
  background: rgba(255, 255, 255, 0.95);
  padding: 16px 24px;
  border-radius: 16px;
  color: #667eea;
  text-align: center;
  min-width: 100px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.score-box:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.score-label {
  font-size: 12px;
  font-weight: 700;
  text-transform: uppercase;
  margin-bottom: 8px;
  letter-spacing: 0.1em;
  opacity: 0.8;
}

.score-value {
  font-size: 28px;
  font-weight: 800;
  letter-spacing: -0.02em;
}

.game-board {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 15px;
  margin-bottom: 30px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.grid-container {
  position: relative;
  z-index: 1;
}

.grid-row {
  display: flex;
  margin-bottom: 15px;
}

.grid-row:last-child {
  margin-bottom: 0;
}

.grid-cell {
  width: 100px;
  height: 100px;
  background: rgba(102, 126, 234, 0.08);
  border-radius: 12px;
  margin-right: 15px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.grid-cell:last-child {
  margin-right: 0;
}

.tile-container {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 2;
}

.tile {
  position: absolute;
  width: 100px;
  height: 100px;
  border-radius: 12px;
  font-weight: 800;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  letter-spacing: -0.02em;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 方块位置 */
.tile-position-0-0 { top: 0px; left: 0px; }
.tile-position-0-1 { top: 0px; left: 110px; }
.tile-position-0-2 { top: 0px; left: 220px; }
.tile-position-0-3 { top: 0px; left: 330px; }
.tile-position-1-0 { top: 110px; left: 0px; }
.tile-position-1-1 { top: 110px; left: 110px; }
.tile-position-1-2 { top: 110px; left: 220px; }
.tile-position-1-3 { top: 110px; left: 330px; }
.tile-position-2-0 { top: 220px; left: 0px; }
.tile-position-2-1 { top: 220px; left: 110px; }
.tile-position-2-2 { top: 220px; left: 220px; }
.tile-position-2-3 { top: 220px; left: 330px; }
.tile-position-3-0 { top: 330px; left: 0px; }
.tile-position-3-1 { top: 330px; left: 110px; }
.tile-position-3-2 { top: 330px; left: 220px; }
.tile-position-3-3 { top: 330px; left: 330px; }

/* 现代渐变色方块 - 符合WCAG AA级对比度标准 */
.tile-2 {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8eaff 100%);
  color: #4c51bf;
  box-shadow: 0 4px 15px rgba(76, 81, 191, 0.2);
}

.tile-4 {
  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
  color: #3730a3;
  box-shadow: 0 4px 15px rgba(55, 48, 163, 0.25);
}

.tile-8 {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #92400e;
  box-shadow: 0 4px 15px rgba(146, 64, 14, 0.3);
}

.tile-16 {
  background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%);
  color: #9a3412;
  box-shadow: 0 4px 15px rgba(154, 52, 18, 0.3);
}

.tile-32 {
  background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%);
  color: #991b1b;
  box-shadow: 0 4px 15px rgba(153, 27, 27, 0.3);
}

.tile-64 {
  background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
  color: #6b21a8;
  box-shadow: 0 4px 15px rgba(107, 33, 168, 0.3);
}

.tile-128 {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #166534;
  font-size: 28px;
  box-shadow: 0 6px 20px rgba(22, 101, 52, 0.3);
}

.tile-256 {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1e40af;
  font-size: 28px;
  box-shadow: 0 6px 20px rgba(30, 64, 175, 0.3);
}

.tile-512 {
  background: linear-gradient(135deg, #fef7cd 0%, #fef08a 100%);
  color: #a16207;
  font-size: 28px;
  box-shadow: 0 6px 20px rgba(161, 98, 7, 0.3);
}

.tile-1024 {
  background: linear-gradient(135deg, #ffedd5 0%, #fed7aa 100%);
  color: #c2410c;
  font-size: 24px;
  box-shadow: 0 8px 25px rgba(194, 65, 12, 0.4);
}

.tile-2048 {
  background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
  color: #be185d;
  font-size: 24px;
  box-shadow: 0 8px 25px rgba(190, 24, 93, 0.4);
  animation: pulse-glow 2s infinite;
}

/* 2048方块的特殊发光效果 */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 8px 25px rgba(190, 24, 93, 0.4), 0 0 20px rgba(190, 24, 93, 0.3);
  }
  50% {
    box-shadow: 0 8px 25px rgba(190, 24, 93, 0.6), 0 0 30px rgba(190, 24, 93, 0.5);
  }
}

/* 增强动画效果 */
.tile-enter-active {
  animation: tile-appear 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.tile:hover {
  transform: scale(1.05);
  z-index: 10;
}

/* 方块合并动画 */
.tile-merge {
  animation: tile-merge 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes tile-merge {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes tile-appear {
  0% {
    opacity: 0;
    transform: scale(0) rotate(180deg);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1) rotate(90deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* 方块移动时的增强效果 */
.tile {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, opacity;
}

.tile:active {
  transform: scale(0.95);
}

/* 游戏板震动效果（当无法移动时） */
.game-board.shake {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* 分数增加动画 - 多级强度 */
.score-value.score-increase {
  animation: score-pop 0.3s ease-out;
}

.score-value.score-increase-heated {
  animation: score-pop-heated 0.4s ease-out;
}

.score-value.score-increase-intense {
  animation: score-pop-intense 0.5s ease-out;
}

@keyframes score-pop {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); color: #48bb78; }
  100% { transform: scale(1); }
}

@keyframes score-pop-heated {
  0% { transform: scale(1); }
  30% { transform: scale(1.15); color: #ff6b6b; }
  60% { transform: scale(1.05); color: #ff8e53; }
  100% { transform: scale(1); }
}

@keyframes score-pop-intense {
  0% { transform: scale(1); }
  20% { transform: scale(1.2); color: #ff4757; }
  40% { transform: scale(1.1); color: #ff3838; }
  60% { transform: scale(1.15); color: #ff4757; }
  80% { transform: scale(1.05); color: #ff6b6b; }
  100% { transform: scale(1); }
}

/* 动态分数脉冲效果 */
.score-box.score-pulse-medium {
  animation: score-pulse-medium 2s infinite;
}

.score-box.score-pulse-intense {
  animation: score-pulse-intense 1.5s infinite;
}

@keyframes score-pulse-medium {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 12px 40px rgba(255, 100, 50, 0.3);
  }
}

@keyframes score-pulse-intense {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
  25% {
    transform: scale(1.03);
    box-shadow: 0 15px 50px rgba(255, 50, 0, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 20px 60px rgba(255, 0, 0, 0.5);
  }
  75% {
    transform: scale(1.03);
    box-shadow: 0 15px 50px rgba(255, 50, 0, 0.4);
  }
}

/* 分数容器的平滑过渡 */
.score-box {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.score-value {
  transition: color 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 游戏恢复提示 */
.restore-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1001;
  background: rgba(72, 187, 120, 0.95);
  color: white;
  padding: 16px 20px;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(72, 187, 120, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.notification-icon {
  font-size: 20px;
}

.notification-text {
  font-weight: 600;
  font-size: 14px;
}

/* 提示动画 */
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100px);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100px);
}

.game-controls {
  text-align: center;
}

.restart-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 14px 28px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  letter-spacing: 0.01em;
}

.restart-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.restart-btn:active {
  transform: translateY(0);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.auto-save-hint {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin-top: 15px;
  text-align: center;
  font-weight: 400;
}

/* 测试控件样式 */
.test-controls {
  margin-top: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.test-controls h4 {
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 10px 0;
  font-size: 14px;
  text-align: center;
}

.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.test-btn, .test-btn-auto {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.test-btn-auto {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
  box-shadow: 0 2px 8px rgba(78, 205, 196, 0.3);
}

.test-btn:hover, .test-btn-auto:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
}

.test-btn-auto:hover {
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.4);
}

.test-btn:active, .test-btn-auto:active {
  transform: translateY(0);
}

.game-over-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(102, 126, 234, 0.9);
  backdrop-filter: blur(10px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
  box-sizing: border-box;
  animation: overlay-appear 0.3s ease-out;
}

@keyframes overlay-appear {
  0% {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  100% {
    opacity: 1;
    backdrop-filter: blur(10px);
  }
}

.game-over-message {
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-sizing: border-box;
  position: relative;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: message-appear 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes message-appear {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.game-over-message h2 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 15px;
  font-size: 32px;
  font-weight: 800;
  letter-spacing: -0.02em;
}

.game-over-message p {
  color: #4a5568;
  margin-bottom: 30px;
  font-size: 18px;
  font-weight: 500;
}

.game-over-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.submit-score-btn {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 14px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
}

.submit-score-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
}

.score-submission {
  width: 100%;
}

.player-name-input {
  width: 100%;
  padding: 16px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  border-radius: 12px;
  font-size: 16px;
  margin-bottom: 20px;
  text-align: center;
  outline: none;
  background: rgba(255, 255, 255, 0.9);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
}

.player-name-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: white;
}

.submission-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-bottom: 15px;
}

.submit-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 14px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.submit-btn:disabled {
  background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(160, 174, 192, 0.2);
}

.cancel-btn {
  background: linear-gradient(135deg, #fc8181 0%, #f56565 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 14px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(252, 129, 129, 0.3);
}

.cancel-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(252, 129, 129, 0.4);
}

.submission-error {
  color: #e53e3e;
  text-align: center;
  font-size: 14px;
  margin-top: 15px;
  padding: 12px;
  background: rgba(254, 226, 226, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(252, 129, 129, 0.3);
  font-weight: 500;
}

.submission-success {
  color: #38a169;
  text-align: center;
  font-size: 14px;
  margin-top: 15px;
  padding: 12px;
  background: rgba(240, 253, 244, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(72, 187, 120, 0.3);
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 600px) {
  .game-container {
    max-width: 320px;
    padding: 10px;
    min-height: auto;
    justify-content: flex-start;
  }

  .grid-cell, .tile {
    width: 70px;
    height: 70px;
    font-size: 24px;
  }

  .tile-128, .tile-256, .tile-512 {
    font-size: 20px;
  }

  .tile-1024, .tile-2048 {
    font-size: 18px;
  }

  /* 重新定义移动端方块位置 */
  .tile-position-0-0 { top: 0px; left: 0px; }
  .tile-position-0-1 { top: 0px; left: 80px; }
  .tile-position-0-2 { top: 0px; left: 160px; }
  .tile-position-0-3 { top: 0px; left: 240px; }
  .tile-position-1-0 { top: 80px; left: 0px; }
  .tile-position-1-1 { top: 80px; left: 80px; }
  .tile-position-1-2 { top: 80px; left: 160px; }
  .tile-position-1-3 { top: 80px; left: 240px; }
  .tile-position-2-0 { top: 160px; left: 0px; }
  .tile-position-2-1 { top: 160px; left: 80px; }
  .tile-position-2-2 { top: 160px; left: 160px; }
  .tile-position-2-3 { top: 160px; left: 240px; }
  .tile-position-3-0 { top: 240px; left: 0px; }
  .tile-position-3-1 { top: 240px; left: 80px; }
  .tile-position-3-2 { top: 240px; left: 160px; }
  .tile-position-3-3 { top: 240px; left: 240px; }

  .grid-cell {
    margin-right: 10px;
  }

  .score-box {
    padding: 8px 15px;
    min-width: 60px;
  }

  .score-label {
    font-size: 11px;
  }

  .score-value {
    font-size: 20px;
  }

  /* 游戏结束弹窗响应式 */
  .game-over-overlay {
    padding: 15px;
  }

  .game-over-message {
    padding: 20px;
    max-width: 95vw;
    max-height: 85vh;
  }

  .game-over-message h2 {
    font-size: 22px;
    margin-bottom: 10px;
  }

  .game-over-message p {
    font-size: 16px;
    margin-bottom: 20px;
  }

  .game-over-actions {
    flex-direction: column;
    gap: 15px;
  }

  .submit-score-btn, .restart-btn {
    width: 100%;
    padding: 14px 20px;
    font-size: 16px;
    min-height: 48px;
  }

  .submission-actions {
    flex-direction: column;
    gap: 12px;
  }

  .submit-btn, .cancel-btn {
    width: 100%;
    padding: 14px 20px;
    font-size: 16px;
    min-height: 48px;
  }

  .player-name-input {
    padding: 14px;
    font-size: 16px;
    min-height: 48px;
  }
}

@media (max-width: 400px) {
  .game-container {
    max-width: 280px;
    padding: 8px;
  }

  .grid-cell, .tile {
    width: 60px;
    height: 60px;
    font-size: 20px;
  }

  .tile-128, .tile-256, .tile-512 {
    font-size: 18px;
  }

  .tile-1024, .tile-2048 {
    font-size: 16px;
  }

  /* 小屏幕方块位置 */
  .tile-position-0-0 { top: 0px; left: 0px; }
  .tile-position-0-1 { top: 0px; left: 70px; }
  .tile-position-0-2 { top: 0px; left: 140px; }
  .tile-position-0-3 { top: 0px; left: 210px; }
  .tile-position-1-0 { top: 70px; left: 0px; }
  .tile-position-1-1 { top: 70px; left: 70px; }
  .tile-position-1-2 { top: 70px; left: 140px; }
  .tile-position-1-3 { top: 70px; left: 210px; }
  .tile-position-2-0 { top: 140px; left: 0px; }
  .tile-position-2-1 { top: 140px; left: 70px; }
  .tile-position-2-2 { top: 140px; left: 140px; }
  .tile-position-2-3 { top: 140px; left: 210px; }
  .tile-position-3-0 { top: 210px; left: 0px; }
  .tile-position-3-1 { top: 210px; left: 70px; }
  .tile-position-3-2 { top: 210px; left: 140px; }
  .tile-position-3-3 { top: 210px; left: 210px; }

  /* 小屏幕游戏结束弹窗 */
  .game-over-overlay {
    padding: 10px;
  }

  .game-over-message {
    padding: 15px;
    max-width: 98vw;
    max-height: 90vh;
  }

  .game-over-message h2 {
    font-size: 18px;
    margin-bottom: 8px;
  }

  .game-over-message p {
    font-size: 14px;
    margin-bottom: 15px;
  }

  .game-over-actions {
    gap: 12px;
  }

  .submit-score-btn, .restart-btn {
    padding: 12px 15px;
    font-size: 14px;
    min-height: 44px;
  }

  .submission-actions {
    gap: 10px;
  }

  .submit-btn, .cancel-btn {
    padding: 12px 15px;
    font-size: 14px;
    min-height: 44px;
  }

  .player-name-input {
    padding: 12px;
    font-size: 14px;
    min-height: 44px;
  }
}

/* 处理高度受限的屏幕 */
@media (max-height: 600px) {
  .game-over-overlay {
    align-items: flex-start;
    padding: 10px;
    padding-top: 20px;
  }

  .game-over-message {
    padding: 15px;
    max-height: calc(100vh - 40px);
    max-width: 90vw;
  }

  .game-over-message h2 {
    font-size: 18px;
    margin-bottom: 5px;
  }

  .game-over-message p {
    font-size: 14px;
    margin-bottom: 10px;
  }

  .game-over-actions {
    gap: 8px;
  }

  .submit-score-btn, .restart-btn {
    padding: 10px 15px;
    font-size: 14px;
    min-height: 40px;
  }

  .submission-actions {
    gap: 8px;
  }

  .submit-btn, .cancel-btn {
    padding: 10px 15px;
    font-size: 14px;
    min-height: 40px;
  }

  .player-name-input {
    padding: 10px;
    font-size: 14px;
    min-height: 40px;
  }
}

/* 处理非常小的屏幕 */
@media (max-width: 320px) {
  .game-container {
    max-width: 260px;
    padding: 5px;
  }

  .game-over-overlay {
    padding: 5px;
  }

  .game-over-message {
    padding: 12px;
    max-width: 99vw;
    max-height: 95vh;
  }

  .game-over-message h2 {
    font-size: 16px;
    margin-bottom: 6px;
  }

  .game-over-message p {
    font-size: 13px;
    margin-bottom: 12px;
  }

  .game-over-actions {
    gap: 10px;
  }

  .submit-score-btn, .restart-btn {
    padding: 10px 12px;
    font-size: 13px;
    min-height: 42px;
  }

  .submission-actions {
    gap: 8px;
  }

  .submit-btn, .cancel-btn {
    padding: 10px 12px;
    font-size: 13px;
    min-height: 42px;
  }

  .player-name-input {
    padding: 10px;
    font-size: 13px;
    min-height: 42px;
  }
}

/* 针对触摸设备的优化 */
@media (hover: none) and (pointer: coarse) {
  .game-over-overlay {
    position: fixed;
    z-index: 1001;
  }

  .submit-score-btn, .restart-btn, .submit-btn, .cancel-btn {
    min-height: 48px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  .submit-score-btn:active, .restart-btn:active,
  .submit-btn:active, .cancel-btn:active {
    transform: scale(0.98);
  }

  .player-name-input {
    min-height: 48px;
    touch-action: manipulation;
  }
}
</style>
