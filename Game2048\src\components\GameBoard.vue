<template>
  <div class="game-container">
    <div class="score-container">
      <div class="score-box">
        <div class="score-label">分数</div>
        <div class="score-value">{{ score }}</div>
      </div>
      <div class="score-box">
        <div class="score-label">最高分</div>
        <div class="score-value">{{ bestScore }}</div>
      </div>
    </div>
    
    <div class="game-board"
         @touchstart="handleTouchStart"
         @touchend="handleTouchEnd">
      <div class="grid-container">
        <div class="grid-row" v-for="row in 4" :key="row">
          <div class="grid-cell" v-for="col in 4" :key="col"></div>
        </div>
      </div>
      
      <TransitionGroup name="tile" tag="div" class="tile-container">
        <div
          v-for="tile in tiles"
          :key="tile.id"
          :class="['tile', `tile-${tile.value}`, `tile-position-${tile.row}-${tile.col}`]"
        >
          {{ tile.value }}
        </div>
      </TransitionGroup>
    </div>
    
    <div class="game-controls">
      <button @click="restartGame" class="restart-btn">新游戏</button>
      <p class="auto-save-hint">游戏进度自动保存</p>
    </div>
    
    <div v-if="isGameOver" class="game-over-overlay">
      <div class="game-over-message">
        <h2>游戏结束!</h2>
        <p>最终分数: {{ score }}</p>

        <div v-if="!showScoreSubmission" class="game-over-actions">
          <button @click="showScoreSubmission = true" class="submit-score-btn">提交分数</button>
          <button @click="restartGame" class="restart-btn">再来一局</button>
        </div>

        <div v-else class="score-submission">
          <input
            v-model="playerName"
            type="text"
            placeholder="输入你的昵称"
            maxlength="20"
            class="player-name-input"
            @keyup.enter="submitScore"
          />
          <div class="submission-actions">
            <button @click="submitScore" :disabled="submitting || !playerName.trim()" class="submit-btn">
              {{ submitting ? '提交中...' : '提交' }}
            </button>
            <button @click="showScoreSubmission = false" class="cancel-btn">取消</button>
          </div>
          <div v-if="submissionError" class="submission-error">{{ submissionError }}</div>
          <div v-if="submissionSuccess" class="submission-success">分数提交成功！</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

interface Tile {
  id: number
  value: number
  row: number
  col: number
}

// 响应式数据
const board = ref<number[][]>([])
const tiles = ref<Tile[]>([])
const score = ref(0)
const bestScore = ref(0)
const isGameOver = ref(false)
let nextTileId = 1

// 触摸事件相关
let touchStartX = 0
let touchStartY = 0

// 分数提交相关
const showScoreSubmission = ref(false)
const playerName = ref('')
const submitting = ref(false)
const submissionError = ref('')
const submissionSuccess = ref(false)

const API_BASE_URL = 'http://localhost:3001/api'

// 初始化游戏
const initGame = (forceNew = false) => {
  // 加载最高分
  loadBestScore()

  // 尝试加载保存的游戏状态
  if (!forceNew && loadGameState()) {
    return
  }

  // 创建新游戏
  board.value = Array(4).fill(null).map(() => Array(4).fill(0))
  tiles.value = []
  score.value = 0
  isGameOver.value = false
  nextTileId = 1

  // 添加两个初始方块
  addRandomTile()
  addRandomTile()

  // 保存初始状态
  saveGameState()
}

// 添加随机方块
const addRandomTile = () => {
  const emptyCells = []
  for (let row = 0; row < 4; row++) {
    for (let col = 0; col < 4; col++) {
      if (board.value[row][col] === 0) {
        emptyCells.push({ row, col })
      }
    }
  }
  
  if (emptyCells.length > 0) {
    const randomCell = emptyCells[Math.floor(Math.random() * emptyCells.length)]
    const value = Math.random() < 0.9 ? 2 : 4
    
    board.value[randomCell.row][randomCell.col] = value
    tiles.value.push({
      id: nextTileId++,
      value,
      row: randomCell.row,
      col: randomCell.col
    })
  }
}

// 移动逻辑 - 向左移动
const moveLeft = (): boolean => {
  let moved = false
  const newBoard = board.value.map(row => [...row])
  
  for (let row = 0; row < 4; row++) {
    const line = newBoard[row].filter(val => val !== 0)
    const merged = []
    let i = 0
    
    while (i < line.length) {
      if (i < line.length - 1 && line[i] === line[i + 1]) {
        // 合并相同的方块
        merged.push(line[i] * 2)
        score.value += line[i] * 2
        i += 2
      } else {
        merged.push(line[i])
        i++
      }
    }
    
    // 填充剩余位置为0
    while (merged.length < 4) {
      merged.push(0)
    }
    
    // 检查是否有移动
    for (let col = 0; col < 4; col++) {
      if (newBoard[row][col] !== merged[col]) {
        moved = true
      }
    }
    
    newBoard[row] = merged
  }
  
  if (moved) {
    board.value = newBoard
    updateTiles()
    return true
  }
  
  return false
}

// 旋转棋盘（用于实现其他方向的移动）
const rotateBoard = (board: number[][]): number[][] => {
  const size = board.length
  const rotated = Array(size).fill(null).map(() => Array(size).fill(0))
  
  for (let row = 0; row < size; row++) {
    for (let col = 0; col < size; col++) {
      rotated[col][size - 1 - row] = board[row][col]
    }
  }
  
  return rotated
}

// 移动方向处理
const move = (direction: string): boolean => {
  let moved = false
  let rotations = 0
  
  // 根据方向确定需要旋转的次数
  switch (direction) {
    case 'left':
      rotations = 0
      break
    case 'up':
      rotations = 1
      break
    case 'right':
      rotations = 2
      break
    case 'down':
      rotations = 3
      break
  }
  
  // 旋转棋盘到左移位置
  let currentBoard = board.value
  for (let i = 0; i < rotations; i++) {
    currentBoard = rotateBoard(currentBoard)
  }
  
  board.value = currentBoard
  moved = moveLeft()
  
  // 旋转回原来的方向
  for (let i = 0; i < (4 - rotations) % 4; i++) {
    board.value = rotateBoard(board.value)
  }
  
  return moved
}

// 更新方块显示
const updateTiles = () => {
  const newTiles: Tile[] = []

  for (let row = 0; row < 4; row++) {
    for (let col = 0; col < 4; col++) {
      if (board.value[row][col] !== 0) {
        newTiles.push({
          id: nextTileId++,
          value: board.value[row][col],
          row,
          col
        })
      }
    }
  }

  tiles.value = newTiles
}

// 检查游戏是否结束
const checkGameOver = (): boolean => {
  // 检查是否还有空格
  for (let row = 0; row < 4; row++) {
    for (let col = 0; col < 4; col++) {
      if (board.value[row][col] === 0) {
        return false
      }
    }
  }

  // 检查是否还能合并
  for (let row = 0; row < 4; row++) {
    for (let col = 0; col < 4; col++) {
      const current = board.value[row][col]

      // 检查右边
      if (col < 3 && board.value[row][col + 1] === current) {
        return false
      }

      // 检查下面
      if (row < 3 && board.value[row + 1][col] === current) {
        return false
      }
    }
  }

  return true
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (isGameOver.value) return

  let moved = false

  switch (event.key) {
    case 'ArrowLeft':
      event.preventDefault()
      moved = move('left')
      break
    case 'ArrowUp':
      event.preventDefault()
      moved = move('up')
      break
    case 'ArrowRight':
      event.preventDefault()
      moved = move('right')
      break
    case 'ArrowDown':
      event.preventDefault()
      moved = move('down')
      break
  }

  if (moved) {
    addRandomTile()
    updateBestScore()
    saveGameState()

    if (checkGameOver()) {
      isGameOver.value = true
      saveGameState()
    }
  }
}

// 触摸事件处理
const handleTouchStart = (event: TouchEvent) => {
  if (event.touches.length === 1) {
    touchStartX = event.touches[0].clientX
    touchStartY = event.touches[0].clientY
  }
}

const handleTouchEnd = (event: TouchEvent) => {
  if (isGameOver.value || event.changedTouches.length !== 1) return

  const touchEndX = event.changedTouches[0].clientX
  const touchEndY = event.changedTouches[0].clientY

  const deltaX = touchEndX - touchStartX
  const deltaY = touchEndY - touchStartY

  const minSwipeDistance = 30

  if (Math.abs(deltaX) < minSwipeDistance && Math.abs(deltaY) < minSwipeDistance) {
    return
  }

  let moved = false

  if (Math.abs(deltaX) > Math.abs(deltaY)) {
    // 水平滑动
    if (deltaX > 0) {
      moved = move('right')
    } else {
      moved = move('left')
    }
  } else {
    // 垂直滑动
    if (deltaY > 0) {
      moved = move('down')
    } else {
      moved = move('up')
    }
  }

  if (moved) {
    addRandomTile()
    updateBestScore()
    saveGameState()

    if (checkGameOver()) {
      isGameOver.value = true
      saveGameState()
    }
  }
}

// 生成验证载荷
const generateValidationPayload = (score: number, board: number[][]) => {
  const data = `${score}-${JSON.stringify(board)}-${Date.now()}`
  // 简单的哈希函数（在实际应用中应该使用更安全的方法）
  let hash = 0
  for (let i = 0; i < data.length; i++) {
    const char = data.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  return Math.abs(hash).toString(16)
}

// 提交分数
const submitScore = async () => {
  if (!playerName.value.trim()) {
    submissionError.value = '请输入昵称'
    return
  }

  submitting.value = true
  submissionError.value = ''
  submissionSuccess.value = false

  try {
    const validationPayload = generateValidationPayload(score.value, board.value)

    const response = await fetch(`${API_BASE_URL}/scores`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        playerName: playerName.value.trim(),
        score: score.value,
        validationPayload
      })
    })

    const data = await response.json()

    if (!response.ok) {
      throw new Error(data.error || '提交失败')
    }

    submissionSuccess.value = true
    setTimeout(() => {
      showScoreSubmission.value = false
      submissionSuccess.value = false
    }, 2000)

  } catch (error) {
    submissionError.value = error instanceof Error ? error.message : '网络错误'
  } finally {
    submitting.value = false
  }
}

// 重新开始游戏
const restartGame = () => {
  clearGameState()
  showScoreSubmission.value = false
  playerName.value = ''
  submissionError.value = ''
  submissionSuccess.value = false
  initGame(true)
}

// 保存和加载游戏状态
const saveGameState = () => {
  const gameState = {
    board: board.value,
    tiles: tiles.value,
    score: score.value,
    isGameOver: isGameOver.value,
    nextTileId
  }
  localStorage.setItem('game2048-state', JSON.stringify(gameState))
}

const loadGameState = (): boolean => {
  const saved = localStorage.getItem('game2048-state')
  if (saved) {
    try {
      const gameState = JSON.parse(saved)
      board.value = gameState.board
      tiles.value = gameState.tiles
      score.value = gameState.score
      isGameOver.value = gameState.isGameOver
      nextTileId = gameState.nextTileId
      return true
    } catch (e) {
      console.error('Failed to load game state:', e)
      return false
    }
  }
  return false
}

const clearGameState = () => {
  localStorage.removeItem('game2048-state')
}

// 保存和加载最高分
const saveBestScore = () => {
  localStorage.setItem('game2048-best-score', bestScore.value.toString())
}

const loadBestScore = () => {
  const saved = localStorage.getItem('game2048-best-score')
  if (saved) {
    bestScore.value = parseInt(saved, 10)
  }
}

const updateBestScore = () => {
  if (score.value > bestScore.value) {
    bestScore.value = score.value
    saveBestScore()
  }
}

// 生命周期钩子
onMounted(() => {
  initGame()
  window.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.game-container {
  max-width: 500px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Arial', sans-serif;
  min-height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.score-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.score-box {
  background: #bbada0;
  padding: 10px 20px;
  border-radius: 6px;
  color: white;
  text-align: center;
  min-width: 80px;
}

.score-label {
  font-size: 13px;
  font-weight: bold;
  text-transform: uppercase;
  margin-bottom: 5px;
}

.score-value {
  font-size: 24px;
  font-weight: bold;
}

.game-board {
  position: relative;
  background: #bbada0;
  border-radius: 10px;
  padding: 10px;
  margin-bottom: 20px;
}

.grid-container {
  position: relative;
  z-index: 1;
}

.grid-row {
  display: flex;
  margin-bottom: 10px;
}

.grid-row:last-child {
  margin-bottom: 0;
}

.grid-cell {
  width: 100px;
  height: 100px;
  background: rgba(238, 228, 218, 0.35);
  border-radius: 6px;
  margin-right: 10px;
}

.grid-cell:last-child {
  margin-right: 0;
}

.tile-container {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 2;
}

.tile {
  position: absolute;
  width: 100px;
  height: 100px;
  border-radius: 6px;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32px;
  transition: all 0.15s ease-in-out;
}

/* 方块位置 */
.tile-position-0-0 { top: 0px; left: 0px; }
.tile-position-0-1 { top: 0px; left: 110px; }
.tile-position-0-2 { top: 0px; left: 220px; }
.tile-position-0-3 { top: 0px; left: 330px; }
.tile-position-1-0 { top: 110px; left: 0px; }
.tile-position-1-1 { top: 110px; left: 110px; }
.tile-position-1-2 { top: 110px; left: 220px; }
.tile-position-1-3 { top: 110px; left: 330px; }
.tile-position-2-0 { top: 220px; left: 0px; }
.tile-position-2-1 { top: 220px; left: 110px; }
.tile-position-2-2 { top: 220px; left: 220px; }
.tile-position-2-3 { top: 220px; left: 330px; }
.tile-position-3-0 { top: 330px; left: 0px; }
.tile-position-3-1 { top: 330px; left: 110px; }
.tile-position-3-2 { top: 330px; left: 220px; }
.tile-position-3-3 { top: 330px; left: 330px; }

/* 方块颜色 */
.tile-2 { background: #eee4da; color: #776e65; }
.tile-4 { background: #ede0c8; color: #776e65; }
.tile-8 { background: #f2b179; color: #f9f6f2; }
.tile-16 { background: #f59563; color: #f9f6f2; }
.tile-32 { background: #f67c5f; color: #f9f6f2; }
.tile-64 { background: #f65e3b; color: #f9f6f2; }
.tile-128 { background: #edcf72; color: #f9f6f2; font-size: 28px; }
.tile-256 { background: #edcc61; color: #f9f6f2; font-size: 28px; }
.tile-512 { background: #edc850; color: #f9f6f2; font-size: 28px; }
.tile-1024 { background: #edc53f; color: #f9f6f2; font-size: 24px; }
.tile-2048 { background: #edc22e; color: #f9f6f2; font-size: 24px; }

/* 动画 */
.tile-enter-active {
  animation: tile-appear 0.2s ease-in-out;
}

@keyframes tile-appear {
  0% {
    opacity: 0;
    transform: scale(0);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.game-controls {
  text-align: center;
}

.restart-btn {
  background: #8f7a66;
  color: #f9f6f2;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.2s;
}

.restart-btn:hover {
  background: #9f8a76;
}

.auto-save-hint {
  color: #776e65;
  font-size: 14px;
  margin-top: 10px;
  text-align: center;
}

.game-over-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
  box-sizing: border-box;
}

.game-over-message {
  text-align: center;
  background: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-sizing: border-box;
  position: relative;
}

.game-over-message h2 {
  color: #776e65;
  margin-bottom: 10px;
}

.game-over-message p {
  color: #776e65;
  margin-bottom: 20px;
}

.game-over-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.submit-score-btn {
  background: #f67c5f;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.2s;
}

.submit-score-btn:hover {
  background: #f65e3b;
}

.score-submission {
  width: 100%;
}

.player-name-input {
  width: 100%;
  padding: 12px;
  border: 2px solid #bbada0;
  border-radius: 6px;
  font-size: 16px;
  margin-bottom: 15px;
  text-align: center;
  outline: none;
}

.player-name-input:focus {
  border-color: #8f7a66;
}

.submission-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-bottom: 10px;
}

.submit-btn {
  background: #8f7a66;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.2s;
}

.submit-btn:hover:not(:disabled) {
  background: #9f8a76;
}

.submit-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.cancel-btn {
  background: #bbada0;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.2s;
}

.cancel-btn:hover {
  background: #a09890;
}

.submission-error {
  color: #f67c5f;
  text-align: center;
  font-size: 14px;
  margin-top: 10px;
}

.submission-success {
  color: #4caf50;
  text-align: center;
  font-size: 14px;
  margin-top: 10px;
}

/* 响应式设计 */
@media (max-width: 600px) {
  .game-container {
    max-width: 320px;
    padding: 10px;
    min-height: auto;
    justify-content: flex-start;
  }

  .grid-cell, .tile {
    width: 70px;
    height: 70px;
    font-size: 24px;
  }

  .tile-128, .tile-256, .tile-512 {
    font-size: 20px;
  }

  .tile-1024, .tile-2048 {
    font-size: 18px;
  }

  /* 重新定义移动端方块位置 */
  .tile-position-0-0 { top: 0px; left: 0px; }
  .tile-position-0-1 { top: 0px; left: 80px; }
  .tile-position-0-2 { top: 0px; left: 160px; }
  .tile-position-0-3 { top: 0px; left: 240px; }
  .tile-position-1-0 { top: 80px; left: 0px; }
  .tile-position-1-1 { top: 80px; left: 80px; }
  .tile-position-1-2 { top: 80px; left: 160px; }
  .tile-position-1-3 { top: 80px; left: 240px; }
  .tile-position-2-0 { top: 160px; left: 0px; }
  .tile-position-2-1 { top: 160px; left: 80px; }
  .tile-position-2-2 { top: 160px; left: 160px; }
  .tile-position-2-3 { top: 160px; left: 240px; }
  .tile-position-3-0 { top: 240px; left: 0px; }
  .tile-position-3-1 { top: 240px; left: 80px; }
  .tile-position-3-2 { top: 240px; left: 160px; }
  .tile-position-3-3 { top: 240px; left: 240px; }

  .grid-cell {
    margin-right: 10px;
  }

  .score-box {
    padding: 8px 15px;
    min-width: 60px;
  }

  .score-label {
    font-size: 11px;
  }

  .score-value {
    font-size: 20px;
  }

  /* 游戏结束弹窗响应式 */
  .game-over-overlay {
    padding: 15px;
  }

  .game-over-message {
    padding: 20px;
    max-width: 95vw;
    max-height: 85vh;
  }

  .game-over-message h2 {
    font-size: 22px;
    margin-bottom: 10px;
  }

  .game-over-message p {
    font-size: 16px;
    margin-bottom: 20px;
  }

  .game-over-actions {
    flex-direction: column;
    gap: 15px;
  }

  .submit-score-btn, .restart-btn {
    width: 100%;
    padding: 14px 20px;
    font-size: 16px;
    min-height: 48px;
  }

  .submission-actions {
    flex-direction: column;
    gap: 12px;
  }

  .submit-btn, .cancel-btn {
    width: 100%;
    padding: 14px 20px;
    font-size: 16px;
    min-height: 48px;
  }

  .player-name-input {
    padding: 14px;
    font-size: 16px;
    min-height: 48px;
  }
}

@media (max-width: 400px) {
  .game-container {
    max-width: 280px;
    padding: 8px;
  }

  .grid-cell, .tile {
    width: 60px;
    height: 60px;
    font-size: 20px;
  }

  .tile-128, .tile-256, .tile-512 {
    font-size: 18px;
  }

  .tile-1024, .tile-2048 {
    font-size: 16px;
  }

  /* 小屏幕方块位置 */
  .tile-position-0-0 { top: 0px; left: 0px; }
  .tile-position-0-1 { top: 0px; left: 70px; }
  .tile-position-0-2 { top: 0px; left: 140px; }
  .tile-position-0-3 { top: 0px; left: 210px; }
  .tile-position-1-0 { top: 70px; left: 0px; }
  .tile-position-1-1 { top: 70px; left: 70px; }
  .tile-position-1-2 { top: 70px; left: 140px; }
  .tile-position-1-3 { top: 70px; left: 210px; }
  .tile-position-2-0 { top: 140px; left: 0px; }
  .tile-position-2-1 { top: 140px; left: 70px; }
  .tile-position-2-2 { top: 140px; left: 140px; }
  .tile-position-2-3 { top: 140px; left: 210px; }
  .tile-position-3-0 { top: 210px; left: 0px; }
  .tile-position-3-1 { top: 210px; left: 70px; }
  .tile-position-3-2 { top: 210px; left: 140px; }
  .tile-position-3-3 { top: 210px; left: 210px; }

  /* 小屏幕游戏结束弹窗 */
  .game-over-overlay {
    padding: 10px;
  }

  .game-over-message {
    padding: 15px;
    max-width: 98vw;
    max-height: 90vh;
  }

  .game-over-message h2 {
    font-size: 18px;
    margin-bottom: 8px;
  }

  .game-over-message p {
    font-size: 14px;
    margin-bottom: 15px;
  }

  .game-over-actions {
    gap: 12px;
  }

  .submit-score-btn, .restart-btn {
    padding: 12px 15px;
    font-size: 14px;
    min-height: 44px;
  }

  .submission-actions {
    gap: 10px;
  }

  .submit-btn, .cancel-btn {
    padding: 12px 15px;
    font-size: 14px;
    min-height: 44px;
  }

  .player-name-input {
    padding: 12px;
    font-size: 14px;
    min-height: 44px;
  }
}

/* 处理高度受限的屏幕 */
@media (max-height: 600px) {
  .game-over-overlay {
    align-items: flex-start;
    padding: 10px;
    padding-top: 20px;
  }

  .game-over-message {
    padding: 15px;
    max-height: calc(100vh - 40px);
    max-width: 90vw;
  }

  .game-over-message h2 {
    font-size: 18px;
    margin-bottom: 5px;
  }

  .game-over-message p {
    font-size: 14px;
    margin-bottom: 10px;
  }

  .game-over-actions {
    gap: 8px;
  }

  .submit-score-btn, .restart-btn {
    padding: 10px 15px;
    font-size: 14px;
    min-height: 40px;
  }

  .submission-actions {
    gap: 8px;
  }

  .submit-btn, .cancel-btn {
    padding: 10px 15px;
    font-size: 14px;
    min-height: 40px;
  }

  .player-name-input {
    padding: 10px;
    font-size: 14px;
    min-height: 40px;
  }
}

/* 处理非常小的屏幕 */
@media (max-width: 320px) {
  .game-container {
    max-width: 260px;
    padding: 5px;
  }

  .game-over-overlay {
    padding: 5px;
  }

  .game-over-message {
    padding: 12px;
    max-width: 99vw;
    max-height: 95vh;
  }

  .game-over-message h2 {
    font-size: 16px;
    margin-bottom: 6px;
  }

  .game-over-message p {
    font-size: 13px;
    margin-bottom: 12px;
  }

  .game-over-actions {
    gap: 10px;
  }

  .submit-score-btn, .restart-btn {
    padding: 10px 12px;
    font-size: 13px;
    min-height: 42px;
  }

  .submission-actions {
    gap: 8px;
  }

  .submit-btn, .cancel-btn {
    padding: 10px 12px;
    font-size: 13px;
    min-height: 42px;
  }

  .player-name-input {
    padding: 10px;
    font-size: 13px;
    min-height: 42px;
  }
}

/* 针对触摸设备的优化 */
@media (hover: none) and (pointer: coarse) {
  .game-over-overlay {
    position: fixed;
    z-index: 1001;
  }

  .submit-score-btn, .restart-btn, .submit-btn, .cancel-btn {
    min-height: 48px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  .submit-score-btn:active, .restart-btn:active,
  .submit-btn:active, .cancel-btn:active {
    transform: scale(0.98);
  }

  .player-name-input {
    min-height: 48px;
    touch-action: manipulation;
  }
}
</style>
