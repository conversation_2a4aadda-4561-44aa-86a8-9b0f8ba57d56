{"name": "is-plain-obj", "version": "4.1.0", "description": "Check if a value is a plain object", "license": "MIT", "repository": "sindresorhus/is-plain-obj", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["object", "is", "check", "test", "type", "plain", "vanilla", "pure", "simple"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}}