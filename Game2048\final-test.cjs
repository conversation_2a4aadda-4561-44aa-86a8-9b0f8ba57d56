/**
 * 最终部署测试脚本
 */

const fs = require('fs');
const path = require('path');

console.log('🎯 开始最终部署测试...\n');

let hasErrors = false;
let warnings = 0;

// 测试1: 检查构建产物
console.log('📦 检查构建产物...');
if (fs.existsSync('dist')) {
  const distFiles = fs.readdirSync('dist');
  const requiredFiles = ['index.html', 'assets'];
  
  requiredFiles.forEach(file => {
    if (distFiles.includes(file)) {
      console.log(`✅ ${file}`);
    } else {
      console.log(`❌ ${file} - 构建产物缺失`);
      hasErrors = true;
    }
  });
  
  // 检查资源文件
  if (fs.existsSync('dist/assets')) {
    const assets = fs.readdirSync('dist/assets');
    const hasJS = assets.some(file => file.endsWith('.js'));
    const hasCSS = assets.some(file => file.endsWith('.css'));
    
    if (hasJS) {
      console.log('✅ JavaScript 文件');
    } else {
      console.log('❌ JavaScript 文件缺失');
      hasErrors = true;
    }
    
    if (hasCSS) {
      console.log('✅ CSS 文件');
    } else {
      console.log('❌ CSS 文件缺失');
      hasErrors = true;
    }
  }
} else {
  console.log('❌ dist 目录不存在 - 请运行 npm run build');
  hasErrors = true;
}

// 测试2: 检查环境配置
console.log('\n🔧 检查环境配置...');
const envFiles = [
  { file: '.env.example', required: false },
  { file: 'backend/.env.example', required: false },
  { file: 'vercel.json', required: false },
  { file: 'netlify.toml', required: false },
  { file: 'backend/Dockerfile', required: false }
];

envFiles.forEach(({ file, required }) => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else if (required) {
    console.log(`❌ ${file} - 必需文件缺失`);
    hasErrors = true;
  } else {
    console.log(`⚠️  ${file} - 建议文件缺失`);
    warnings++;
  }
});

// 测试3: 检查分析集成
console.log('\n📊 检查分析集成...');
const analyticsFile = 'src/utils/analytics.ts';
if (fs.existsSync(analyticsFile)) {
  console.log('✅ 分析工具文件存在');
  
  const analyticsContent = fs.readFileSync(analyticsFile, 'utf8');
  const requiredFunctions = [
    'trackGameStart',
    'trackGameEnd',
    'trackMove',
    'trackScoreSubmission',
    'trackLeaderboardView'
  ];
  
  requiredFunctions.forEach(func => {
    if (analyticsContent.includes(func)) {
      console.log(`✅ ${func} 函数`);
    } else {
      console.log(`❌ ${func} 函数缺失`);
      hasErrors = true;
    }
  });
} else {
  console.log('❌ 分析工具文件缺失');
  hasErrors = true;
}

// 测试4: 检查部署文档
console.log('\n📚 检查部署文档...');
const deploymentDoc = 'DEPLOYMENT.md';
if (fs.existsSync(deploymentDoc)) {
  console.log('✅ 部署文档存在');
  
  const docContent = fs.readFileSync(deploymentDoc, 'utf8');
  const requiredSections = [
    '部署前准备',
    'Vercel',
    'Netlify',
    'Railway',
    'Render',
    '环境变量'
  ];
  
  requiredSections.forEach(section => {
    if (docContent.includes(section)) {
      console.log(`✅ ${section} 章节`);
    } else {
      console.log(`⚠️  ${section} 章节缺失`);
      warnings++;
    }
  });
} else {
  console.log('⚠️  部署文档缺失');
  warnings++;
}

// 测试5: 检查测试文件
console.log('\n🧪 检查测试覆盖...');
const testFiles = [
  'tests/game-logic.test.js',
  'tests/api.test.js',
  'tests/stress.test.js'
];

testFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`⚠️  ${file} - 测试文件缺失`);
    warnings++;
  }
});

// 测试6: 检查安全配置
console.log('\n🔒 检查安全配置...');
try {
  const backendContent = fs.readFileSync('backend/server.js', 'utf8');
  const securityFeatures = [
    { feature: 'helmet', description: 'HTTP 安全头' },
    { feature: 'cors', description: 'CORS 配置' },
    { feature: 'rateLimit', description: '速率限制' },
    { feature: 'express.json', description: '请求体大小限制' }
  ];
  
  securityFeatures.forEach(({ feature, description }) => {
    if (backendContent.includes(feature)) {
      console.log(`✅ ${description}`);
    } else {
      console.log(`⚠️  ${description} - 安全特性缺失`);
      warnings++;
    }
  });
} catch (error) {
  console.log('❌ 无法检查后端安全配置');
  hasErrors = true;
}

// 测试7: 性能检查
console.log('\n⚡ 性能检查...');
if (fs.existsSync('dist')) {
  try {
    const indexHtml = fs.readFileSync('dist/index.html', 'utf8');
    
    // 检查是否有预加载
    if (indexHtml.includes('preload')) {
      console.log('✅ 资源预加载');
    } else {
      console.log('⚠️  缺少资源预加载优化');
      warnings++;
    }
    
    // 检查是否压缩
    if (indexHtml.length < 1000) {
      console.log('✅ HTML 已压缩');
    } else {
      console.log('⚠️  HTML 可能未压缩');
      warnings++;
    }
  } catch (error) {
    console.log('⚠️  无法检查性能优化');
    warnings++;
  }
}

// 总结报告
console.log('\n📋 最终测试报告:');
console.log('='.repeat(50));

if (hasErrors) {
  console.log('❌ 发现严重问题，需要修复后才能部署');
  console.log(`   错误数量: ${hasErrors ? '有' : '无'}`);
} else {
  console.log('✅ 所有关键检查通过，可以部署！');
}

if (warnings > 0) {
  console.log(`⚠️  发现 ${warnings} 个警告，建议优化`);
}

console.log('\n🚀 部署建议:');
console.log('1. 确保所有测试通过');
console.log('2. 设置正确的环境变量');
console.log('3. 选择合适的托管服务');
console.log('4. 配置域名和HTTPS');
console.log('5. 设置监控和日志');
console.log('6. 准备回滚计划');

console.log('\n🎉 准备就绪！您的2048游戏即将与世界见面！');

process.exit(hasErrors ? 1 : 0);
