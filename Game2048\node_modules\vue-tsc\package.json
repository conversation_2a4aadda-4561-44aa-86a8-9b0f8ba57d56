{"name": "vue-tsc", "version": "2.2.12", "license": "MIT", "files": ["bin", "**/*.js", "**/*.d.ts"], "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/tsc"}, "bin": {"vue-tsc": "./bin/vue-tsc.js"}, "peerDependencies": {"typescript": ">=5.0.0"}, "dependencies": {"@volar/typescript": "2.4.15", "@vue/language-core": "2.2.12"}, "devDependencies": {"@types/node": "^22.10.4"}, "gitHead": "0b13bf1966398ea3949b6b02d09b251ddc9a51eb"}