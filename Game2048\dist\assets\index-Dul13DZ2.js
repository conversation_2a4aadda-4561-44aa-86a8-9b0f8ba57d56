const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/AboutView-C0meWy0P.js","assets/AboutView-CSIvawM9.css"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(r){if(r.ep)return;r.ep=!0;const o=n(r);fetch(r.href,o)}})();/**
* @vue/shared v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function $s(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const de={},Ht=[],et=()=>{},Pi=()=>!1,Fn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Is=e=>e.startsWith("onUpdate:"),we=Object.assign,ks=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Mi=Object.prototype.hasOwnProperty,ie=(e,t)=>Mi.call(e,t),B=Array.isArray,Dt=e=>jn(e)==="[object Map]",to=e=>jn(e)==="[object Set]",W=e=>typeof e=="function",ve=e=>typeof e=="string",ut=e=>typeof e=="symbol",ge=e=>e!==null&&typeof e=="object",no=e=>(ge(e)||W(e))&&W(e.then)&&W(e.catch),so=Object.prototype.toString,jn=e=>so.call(e),Oi=e=>jn(e).slice(8,-1),ro=e=>jn(e)==="[object Object]",Ls=e=>ve(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,tn=$s(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Vn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},$i=/-(\w)/g,bt=Vn(e=>e.replace($i,(t,n)=>n?n.toUpperCase():"")),Ii=/\B([A-Z])/g,Et=Vn(e=>e.replace(Ii,"-$1").toLowerCase()),oo=Vn(e=>e.charAt(0).toUpperCase()+e.slice(1)),Zn=Vn(e=>e?`on${oo(e)}`:""),_t=(e,t)=>!Object.is(e,t),Tn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},gs=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},ms=e=>{const t=parseFloat(e);return isNaN(t)?e:t},ki=e=>{const t=ve(e)?Number(e):NaN;return isNaN(t)?e:t};let er;const Bn=()=>er||(er=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function zs(e){if(B(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=ve(s)?Hi(s):zs(s);if(r)for(const o in r)t[o]=r[o]}return t}else if(ve(e)||ge(e))return e}const Li=/;(?![^(]*\))/g,zi=/:([^]+)/,Ni=/\/\*[^]*?\*\//g;function Hi(e){const t={};return e.replace(Ni,"").split(Li).forEach(n=>{if(n){const s=n.split(zi);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function wt(e){let t="";if(ve(e))t=e;else if(B(e))for(let n=0;n<e.length;n++){const s=wt(e[n]);s&&(t+=s+" ")}else if(ge(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Di="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Fi=$s(Di);function io(e){return!!e||e===""}const lo=e=>!!(e&&e.__v_isRef===!0),Ee=e=>ve(e)?e:e==null?"":B(e)||ge(e)&&(e.toString===so||!W(e.toString))?lo(e)?Ee(e.value):JSON.stringify(e,co,2):String(e),co=(e,t)=>lo(t)?co(e,t.value):Dt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],o)=>(n[es(s,o)+" =>"]=r,n),{})}:to(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>es(n))}:ut(t)?es(t):ge(t)&&!B(t)&&!ro(t)?String(t):t,es=(e,t="")=>{var n;return ut(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ze;class ao{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=ze,!t&&ze&&(this.index=(ze.scopes||(ze.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=ze;try{return ze=this,t()}finally{ze=n}}}on(){++this._on===1&&(this.prevScope=ze,ze=this)}off(){this._on>0&&--this._on===0&&(ze=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function ji(e){return new ao(e)}function Vi(){return ze}let pe;const ts=new WeakSet;class uo{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,ze&&ze.active&&ze.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,ts.has(this)&&(ts.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||ho(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,tr(this),po(this);const t=pe,n=Ue;pe=this,Ue=!0;try{return this.fn()}finally{go(this),pe=t,Ue=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Ds(t);this.deps=this.depsTail=void 0,tr(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?ts.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){vs(this)&&this.run()}get dirty(){return vs(this)}}let fo=0,nn,sn;function ho(e,t=!1){if(e.flags|=8,t){e.next=sn,sn=e;return}e.next=nn,nn=e}function Ns(){fo++}function Hs(){if(--fo>0)return;if(sn){let t=sn;for(sn=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;nn;){let t=nn;for(nn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function po(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function go(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),Ds(s),Bi(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function vs(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(mo(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function mo(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===fn)||(e.globalVersion=fn,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!vs(e))))return;e.flags|=2;const t=e.dep,n=pe,s=Ue;pe=e,Ue=!0;try{po(e);const r=e.fn(e._value);(t.version===0||_t(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{pe=n,Ue=s,go(e),e.flags&=-3}}function Ds(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)Ds(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Bi(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Ue=!0;const vo=[];function ct(){vo.push(Ue),Ue=!1}function at(){const e=vo.pop();Ue=e===void 0?!0:e}function tr(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=pe;pe=void 0;try{t()}finally{pe=n}}}let fn=0;class Ui{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Fs{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!pe||!Ue||pe===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==pe)n=this.activeLink=new Ui(pe,this),pe.deps?(n.prevDep=pe.depsTail,pe.depsTail.nextDep=n,pe.depsTail=n):pe.deps=pe.depsTail=n,yo(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=pe.depsTail,n.nextDep=void 0,pe.depsTail.nextDep=n,pe.depsTail=n,pe.deps===n&&(pe.deps=s)}return n}trigger(t){this.version++,fn++,this.notify(t)}notify(t){Ns();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Hs()}}}function yo(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)yo(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const ys=new WeakMap,Tt=Symbol(""),_s=Symbol(""),dn=Symbol("");function Te(e,t,n){if(Ue&&pe){let s=ys.get(e);s||ys.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new Fs),r.map=s,r.key=n),r.track()}}function ot(e,t,n,s,r,o){const i=ys.get(e);if(!i){fn++;return}const l=c=>{c&&c.trigger()};if(Ns(),t==="clear")i.forEach(l);else{const c=B(e),f=c&&Ls(n);if(c&&n==="length"){const a=Number(s);i.forEach((d,p)=>{(p==="length"||p===dn||!ut(p)&&p>=a)&&l(d)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),f&&l(i.get(dn)),t){case"add":c?f&&l(i.get("length")):(l(i.get(Tt)),Dt(e)&&l(i.get(_s)));break;case"delete":c||(l(i.get(Tt)),Dt(e)&&l(i.get(_s)));break;case"set":Dt(e)&&l(i.get(Tt));break}}Hs()}function $t(e){const t=ne(e);return t===e?t:(Te(t,"iterate",dn),Ve(e)?t:t.map(Re))}function Un(e){return Te(e=ne(e),"iterate",dn),e}const Ki={__proto__:null,[Symbol.iterator](){return ns(this,Symbol.iterator,Re)},concat(...e){return $t(this).concat(...e.map(t=>B(t)?$t(t):t))},entries(){return ns(this,"entries",e=>(e[1]=Re(e[1]),e))},every(e,t){return nt(this,"every",e,t,void 0,arguments)},filter(e,t){return nt(this,"filter",e,t,n=>n.map(Re),arguments)},find(e,t){return nt(this,"find",e,t,Re,arguments)},findIndex(e,t){return nt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return nt(this,"findLast",e,t,Re,arguments)},findLastIndex(e,t){return nt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return nt(this,"forEach",e,t,void 0,arguments)},includes(...e){return ss(this,"includes",e)},indexOf(...e){return ss(this,"indexOf",e)},join(e){return $t(this).join(e)},lastIndexOf(...e){return ss(this,"lastIndexOf",e)},map(e,t){return nt(this,"map",e,t,void 0,arguments)},pop(){return Jt(this,"pop")},push(...e){return Jt(this,"push",e)},reduce(e,...t){return nr(this,"reduce",e,t)},reduceRight(e,...t){return nr(this,"reduceRight",e,t)},shift(){return Jt(this,"shift")},some(e,t){return nt(this,"some",e,t,void 0,arguments)},splice(...e){return Jt(this,"splice",e)},toReversed(){return $t(this).toReversed()},toSorted(e){return $t(this).toSorted(e)},toSpliced(...e){return $t(this).toSpliced(...e)},unshift(...e){return Jt(this,"unshift",e)},values(){return ns(this,"values",Re)}};function ns(e,t,n){const s=Un(e),r=s[t]();return s!==e&&!Ve(e)&&(r._next=r.next,r.next=()=>{const o=r._next();return o.value&&(o.value=n(o.value)),o}),r}const Wi=Array.prototype;function nt(e,t,n,s,r,o){const i=Un(e),l=i!==e&&!Ve(e),c=i[t];if(c!==Wi[t]){const d=c.apply(e,o);return l?Re(d):d}let f=n;i!==e&&(l?f=function(d,p){return n.call(this,Re(d),p,e)}:n.length>2&&(f=function(d,p){return n.call(this,d,p,e)}));const a=c.call(i,f,s);return l&&r?r(a):a}function nr(e,t,n,s){const r=Un(e);let o=n;return r!==e&&(Ve(e)?n.length>3&&(o=function(i,l,c){return n.call(this,i,l,c,e)}):o=function(i,l,c){return n.call(this,i,Re(l),c,e)}),r[t](o,...s)}function ss(e,t,n){const s=ne(e);Te(s,"iterate",dn);const r=s[t](...n);return(r===-1||r===!1)&&Bs(n[0])?(n[0]=ne(n[0]),s[t](...n)):r}function Jt(e,t,n=[]){ct(),Ns();const s=ne(e)[t].apply(e,n);return Hs(),at(),s}const Gi=$s("__proto__,__v_isRef,__isVue"),_o=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(ut));function qi(e){ut(e)||(e=String(e));const t=ne(this);return Te(t,"has",e),t.hasOwnProperty(e)}class bo{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return o;if(n==="__v_raw")return s===(r?o?rl:Co:o?Eo:So).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const i=B(t);if(!r){let c;if(i&&(c=Ki[n]))return c;if(n==="hasOwnProperty")return qi}const l=Reflect.get(t,n,Me(t)?t:s);return(ut(n)?_o.has(n):Gi(n))||(r||Te(t,"get",n),o)?l:Me(l)?i&&Ls(n)?l:l.value:ge(l)?r?Ro(l):Kn(l):l}}class wo extends bo{constructor(t=!1){super(!1,t)}set(t,n,s,r){let o=t[n];if(!this._isShallow){const c=St(o);if(!Ve(s)&&!St(s)&&(o=ne(o),s=ne(s)),!B(t)&&Me(o)&&!Me(s))return c?!1:(o.value=s,!0)}const i=B(t)&&Ls(n)?Number(n)<t.length:ie(t,n),l=Reflect.set(t,n,s,Me(t)?t:r);return t===ne(r)&&(i?_t(s,o)&&ot(t,"set",n,s):ot(t,"add",n,s)),l}deleteProperty(t,n){const s=ie(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&ot(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!ut(n)||!_o.has(n))&&Te(t,"has",n),s}ownKeys(t){return Te(t,"iterate",B(t)?"length":Tt),Reflect.ownKeys(t)}}class Yi extends bo{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Ji=new wo,Qi=new Yi,Xi=new wo(!0);const bs=e=>e,Cn=e=>Reflect.getPrototypeOf(e);function Zi(e,t,n){return function(...s){const r=this.__v_raw,o=ne(r),i=Dt(o),l=e==="entries"||e===Symbol.iterator&&i,c=e==="keys"&&i,f=r[e](...s),a=n?bs:t?In:Re;return!t&&Te(o,"iterate",c?_s:Tt),{next(){const{value:d,done:p}=f.next();return p?{value:d,done:p}:{value:l?[a(d[0]),a(d[1])]:a(d),done:p}},[Symbol.iterator](){return this}}}}function xn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function el(e,t){const n={get(r){const o=this.__v_raw,i=ne(o),l=ne(r);e||(_t(r,l)&&Te(i,"get",r),Te(i,"get",l));const{has:c}=Cn(i),f=t?bs:e?In:Re;if(c.call(i,r))return f(o.get(r));if(c.call(i,l))return f(o.get(l));o!==i&&o.get(r)},get size(){const r=this.__v_raw;return!e&&Te(ne(r),"iterate",Tt),Reflect.get(r,"size",r)},has(r){const o=this.__v_raw,i=ne(o),l=ne(r);return e||(_t(r,l)&&Te(i,"has",r),Te(i,"has",l)),r===l?o.has(r):o.has(r)||o.has(l)},forEach(r,o){const i=this,l=i.__v_raw,c=ne(l),f=t?bs:e?In:Re;return!e&&Te(c,"iterate",Tt),l.forEach((a,d)=>r.call(o,f(a),f(d),i))}};return we(n,e?{add:xn("add"),set:xn("set"),delete:xn("delete"),clear:xn("clear")}:{add(r){!t&&!Ve(r)&&!St(r)&&(r=ne(r));const o=ne(this);return Cn(o).has.call(o,r)||(o.add(r),ot(o,"add",r,r)),this},set(r,o){!t&&!Ve(o)&&!St(o)&&(o=ne(o));const i=ne(this),{has:l,get:c}=Cn(i);let f=l.call(i,r);f||(r=ne(r),f=l.call(i,r));const a=c.call(i,r);return i.set(r,o),f?_t(o,a)&&ot(i,"set",r,o):ot(i,"add",r,o),this},delete(r){const o=ne(this),{has:i,get:l}=Cn(o);let c=i.call(o,r);c||(r=ne(r),c=i.call(o,r)),l&&l.call(o,r);const f=o.delete(r);return c&&ot(o,"delete",r,void 0),f},clear(){const r=ne(this),o=r.size!==0,i=r.clear();return o&&ot(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=Zi(r,e,t)}),n}function js(e,t){const n=el(e,t);return(s,r,o)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(ie(n,r)&&r in s?n:s,r,o)}const tl={get:js(!1,!1)},nl={get:js(!1,!0)},sl={get:js(!0,!1)};const So=new WeakMap,Eo=new WeakMap,Co=new WeakMap,rl=new WeakMap;function ol(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function il(e){return e.__v_skip||!Object.isExtensible(e)?0:ol(Oi(e))}function Kn(e){return St(e)?e:Vs(e,!1,Ji,tl,So)}function xo(e){return Vs(e,!1,Xi,nl,Eo)}function Ro(e){return Vs(e,!0,Qi,sl,Co)}function Vs(e,t,n,s,r){if(!ge(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=il(e);if(o===0)return e;const i=r.get(e);if(i)return i;const l=new Proxy(e,o===2?s:n);return r.set(e,l),l}function Ft(e){return St(e)?Ft(e.__v_raw):!!(e&&e.__v_isReactive)}function St(e){return!!(e&&e.__v_isReadonly)}function Ve(e){return!!(e&&e.__v_isShallow)}function Bs(e){return e?!!e.__v_raw:!1}function ne(e){const t=e&&e.__v_raw;return t?ne(t):e}function Ao(e){return!ie(e,"__v_skip")&&Object.isExtensible(e)&&gs(e,"__v_skip",!0),e}const Re=e=>ge(e)?Kn(e):e,In=e=>ge(e)?Ro(e):e;function Me(e){return e?e.__v_isRef===!0:!1}function Ce(e){return To(e,!1)}function ll(e){return To(e,!0)}function To(e,t){return Me(e)?e:new cl(e,t)}class cl{constructor(t,n){this.dep=new Fs,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:ne(t),this._value=n?t:Re(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||Ve(t)||St(t);t=s?t:ne(t),_t(t,n)&&(this._rawValue=t,this._value=s?t:Re(t),this.dep.trigger())}}function jt(e){return Me(e)?e.value:e}const al={get:(e,t,n)=>t==="__v_raw"?e:jt(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return Me(r)&&!Me(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function Po(e){return Ft(e)?e:new Proxy(e,al)}class ul{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Fs(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=fn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&pe!==this)return ho(this,!0),!0}get value(){const t=this.dep.track();return mo(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function fl(e,t,n=!1){let s,r;return W(e)?s=e:(s=e.get,r=e.set),new ul(s,r,n)}const Rn={},kn=new WeakMap;let At;function dl(e,t=!1,n=At){if(n){let s=kn.get(n);s||kn.set(n,s=[]),s.push(e)}}function hl(e,t,n=de){const{immediate:s,deep:r,once:o,scheduler:i,augmentJob:l,call:c}=n,f=k=>r?k:Ve(k)||r===!1||r===0?it(k,1):it(k);let a,d,p,m,S=!1,E=!1;if(Me(e)?(d=()=>e.value,S=Ve(e)):Ft(e)?(d=()=>f(e),S=!0):B(e)?(E=!0,S=e.some(k=>Ft(k)||Ve(k)),d=()=>e.map(k=>{if(Me(k))return k.value;if(Ft(k))return f(k);if(W(k))return c?c(k,2):k()})):W(e)?t?d=c?()=>c(e,2):e:d=()=>{if(p){ct();try{p()}finally{at()}}const k=At;At=a;try{return c?c(e,3,[m]):e(m)}finally{At=k}}:d=et,t&&r){const k=d,q=r===!0?1/0:r;d=()=>it(k(),q)}const N=Vi(),H=()=>{a.stop(),N&&N.active&&ks(N.effects,a)};if(o&&t){const k=t;t=(...q)=>{k(...q),H()}}let P=E?new Array(e.length).fill(Rn):Rn;const D=k=>{if(!(!(a.flags&1)||!a.dirty&&!k))if(t){const q=a.run();if(r||S||(E?q.some((le,re)=>_t(le,P[re])):_t(q,P))){p&&p();const le=At;At=a;try{const re=[q,P===Rn?void 0:E&&P[0]===Rn?[]:P,m];P=q,c?c(t,3,re):t(...re)}finally{At=le}}}else a.run()};return l&&l(D),a=new uo(d),a.scheduler=i?()=>i(D,!1):D,m=k=>dl(k,!1,a),p=a.onStop=()=>{const k=kn.get(a);if(k){if(c)c(k,4);else for(const q of k)q();kn.delete(a)}},t?s?D(!0):P=a.run():i?i(D.bind(null,!0),!0):a.run(),H.pause=a.pause.bind(a),H.resume=a.resume.bind(a),H.stop=H,H}function it(e,t=1/0,n){if(t<=0||!ge(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,Me(e))it(e.value,t,n);else if(B(e))for(let s=0;s<e.length;s++)it(e[s],t,n);else if(to(e)||Dt(e))e.forEach(s=>{it(s,t,n)});else if(ro(e)){for(const s in e)it(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&it(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function wn(e,t,n,s){try{return s?e(...s):e()}catch(r){Wn(r,t,n)}}function Ke(e,t,n,s){if(W(e)){const r=wn(e,t,n,s);return r&&no(r)&&r.catch(o=>{Wn(o,t,n)}),r}if(B(e)){const r=[];for(let o=0;o<e.length;o++)r.push(Ke(e[o],t,n,s));return r}}function Wn(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||de;if(t){let l=t.parent;const c=t.proxy,f=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const a=l.ec;if(a){for(let d=0;d<a.length;d++)if(a[d](e,c,f)===!1)return}l=l.parent}if(o){ct(),wn(o,null,10,[e,c,f]),at();return}}pl(e,n,r,s,i)}function pl(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const Ie=[];let Xe=-1;const Vt=[];let mt=null,kt=0;const Mo=Promise.resolve();let Ln=null;function Oo(e){const t=Ln||Mo;return e?t.then(this?e.bind(this):e):t}function gl(e){let t=Xe+1,n=Ie.length;for(;t<n;){const s=t+n>>>1,r=Ie[s],o=hn(r);o<e||o===e&&r.flags&2?t=s+1:n=s}return t}function Us(e){if(!(e.flags&1)){const t=hn(e),n=Ie[Ie.length-1];!n||!(e.flags&2)&&t>=hn(n)?Ie.push(e):Ie.splice(gl(t),0,e),e.flags|=1,$o()}}function $o(){Ln||(Ln=Mo.then(ko))}function ml(e){B(e)?Vt.push(...e):mt&&e.id===-1?mt.splice(kt+1,0,e):e.flags&1||(Vt.push(e),e.flags|=1),$o()}function sr(e,t,n=Xe+1){for(;n<Ie.length;n++){const s=Ie[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;Ie.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function Io(e){if(Vt.length){const t=[...new Set(Vt)].sort((n,s)=>hn(n)-hn(s));if(Vt.length=0,mt){mt.push(...t);return}for(mt=t,kt=0;kt<mt.length;kt++){const n=mt[kt];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}mt=null,kt=0}}const hn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function ko(e){try{for(Xe=0;Xe<Ie.length;Xe++){const t=Ie[Xe];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),wn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Xe<Ie.length;Xe++){const t=Ie[Xe];t&&(t.flags&=-2)}Xe=-1,Ie.length=0,Io(),Ln=null,(Ie.length||Vt.length)&&ko()}}let Ae=null,Lo=null;function zn(e){const t=Ae;return Ae=e,Lo=e&&e.type.__scopeId||null,t}function xe(e,t=Ae,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&dr(-1);const o=zn(t);let i;try{i=e(...r)}finally{zn(o),s._d&&dr(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function vl(e,t){if(Ae===null)return e;const n=Qn(Ae),s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[o,i,l,c=de]=t[r];o&&(W(o)&&(o={mounted:o,updated:o}),o.deep&&it(i),s.push({dir:o,instance:n,value:i,oldValue:void 0,arg:l,modifiers:c}))}return e}function Ct(e,t,n,s){const r=e.dirs,o=t&&t.dirs;for(let i=0;i<r.length;i++){const l=r[i];o&&(l.oldValue=o[i].value);let c=l.dir[s];c&&(ct(),Ke(c,n,8,[e.el,l,e,t]),at())}}const yl=Symbol("_vte"),_l=e=>e.__isTeleport,It=Symbol("_leaveCb"),An=Symbol("_enterCb");function bl(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return qn(()=>{e.isMounted=!0}),jo(()=>{e.isUnmounting=!0}),e}const je=[Function,Array],wl={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:je,onEnter:je,onAfterEnter:je,onEnterCancelled:je,onBeforeLeave:je,onLeave:je,onAfterLeave:je,onLeaveCancelled:je,onBeforeAppear:je,onAppear:je,onAfterAppear:je,onAppearCancelled:je};function Sl(e,t){const{leavingVNodes:n}=e;let s=n.get(t.type);return s||(s=Object.create(null),n.set(t.type,s)),s}function ws(e,t,n,s,r){const{appear:o,mode:i,persisted:l=!1,onBeforeEnter:c,onEnter:f,onAfterEnter:a,onEnterCancelled:d,onBeforeLeave:p,onLeave:m,onAfterLeave:S,onLeaveCancelled:E,onBeforeAppear:N,onAppear:H,onAfterAppear:P,onAppearCancelled:D}=t,k=String(e.key),q=Sl(n,e),le=(K,X)=>{K&&Ke(K,s,9,X)},re=(K,X)=>{const Z=X[1];le(K,X),B(K)?K.every(L=>L.length<=1)&&Z():K.length<=1&&Z()},Se={mode:i,persisted:l,beforeEnter(K){let X=c;if(!n.isMounted)if(o)X=N||c;else return;K[It]&&K[It](!0);const Z=q[k];Z&&Lt(e,Z)&&Z.el[It]&&Z.el[It](),le(X,[K])},enter(K){let X=f,Z=a,L=d;if(!n.isMounted)if(o)X=H||f,Z=P||a,L=D||d;else return;let ee=!1;const ye=K[An]=Oe=>{ee||(ee=!0,Oe?le(L,[K]):le(Z,[K]),Se.delayedLeave&&Se.delayedLeave(),K[An]=void 0)};X?re(X,[K,ye]):ye()},leave(K,X){const Z=String(e.key);if(K[An]&&K[An](!0),n.isUnmounting)return X();le(p,[K]);let L=!1;const ee=K[It]=ye=>{L||(L=!0,X(),ye?le(E,[K]):le(S,[K]),K[It]=void 0,q[Z]===e&&delete q[Z])};q[Z]=e,m?re(m,[K,ee]):ee()},clone(K){return ws(K,t,n,s)}};return Se}function pn(e,t){e.shapeFlag&6&&e.component?(e.transition=t,pn(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function zo(e,t=!1,n){let s=[],r=0;for(let o=0;o<e.length;o++){let i=e[o];const l=n==null?i.key:String(n)+String(i.key!=null?i.key:o);i.type===_e?(i.patchFlag&128&&r++,s=s.concat(zo(i.children,t,l))):(t||i.type!==tt)&&s.push(l!=null?Pt(i,{key:l}):i)}if(r>1)for(let o=0;o<s.length;o++)s[o].patchFlag=-2;return s}/*! #__NO_SIDE_EFFECTS__ */function Mt(e,t){return W(e)?we({name:e.name},t,{setup:e}):e}function No(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function rn(e,t,n,s,r=!1){if(B(e)){e.forEach((S,E)=>rn(S,t&&(B(t)?t[E]:t),n,s,r));return}if(Bt(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&rn(e,t,n,s.component.subTree);return}const o=s.shapeFlag&4?Qn(s.component):s.el,i=r?null:o,{i:l,r:c}=e,f=t&&t.r,a=l.refs===de?l.refs={}:l.refs,d=l.setupState,p=ne(d),m=d===de?()=>!1:S=>ie(p,S);if(f!=null&&f!==c&&(ve(f)?(a[f]=null,m(f)&&(d[f]=null)):Me(f)&&(f.value=null)),W(c))wn(c,l,12,[i,a]);else{const S=ve(c),E=Me(c);if(S||E){const N=()=>{if(e.f){const H=S?m(c)?d[c]:a[c]:c.value;r?B(H)&&ks(H,o):B(H)?H.includes(o)||H.push(o):S?(a[c]=[o],m(c)&&(d[c]=a[c])):(c.value=[o],e.k&&(a[e.k]=c.value))}else S?(a[c]=i,m(c)&&(d[c]=i)):E&&(c.value=i,e.k&&(a[e.k]=i))};i?(N.id=-1,He(N,n)):N()}}}Bn().requestIdleCallback;Bn().cancelIdleCallback;const Bt=e=>!!e.type.__asyncLoader,Ho=e=>e.type.__isKeepAlive;function El(e,t){Do(e,"a",t)}function Cl(e,t){Do(e,"da",t)}function Do(e,t,n=Pe){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(Gn(t,s,n),n){let r=n.parent;for(;r&&r.parent;)Ho(r.parent.vnode)&&xl(s,t,n,r),r=r.parent}}function xl(e,t,n,s){const r=Gn(t,e,s,!0);Ks(()=>{ks(s[t],r)},n)}function Gn(e,t,n=Pe,s=!1){if(n){const r=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{ct();const l=Sn(n),c=Ke(t,n,e,i);return l(),at(),c});return s?r.unshift(o):r.push(o),o}}const ft=e=>(t,n=Pe)=>{(!yn||e==="sp")&&Gn(e,(...s)=>t(...s),n)},Rl=ft("bm"),qn=ft("m"),Al=ft("bu"),Fo=ft("u"),jo=ft("bum"),Ks=ft("um"),Tl=ft("sp"),Pl=ft("rtg"),Ml=ft("rtc");function Ol(e,t=Pe){Gn("ec",e,t)}const $l=Symbol.for("v-ndc");function on(e,t,n,s){let r;const o=n,i=B(e);if(i||ve(e)){const l=i&&Ft(e);let c=!1,f=!1;l&&(c=!Ve(e),f=St(e),e=Un(e)),r=new Array(e.length);for(let a=0,d=e.length;a<d;a++)r[a]=t(c?f?In(Re(e[a])):Re(e[a]):e[a],a,void 0,o)}else if(typeof e=="number"){r=new Array(e);for(let l=0;l<e;l++)r[l]=t(l+1,l,void 0,o)}else if(ge(e))if(e[Symbol.iterator])r=Array.from(e,(l,c)=>t(l,c,void 0,o));else{const l=Object.keys(e);r=new Array(l.length);for(let c=0,f=l.length;c<f;c++){const a=l[c];r[c]=t(e[a],a,c,o)}}else r=[];return r}function rs(e,t,n={},s,r){if(Ae.ce||Ae.parent&&Bt(Ae.parent)&&Ae.parent.ce)return t!=="default"&&(n.name=t),J(),mn(_e,null,[fe("slot",n,s)],64);let o=e[t];o&&o._c&&(o._d=!1),J();const i=o&&Vo(o(n)),l=n.key||i&&i.key,c=mn(_e,{key:(l&&!ut(l)?l:`_${t}`)+""},i||[],i&&e._===1?64:-2);return o&&o._c&&(o._d=!0),c}function Vo(e){return e.some(t=>vn(t)?!(t.type===tt||t.type===_e&&!Vo(t.children)):!0)?e:null}const Ss=e=>e?ci(e)?Qn(e):Ss(e.parent):null,ln=we(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Ss(e.parent),$root:e=>Ss(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Uo(e),$forceUpdate:e=>e.f||(e.f=()=>{Us(e.update)}),$nextTick:e=>e.n||(e.n=Oo.bind(e.proxy)),$watch:e=>Zl.bind(e)}),os=(e,t)=>e!==de&&!e.__isScriptSetup&&ie(e,t),Il={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:o,accessCache:i,type:l,appContext:c}=e;let f;if(t[0]!=="$"){const m=i[t];if(m!==void 0)switch(m){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return o[t]}else{if(os(s,t))return i[t]=1,s[t];if(r!==de&&ie(r,t))return i[t]=2,r[t];if((f=e.propsOptions[0])&&ie(f,t))return i[t]=3,o[t];if(n!==de&&ie(n,t))return i[t]=4,n[t];Es&&(i[t]=0)}}const a=ln[t];let d,p;if(a)return t==="$attrs"&&Te(e.attrs,"get",""),a(e);if((d=l.__cssModules)&&(d=d[t]))return d;if(n!==de&&ie(n,t))return i[t]=4,n[t];if(p=c.config.globalProperties,ie(p,t))return p[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:o}=e;return os(r,t)?(r[t]=n,!0):s!==de&&ie(s,t)?(s[t]=n,!0):ie(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:o}},i){let l;return!!n[i]||e!==de&&ie(e,i)||os(t,i)||(l=o[0])&&ie(l,i)||ie(s,i)||ie(ln,i)||ie(r.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:ie(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function rr(e){return B(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Es=!0;function kl(e){const t=Uo(e),n=e.proxy,s=e.ctx;Es=!1,t.beforeCreate&&or(t.beforeCreate,e,"bc");const{data:r,computed:o,methods:i,watch:l,provide:c,inject:f,created:a,beforeMount:d,mounted:p,beforeUpdate:m,updated:S,activated:E,deactivated:N,beforeDestroy:H,beforeUnmount:P,destroyed:D,unmounted:k,render:q,renderTracked:le,renderTriggered:re,errorCaptured:Se,serverPrefetch:K,expose:X,inheritAttrs:Z,components:L,directives:ee,filters:ye}=t;if(f&&Ll(f,s,null),i)for(const x in i){const b=i[x];W(b)&&(s[x]=b.bind(n))}if(r){const x=r.call(n,n);ge(x)&&(e.data=Kn(x))}if(Es=!0,o)for(const x in o){const b=o[x],z=W(b)?b.bind(n,n):W(b.get)?b.get.bind(n,n):et,G=!W(b)&&W(b.set)?b.set.bind(n):et,Y=Be({get:z,set:G});Object.defineProperty(s,x,{enumerable:!0,configurable:!0,get:()=>Y.value,set:ce=>Y.value=ce})}if(l)for(const x in l)Bo(l[x],s,n,x);if(c){const x=W(c)?c.call(n):c;Reflect.ownKeys(x).forEach(b=>{Pn(b,x[b])})}a&&or(a,e,"c");function me(x,b){B(b)?b.forEach(z=>x(z.bind(n))):b&&x(b.bind(n))}if(me(Rl,d),me(qn,p),me(Al,m),me(Fo,S),me(El,E),me(Cl,N),me(Ol,Se),me(Ml,le),me(Pl,re),me(jo,P),me(Ks,k),me(Tl,K),B(X))if(X.length){const x=e.exposed||(e.exposed={});X.forEach(b=>{Object.defineProperty(x,b,{get:()=>n[b],set:z=>n[b]=z})})}else e.exposed||(e.exposed={});q&&e.render===et&&(e.render=q),Z!=null&&(e.inheritAttrs=Z),L&&(e.components=L),ee&&(e.directives=ee),K&&No(e)}function Ll(e,t,n=et){B(e)&&(e=Cs(e));for(const s in e){const r=e[s];let o;ge(r)?"default"in r?o=lt(r.from||s,r.default,!0):o=lt(r.from||s):o=lt(r),Me(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[s]=o}}function or(e,t,n){Ke(B(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Bo(e,t,n,s){let r=s.includes(".")?si(n,s):()=>n[s];if(ve(e)){const o=t[e];W(o)&&Mn(r,o)}else if(W(e))Mn(r,e.bind(n));else if(ge(e))if(B(e))e.forEach(o=>Bo(o,t,n,s));else{const o=W(e.handler)?e.handler.bind(n):t[e.handler];W(o)&&Mn(r,o,e)}}function Uo(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,l=o.get(t);let c;return l?c=l:!r.length&&!n&&!s?c=t:(c={},r.length&&r.forEach(f=>Nn(c,f,i,!0)),Nn(c,t,i)),ge(t)&&o.set(t,c),c}function Nn(e,t,n,s=!1){const{mixins:r,extends:o}=t;o&&Nn(e,o,n,!0),r&&r.forEach(i=>Nn(e,i,n,!0));for(const i in t)if(!(s&&i==="expose")){const l=zl[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const zl={data:ir,props:lr,emits:lr,methods:en,computed:en,beforeCreate:$e,created:$e,beforeMount:$e,mounted:$e,beforeUpdate:$e,updated:$e,beforeDestroy:$e,beforeUnmount:$e,destroyed:$e,unmounted:$e,activated:$e,deactivated:$e,errorCaptured:$e,serverPrefetch:$e,components:en,directives:en,watch:Hl,provide:ir,inject:Nl};function ir(e,t){return t?e?function(){return we(W(e)?e.call(this,this):e,W(t)?t.call(this,this):t)}:t:e}function Nl(e,t){return en(Cs(e),Cs(t))}function Cs(e){if(B(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function $e(e,t){return e?[...new Set([].concat(e,t))]:t}function en(e,t){return e?we(Object.create(null),e,t):t}function lr(e,t){return e?B(e)&&B(t)?[...new Set([...e,...t])]:we(Object.create(null),rr(e),rr(t??{})):t}function Hl(e,t){if(!e)return t;if(!t)return e;const n=we(Object.create(null),e);for(const s in t)n[s]=$e(e[s],t[s]);return n}function Ko(){return{app:null,config:{isNativeTag:Pi,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Dl=0;function Fl(e,t){return function(s,r=null){W(s)||(s=we({},s)),r!=null&&!ge(r)&&(r=null);const o=Ko(),i=new WeakSet,l=[];let c=!1;const f=o.app={_uid:Dl++,_component:s,_props:r,_container:null,_context:o,_instance:null,version:bc,get config(){return o.config},set config(a){},use(a,...d){return i.has(a)||(a&&W(a.install)?(i.add(a),a.install(f,...d)):W(a)&&(i.add(a),a(f,...d))),f},mixin(a){return o.mixins.includes(a)||o.mixins.push(a),f},component(a,d){return d?(o.components[a]=d,f):o.components[a]},directive(a,d){return d?(o.directives[a]=d,f):o.directives[a]},mount(a,d,p){if(!c){const m=f._ceVNode||fe(s,r);return m.appContext=o,p===!0?p="svg":p===!1&&(p=void 0),e(m,a,p),c=!0,f._container=a,a.__vue_app__=f,Qn(m.component)}},onUnmount(a){l.push(a)},unmount(){c&&(Ke(l,f._instance,16),e(null,f._container),delete f._container.__vue_app__)},provide(a,d){return o.provides[a]=d,f},runWithContext(a){const d=Ut;Ut=f;try{return a()}finally{Ut=d}}};return f}}let Ut=null;function Pn(e,t){if(Pe){let n=Pe.provides;const s=Pe.parent&&Pe.parent.provides;s===n&&(n=Pe.provides=Object.create(s)),n[e]=t}}function lt(e,t,n=!1){const s=Pe||Ae;if(s||Ut){let r=Ut?Ut._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&W(t)?t.call(s&&s.proxy):t}}const Wo={},Go=()=>Object.create(Wo),qo=e=>Object.getPrototypeOf(e)===Wo;function jl(e,t,n,s=!1){const r={},o=Go();e.propsDefaults=Object.create(null),Yo(e,t,r,o);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);n?e.props=s?r:xo(r):e.type.props?e.props=r:e.props=o,e.attrs=o}function Vl(e,t,n,s){const{props:r,attrs:o,vnode:{patchFlag:i}}=e,l=ne(r),[c]=e.propsOptions;let f=!1;if((s||i>0)&&!(i&16)){if(i&8){const a=e.vnode.dynamicProps;for(let d=0;d<a.length;d++){let p=a[d];if(Yn(e.emitsOptions,p))continue;const m=t[p];if(c)if(ie(o,p))m!==o[p]&&(o[p]=m,f=!0);else{const S=bt(p);r[S]=xs(c,l,S,m,e,!1)}else m!==o[p]&&(o[p]=m,f=!0)}}}else{Yo(e,t,r,o)&&(f=!0);let a;for(const d in l)(!t||!ie(t,d)&&((a=Et(d))===d||!ie(t,a)))&&(c?n&&(n[d]!==void 0||n[a]!==void 0)&&(r[d]=xs(c,l,d,void 0,e,!0)):delete r[d]);if(o!==l)for(const d in o)(!t||!ie(t,d))&&(delete o[d],f=!0)}f&&ot(e.attrs,"set","")}function Yo(e,t,n,s){const[r,o]=e.propsOptions;let i=!1,l;if(t)for(let c in t){if(tn(c))continue;const f=t[c];let a;r&&ie(r,a=bt(c))?!o||!o.includes(a)?n[a]=f:(l||(l={}))[a]=f:Yn(e.emitsOptions,c)||(!(c in s)||f!==s[c])&&(s[c]=f,i=!0)}if(o){const c=ne(n),f=l||de;for(let a=0;a<o.length;a++){const d=o[a];n[d]=xs(r,c,d,f[d],e,!ie(f,d))}}return i}function xs(e,t,n,s,r,o){const i=e[n];if(i!=null){const l=ie(i,"default");if(l&&s===void 0){const c=i.default;if(i.type!==Function&&!i.skipFactory&&W(c)){const{propsDefaults:f}=r;if(n in f)s=f[n];else{const a=Sn(r);s=f[n]=c.call(null,t),a()}}else s=c;r.ce&&r.ce._setProp(n,s)}i[0]&&(o&&!l?s=!1:i[1]&&(s===""||s===Et(n))&&(s=!0))}return s}const Bl=new WeakMap;function Jo(e,t,n=!1){const s=n?Bl:t.propsCache,r=s.get(e);if(r)return r;const o=e.props,i={},l=[];let c=!1;if(!W(e)){const a=d=>{c=!0;const[p,m]=Jo(d,t,!0);we(i,p),m&&l.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!o&&!c)return ge(e)&&s.set(e,Ht),Ht;if(B(o))for(let a=0;a<o.length;a++){const d=bt(o[a]);cr(d)&&(i[d]=de)}else if(o)for(const a in o){const d=bt(a);if(cr(d)){const p=o[a],m=i[d]=B(p)||W(p)?{type:p}:we({},p),S=m.type;let E=!1,N=!0;if(B(S))for(let H=0;H<S.length;++H){const P=S[H],D=W(P)&&P.name;if(D==="Boolean"){E=!0;break}else D==="String"&&(N=!1)}else E=W(S)&&S.name==="Boolean";m[0]=E,m[1]=N,(E||ie(m,"default"))&&l.push(d)}}const f=[i,l];return ge(e)&&s.set(e,f),f}function cr(e){return e[0]!=="$"&&!tn(e)}const Ws=e=>e[0]==="_"||e==="$stable",Gs=e=>B(e)?e.map(Ze):[Ze(e)],Ul=(e,t,n)=>{if(t._n)return t;const s=xe((...r)=>Gs(t(...r)),n);return s._c=!1,s},Qo=(e,t,n)=>{const s=e._ctx;for(const r in e){if(Ws(r))continue;const o=e[r];if(W(o))t[r]=Ul(r,o,s);else if(o!=null){const i=Gs(o);t[r]=()=>i}}},Xo=(e,t)=>{const n=Gs(t);e.slots.default=()=>n},Zo=(e,t,n)=>{for(const s in t)(n||!Ws(s))&&(e[s]=t[s])},Kl=(e,t,n)=>{const s=e.slots=Go();if(e.vnode.shapeFlag&32){const r=t.__;r&&gs(s,"__",r,!0);const o=t._;o?(Zo(s,t,n),n&&gs(s,"_",o,!0)):Qo(t,s)}else t&&Xo(e,t)},Wl=(e,t,n)=>{const{vnode:s,slots:r}=e;let o=!0,i=de;if(s.shapeFlag&32){const l=t._;l?n&&l===1?o=!1:Zo(r,t,n):(o=!t.$stable,Qo(t,r)),i=t}else t&&(Xo(e,t),i={default:1});if(o)for(const l in r)!Ws(l)&&i[l]==null&&delete r[l]},He=ic;function Gl(e){return ql(e)}function ql(e,t){const n=Bn();n.__VUE__=!0;const{insert:s,remove:r,patchProp:o,createElement:i,createText:l,createComment:c,setText:f,setElementText:a,parentNode:d,nextSibling:p,setScopeId:m=et,insertStaticContent:S}=e,E=(u,h,g,v=null,w=null,_=null,M=void 0,T=null,A=!!h.dynamicChildren)=>{if(u===h)return;u&&!Lt(u,h)&&(v=y(u),ce(u,w,_,!0),u=null),h.patchFlag===-2&&(A=!1,h.dynamicChildren=null);const{type:C,ref:V,shapeFlag:$}=h;switch(C){case Jn:N(u,h,g,v);break;case tt:H(u,h,g,v);break;case ls:u==null&&P(h,g,v,M);break;case _e:L(u,h,g,v,w,_,M,T,A);break;default:$&1?q(u,h,g,v,w,_,M,T,A):$&6?ee(u,h,g,v,w,_,M,T,A):($&64||$&128)&&C.process(u,h,g,v,w,_,M,T,A,F)}V!=null&&w?rn(V,u&&u.ref,_,h||u,!h):V==null&&u&&u.ref!=null&&rn(u.ref,null,_,u,!0)},N=(u,h,g,v)=>{if(u==null)s(h.el=l(h.children),g,v);else{const w=h.el=u.el;h.children!==u.children&&f(w,h.children)}},H=(u,h,g,v)=>{u==null?s(h.el=c(h.children||""),g,v):h.el=u.el},P=(u,h,g,v)=>{[u.el,u.anchor]=S(u.children,h,g,v,u.el,u.anchor)},D=({el:u,anchor:h},g,v)=>{let w;for(;u&&u!==h;)w=p(u),s(u,g,v),u=w;s(h,g,v)},k=({el:u,anchor:h})=>{let g;for(;u&&u!==h;)g=p(u),r(u),u=g;r(h)},q=(u,h,g,v,w,_,M,T,A)=>{h.type==="svg"?M="svg":h.type==="math"&&(M="mathml"),u==null?le(h,g,v,w,_,M,T,A):K(u,h,w,_,M,T,A)},le=(u,h,g,v,w,_,M,T)=>{let A,C;const{props:V,shapeFlag:$,transition:j,dirs:U}=u;if(A=u.el=i(u.type,_,V&&V.is,V),$&8?a(A,u.children):$&16&&Se(u.children,A,null,v,w,is(u,_),M,T),U&&Ct(u,null,v,"created"),re(A,u,u.scopeId,M,v),V){for(const he in V)he!=="value"&&!tn(he)&&o(A,he,null,V[he],_,v);"value"in V&&o(A,"value",null,V.value,_),(C=V.onVnodeBeforeMount)&&Je(C,v,u)}U&&Ct(u,null,v,"beforeMount");const Q=Yl(w,j);Q&&j.beforeEnter(A),s(A,h,g),((C=V&&V.onVnodeMounted)||Q||U)&&He(()=>{C&&Je(C,v,u),Q&&j.enter(A),U&&Ct(u,null,v,"mounted")},w)},re=(u,h,g,v,w)=>{if(g&&m(u,g),v)for(let _=0;_<v.length;_++)m(u,v[_]);if(w){let _=w.subTree;if(h===_||oi(_.type)&&(_.ssContent===h||_.ssFallback===h)){const M=w.vnode;re(u,M,M.scopeId,M.slotScopeIds,w.parent)}}},Se=(u,h,g,v,w,_,M,T,A=0)=>{for(let C=A;C<u.length;C++){const V=u[C]=T?vt(u[C]):Ze(u[C]);E(null,V,h,g,v,w,_,M,T)}},K=(u,h,g,v,w,_,M)=>{const T=h.el=u.el;let{patchFlag:A,dynamicChildren:C,dirs:V}=h;A|=u.patchFlag&16;const $=u.props||de,j=h.props||de;let U;if(g&&xt(g,!1),(U=j.onVnodeBeforeUpdate)&&Je(U,g,h,u),V&&Ct(h,u,g,"beforeUpdate"),g&&xt(g,!0),($.innerHTML&&j.innerHTML==null||$.textContent&&j.textContent==null)&&a(T,""),C?X(u.dynamicChildren,C,T,g,v,is(h,w),_):M||b(u,h,T,null,g,v,is(h,w),_,!1),A>0){if(A&16)Z(T,$,j,g,w);else if(A&2&&$.class!==j.class&&o(T,"class",null,j.class,w),A&4&&o(T,"style",$.style,j.style,w),A&8){const Q=h.dynamicProps;for(let he=0;he<Q.length;he++){const ae=Q[he],ke=$[ae],Le=j[ae];(Le!==ke||ae==="value")&&o(T,ae,ke,Le,w,g)}}A&1&&u.children!==h.children&&a(T,h.children)}else!M&&C==null&&Z(T,$,j,g,w);((U=j.onVnodeUpdated)||V)&&He(()=>{U&&Je(U,g,h,u),V&&Ct(h,u,g,"updated")},v)},X=(u,h,g,v,w,_,M)=>{for(let T=0;T<h.length;T++){const A=u[T],C=h[T],V=A.el&&(A.type===_e||!Lt(A,C)||A.shapeFlag&198)?d(A.el):g;E(A,C,V,null,v,w,_,M,!0)}},Z=(u,h,g,v,w)=>{if(h!==g){if(h!==de)for(const _ in h)!tn(_)&&!(_ in g)&&o(u,_,h[_],null,w,v);for(const _ in g){if(tn(_))continue;const M=g[_],T=h[_];M!==T&&_!=="value"&&o(u,_,T,M,w,v)}"value"in g&&o(u,"value",h.value,g.value,w)}},L=(u,h,g,v,w,_,M,T,A)=>{const C=h.el=u?u.el:l(""),V=h.anchor=u?u.anchor:l("");let{patchFlag:$,dynamicChildren:j,slotScopeIds:U}=h;U&&(T=T?T.concat(U):U),u==null?(s(C,g,v),s(V,g,v),Se(h.children||[],g,V,w,_,M,T,A)):$>0&&$&64&&j&&u.dynamicChildren?(X(u.dynamicChildren,j,g,w,_,M,T),(h.key!=null||w&&h===w.subTree)&&ei(u,h,!0)):b(u,h,g,V,w,_,M,T,A)},ee=(u,h,g,v,w,_,M,T,A)=>{h.slotScopeIds=T,u==null?h.shapeFlag&512?w.ctx.activate(h,g,v,M,A):ye(h,g,v,w,_,M,A):Oe(u,h,A)},ye=(u,h,g,v,w,_,M)=>{const T=u.component=hc(u,v,w);if(Ho(u)&&(T.ctx.renderer=F),gc(T,!1,M),T.asyncDep){if(w&&w.registerDep(T,me,M),!u.el){const A=T.subTree=fe(tt);H(null,A,h,g)}}else me(T,u,h,g,w,_,M)},Oe=(u,h,g)=>{const v=h.component=u.component;if(rc(u,h,g))if(v.asyncDep&&!v.asyncResolved){x(v,h,g);return}else v.next=h,v.update();else h.el=u.el,v.vnode=h},me=(u,h,g,v,w,_,M)=>{const T=()=>{if(u.isMounted){let{next:$,bu:j,u:U,parent:Q,vnode:he}=u;{const qe=ti(u);if(qe){$&&($.el=he.el,x(u,$,M)),qe.asyncDep.then(()=>{u.isUnmounted||T()});return}}let ae=$,ke;xt(u,!1),$?($.el=he.el,x(u,$,M)):$=he,j&&Tn(j),(ke=$.props&&$.props.onVnodeBeforeUpdate)&&Je(ke,Q,$,he),xt(u,!0);const Le=ur(u),Ge=u.subTree;u.subTree=Le,E(Ge,Le,d(Ge.el),y(Ge),u,w,_),$.el=Le.el,ae===null&&oc(u,Le.el),U&&He(U,w),(ke=$.props&&$.props.onVnodeUpdated)&&He(()=>Je(ke,Q,$,he),w)}else{let $;const{el:j,props:U}=h,{bm:Q,m:he,parent:ae,root:ke,type:Le}=u,Ge=Bt(h);xt(u,!1),Q&&Tn(Q),!Ge&&($=U&&U.onVnodeBeforeMount)&&Je($,ae,h),xt(u,!0);{ke.ce&&ke.ce._def.shadowRoot!==!1&&ke.ce._injectChildStyle(Le);const qe=u.subTree=ur(u);E(null,qe,g,v,u,w,_),h.el=qe.el}if(he&&He(he,w),!Ge&&($=U&&U.onVnodeMounted)){const qe=h;He(()=>Je($,ae,qe),w)}(h.shapeFlag&256||ae&&Bt(ae.vnode)&&ae.vnode.shapeFlag&256)&&u.a&&He(u.a,w),u.isMounted=!0,h=g=v=null}};u.scope.on();const A=u.effect=new uo(T);u.scope.off();const C=u.update=A.run.bind(A),V=u.job=A.runIfDirty.bind(A);V.i=u,V.id=u.uid,A.scheduler=()=>Us(V),xt(u,!0),C()},x=(u,h,g)=>{h.component=u;const v=u.vnode.props;u.vnode=h,u.next=null,Vl(u,h.props,v,g),Wl(u,h.children,g),ct(),sr(u),at()},b=(u,h,g,v,w,_,M,T,A=!1)=>{const C=u&&u.children,V=u?u.shapeFlag:0,$=h.children,{patchFlag:j,shapeFlag:U}=h;if(j>0){if(j&128){G(C,$,g,v,w,_,M,T,A);return}else if(j&256){z(C,$,g,v,w,_,M,T,A);return}}U&8?(V&16&&Fe(C,w,_),$!==C&&a(g,$)):V&16?U&16?G(C,$,g,v,w,_,M,T,A):Fe(C,w,_,!0):(V&8&&a(g,""),U&16&&Se($,g,v,w,_,M,T,A))},z=(u,h,g,v,w,_,M,T,A)=>{u=u||Ht,h=h||Ht;const C=u.length,V=h.length,$=Math.min(C,V);let j;for(j=0;j<$;j++){const U=h[j]=A?vt(h[j]):Ze(h[j]);E(u[j],U,g,null,w,_,M,T,A)}C>V?Fe(u,w,_,!0,!1,$):Se(h,g,v,w,_,M,T,A,$)},G=(u,h,g,v,w,_,M,T,A)=>{let C=0;const V=h.length;let $=u.length-1,j=V-1;for(;C<=$&&C<=j;){const U=u[C],Q=h[C]=A?vt(h[C]):Ze(h[C]);if(Lt(U,Q))E(U,Q,g,null,w,_,M,T,A);else break;C++}for(;C<=$&&C<=j;){const U=u[$],Q=h[j]=A?vt(h[j]):Ze(h[j]);if(Lt(U,Q))E(U,Q,g,null,w,_,M,T,A);else break;$--,j--}if(C>$){if(C<=j){const U=j+1,Q=U<V?h[U].el:v;for(;C<=j;)E(null,h[C]=A?vt(h[C]):Ze(h[C]),g,Q,w,_,M,T,A),C++}}else if(C>j)for(;C<=$;)ce(u[C],w,_,!0),C++;else{const U=C,Q=C,he=new Map;for(C=Q;C<=j;C++){const Ne=h[C]=A?vt(h[C]):Ze(h[C]);Ne.key!=null&&he.set(Ne.key,C)}let ae,ke=0;const Le=j-Q+1;let Ge=!1,qe=0;const Yt=new Array(Le);for(C=0;C<Le;C++)Yt[C]=0;for(C=U;C<=$;C++){const Ne=u[C];if(ke>=Le){ce(Ne,w,_,!0);continue}let Ye;if(Ne.key!=null)Ye=he.get(Ne.key);else for(ae=Q;ae<=j;ae++)if(Yt[ae-Q]===0&&Lt(Ne,h[ae])){Ye=ae;break}Ye===void 0?ce(Ne,w,_,!0):(Yt[Ye-Q]=C+1,Ye>=qe?qe=Ye:Ge=!0,E(Ne,h[Ye],g,null,w,_,M,T,A),ke++)}const Xs=Ge?Jl(Yt):Ht;for(ae=Xs.length-1,C=Le-1;C>=0;C--){const Ne=Q+C,Ye=h[Ne],Zs=Ne+1<V?h[Ne+1].el:v;Yt[C]===0?E(null,Ye,g,Zs,w,_,M,T,A):Ge&&(ae<0||C!==Xs[ae]?Y(Ye,g,Zs,2):ae--)}}},Y=(u,h,g,v,w=null)=>{const{el:_,type:M,transition:T,children:A,shapeFlag:C}=u;if(C&6){Y(u.component.subTree,h,g,v);return}if(C&128){u.suspense.move(h,g,v);return}if(C&64){M.move(u,h,g,F);return}if(M===_e){s(_,h,g);for(let $=0;$<A.length;$++)Y(A[$],h,g,v);s(u.anchor,h,g);return}if(M===ls){D(u,h,g);return}if(v!==2&&C&1&&T)if(v===0)T.beforeEnter(_),s(_,h,g),He(()=>T.enter(_),w);else{const{leave:$,delayLeave:j,afterLeave:U}=T,Q=()=>{u.ctx.isUnmounted?r(_):s(_,h,g)},he=()=>{$(_,()=>{Q(),U&&U()})};j?j(_,Q,he):he()}else s(_,h,g)},ce=(u,h,g,v=!1,w=!1)=>{const{type:_,props:M,ref:T,children:A,dynamicChildren:C,shapeFlag:V,patchFlag:$,dirs:j,cacheIndex:U}=u;if($===-2&&(w=!1),T!=null&&(ct(),rn(T,null,g,u,!0),at()),U!=null&&(h.renderCache[U]=void 0),V&256){h.ctx.deactivate(u);return}const Q=V&1&&j,he=!Bt(u);let ae;if(he&&(ae=M&&M.onVnodeBeforeUnmount)&&Je(ae,h,u),V&6)En(u.component,g,v);else{if(V&128){u.suspense.unmount(g,v);return}Q&&Ct(u,null,h,"beforeUnmount"),V&64?u.type.remove(u,h,g,F,v):C&&!C.hasOnce&&(_!==_e||$>0&&$&64)?Fe(C,h,g,!1,!0):(_===_e&&$&384||!w&&V&16)&&Fe(A,h,g),v&&be(u)}(he&&(ae=M&&M.onVnodeUnmounted)||Q)&&He(()=>{ae&&Je(ae,h,u),Q&&Ct(u,null,h,"unmounted")},g)},be=u=>{const{type:h,el:g,anchor:v,transition:w}=u;if(h===_e){Ot(g,v);return}if(h===ls){k(u);return}const _=()=>{r(g),w&&!w.persisted&&w.afterLeave&&w.afterLeave()};if(u.shapeFlag&1&&w&&!w.persisted){const{leave:M,delayLeave:T}=w,A=()=>M(g,_);T?T(u.el,_,A):A()}else _()},Ot=(u,h)=>{let g;for(;u!==h;)g=p(u),r(u),u=g;r(h)},En=(u,h,g)=>{const{bum:v,scope:w,job:_,subTree:M,um:T,m:A,a:C,parent:V,slots:{__:$}}=u;ar(A),ar(C),v&&Tn(v),V&&B($)&&$.forEach(j=>{V.renderCache[j]=void 0}),w.stop(),_&&(_.flags|=8,ce(M,u,h,g)),T&&He(T,h),He(()=>{u.isUnmounted=!0},h),h&&h.pendingBranch&&!h.isUnmounted&&u.asyncDep&&!u.asyncResolved&&u.suspenseId===h.pendingId&&(h.deps--,h.deps===0&&h.resolve())},Fe=(u,h,g,v=!1,w=!1,_=0)=>{for(let M=_;M<u.length;M++)ce(u[M],h,g,v,w)},y=u=>{if(u.shapeFlag&6)return y(u.component.subTree);if(u.shapeFlag&128)return u.suspense.next();const h=p(u.anchor||u.el),g=h&&h[yl];return g?p(g):h};let I=!1;const O=(u,h,g)=>{u==null?h._vnode&&ce(h._vnode,null,null,!0):E(h._vnode||null,u,h,null,null,null,g),h._vnode=u,I||(I=!0,sr(),Io(),I=!1)},F={p:E,um:ce,m:Y,r:be,mt:ye,mc:Se,pc:b,pbc:X,n:y,o:e};return{render:O,hydrate:void 0,createApp:Fl(O)}}function is({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function xt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Yl(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function ei(e,t,n=!1){const s=e.children,r=t.children;if(B(s)&&B(r))for(let o=0;o<s.length;o++){const i=s[o];let l=r[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[o]=vt(r[o]),l.el=i.el),!n&&l.patchFlag!==-2&&ei(i,l)),l.type===Jn&&(l.el=i.el),l.type===tt&&!l.el&&(l.el=i.el)}}function Jl(e){const t=e.slice(),n=[0];let s,r,o,i,l;const c=e.length;for(s=0;s<c;s++){const f=e[s];if(f!==0){if(r=n[n.length-1],e[r]<f){t[s]=r,n.push(s);continue}for(o=0,i=n.length-1;o<i;)l=o+i>>1,e[n[l]]<f?o=l+1:i=l;f<e[n[o]]&&(o>0&&(t[s]=n[o-1]),n[o]=s)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function ti(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:ti(t)}function ar(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Ql=Symbol.for("v-scx"),Xl=()=>lt(Ql);function Mn(e,t,n){return ni(e,t,n)}function ni(e,t,n=de){const{immediate:s,deep:r,flush:o,once:i}=n,l=we({},n),c=t&&s||!t&&o!=="post";let f;if(yn){if(o==="sync"){const m=Xl();f=m.__watcherHandles||(m.__watcherHandles=[])}else if(!c){const m=()=>{};return m.stop=et,m.resume=et,m.pause=et,m}}const a=Pe;l.call=(m,S,E)=>Ke(m,a,S,E);let d=!1;o==="post"?l.scheduler=m=>{He(m,a&&a.suspense)}:o!=="sync"&&(d=!0,l.scheduler=(m,S)=>{S?m():Us(m)}),l.augmentJob=m=>{t&&(m.flags|=4),d&&(m.flags|=2,a&&(m.id=a.uid,m.i=a))};const p=hl(e,t,l);return yn&&(f?f.push(p):c&&p()),p}function Zl(e,t,n){const s=this.proxy,r=ve(e)?e.includes(".")?si(s,e):()=>s[e]:e.bind(s,s);let o;W(t)?o=t:(o=t.handler,n=t);const i=Sn(this),l=ni(r,o.bind(s),n);return i(),l}function si(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const ec=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${bt(t)}Modifiers`]||e[`${Et(t)}Modifiers`];function tc(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||de;let r=n;const o=t.startsWith("update:"),i=o&&ec(s,t.slice(7));i&&(i.trim&&(r=n.map(a=>ve(a)?a.trim():a)),i.number&&(r=n.map(ms)));let l,c=s[l=Zn(t)]||s[l=Zn(bt(t))];!c&&o&&(c=s[l=Zn(Et(t))]),c&&Ke(c,e,6,r);const f=s[l+"Once"];if(f){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Ke(f,e,6,r)}}function ri(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const o=e.emits;let i={},l=!1;if(!W(e)){const c=f=>{const a=ri(f,t,!0);a&&(l=!0,we(i,a))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!o&&!l?(ge(e)&&s.set(e,null),null):(B(o)?o.forEach(c=>i[c]=null):we(i,o),ge(e)&&s.set(e,i),i)}function Yn(e,t){return!e||!Fn(t)?!1:(t=t.slice(2).replace(/Once$/,""),ie(e,t[0].toLowerCase()+t.slice(1))||ie(e,Et(t))||ie(e,t))}function ur(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[o],slots:i,attrs:l,emit:c,render:f,renderCache:a,props:d,data:p,setupState:m,ctx:S,inheritAttrs:E}=e,N=zn(e);let H,P;try{if(n.shapeFlag&4){const k=r||s,q=k;H=Ze(f.call(q,k,a,d,m,p,S)),P=l}else{const k=t;H=Ze(k.length>1?k(d,{attrs:l,slots:i,emit:c}):k(d,null)),P=t.props?l:nc(l)}}catch(k){cn.length=0,Wn(k,e,1),H=fe(tt)}let D=H;if(P&&E!==!1){const k=Object.keys(P),{shapeFlag:q}=D;k.length&&q&7&&(o&&k.some(Is)&&(P=sc(P,o)),D=Pt(D,P,!1,!0))}return n.dirs&&(D=Pt(D,null,!1,!0),D.dirs=D.dirs?D.dirs.concat(n.dirs):n.dirs),n.transition&&pn(D,n.transition),H=D,zn(N),H}const nc=e=>{let t;for(const n in e)(n==="class"||n==="style"||Fn(n))&&((t||(t={}))[n]=e[n]);return t},sc=(e,t)=>{const n={};for(const s in e)(!Is(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function rc(e,t,n){const{props:s,children:r,component:o}=e,{props:i,children:l,patchFlag:c}=t,f=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return s?fr(s,i,f):!!i;if(c&8){const a=t.dynamicProps;for(let d=0;d<a.length;d++){const p=a[d];if(i[p]!==s[p]&&!Yn(f,p))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:s===i?!1:s?i?fr(s,i,f):!0:!!i;return!1}function fr(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const o=s[r];if(t[o]!==e[o]&&!Yn(n,o))return!0}return!1}function oc({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const oi=e=>e.__isSuspense;function ic(e,t){t&&t.pendingBranch?B(e)?t.effects.push(...e):t.effects.push(e):ml(e)}const _e=Symbol.for("v-fgt"),Jn=Symbol.for("v-txt"),tt=Symbol.for("v-cmt"),ls=Symbol.for("v-stc"),cn=[];let De=null;function J(e=!1){cn.push(De=e?null:[])}function lc(){cn.pop(),De=cn[cn.length-1]||null}let gn=1;function dr(e,t=!1){gn+=e,e<0&&De&&t&&(De.hasOnce=!0)}function ii(e){return e.dynamicChildren=gn>0?De||Ht:null,lc(),gn>0&&De&&De.push(e),e}function se(e,t,n,s,r,o){return ii(R(e,t,n,s,r,o,!0))}function mn(e,t,n,s,r){return ii(fe(e,t,n,s,r,!0))}function vn(e){return e?e.__v_isVNode===!0:!1}function Lt(e,t){return e.type===t.type&&e.key===t.key}const li=({key:e})=>e??null,On=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?ve(e)||Me(e)||W(e)?{i:Ae,r:e,k:t,f:!!n}:e:null);function R(e,t=null,n=null,s=0,r=null,o=e===_e?0:1,i=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&li(t),ref:t&&On(t),scopeId:Lo,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Ae};return l?(qs(c,n),o&128&&e.normalize(c)):n&&(c.shapeFlag|=ve(n)?8:16),gn>0&&!i&&De&&(c.patchFlag>0||o&6)&&c.patchFlag!==32&&De.push(c),c}const fe=cc;function cc(e,t=null,n=null,s=0,r=null,o=!1){if((!e||e===$l)&&(e=tt),vn(e)){const l=Pt(e,t,!0);return n&&qs(l,n),gn>0&&!o&&De&&(l.shapeFlag&6?De[De.indexOf(e)]=l:De.push(l)),l.patchFlag=-2,l}if(_c(e)&&(e=e.__vccOpts),t){t=ac(t);let{class:l,style:c}=t;l&&!ve(l)&&(t.class=wt(l)),ge(c)&&(Bs(c)&&!B(c)&&(c=we({},c)),t.style=zs(c))}const i=ve(e)?1:oi(e)?128:_l(e)?64:ge(e)?4:W(e)?2:0;return R(e,t,n,s,r,i,o,!0)}function ac(e){return e?Bs(e)||qo(e)?we({},e):e:null}function Pt(e,t,n=!1,s=!1){const{props:r,ref:o,patchFlag:i,children:l,transition:c}=e,f=t?uc(r||{},t):r,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:f,key:f&&li(f),ref:t&&t.ref?n&&o?B(o)?o.concat(On(t)):[o,On(t)]:On(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==_e?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Pt(e.ssContent),ssFallback:e.ssFallback&&Pt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&s&&pn(a,c.clone(a)),a}function te(e=" ",t=0){return fe(Jn,null,e,t)}function Kt(e="",t=!1){return t?(J(),mn(tt,null,e)):fe(tt,null,e)}function Ze(e){return e==null||typeof e=="boolean"?fe(tt):B(e)?fe(_e,null,e.slice()):vn(e)?vt(e):fe(Jn,null,String(e))}function vt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Pt(e)}function qs(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(B(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),qs(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!qo(t)?t._ctx=Ae:r===3&&Ae&&(Ae.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else W(t)?(t={default:t,_ctx:Ae},n=32):(t=String(t),s&64?(n=16,t=[te(t)]):n=8);e.children=t,e.shapeFlag|=n}function uc(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=wt([t.class,s.class]));else if(r==="style")t.style=zs([t.style,s.style]);else if(Fn(r)){const o=t[r],i=s[r];i&&o!==i&&!(B(o)&&o.includes(i))&&(t[r]=o?[].concat(o,i):i)}else r!==""&&(t[r]=s[r])}return t}function Je(e,t,n,s=null){Ke(e,t,7,[n,s])}const fc=Ko();let dc=0;function hc(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||fc,o={uid:dc++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new ao(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Jo(s,r),emitsOptions:ri(s,r),emit:null,emitted:null,propsDefaults:de,inheritAttrs:s.inheritAttrs,ctx:de,data:de,props:de,attrs:de,slots:de,refs:de,setupState:de,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=tc.bind(null,o),e.ce&&e.ce(o),o}let Pe=null;const pc=()=>Pe||Ae;let Hn,Rs;{const e=Bn(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),o=>{r.length>1?r.forEach(i=>i(o)):r[0](o)}};Hn=t("__VUE_INSTANCE_SETTERS__",n=>Pe=n),Rs=t("__VUE_SSR_SETTERS__",n=>yn=n)}const Sn=e=>{const t=Pe;return Hn(e),e.scope.on(),()=>{e.scope.off(),Hn(t)}},hr=()=>{Pe&&Pe.scope.off(),Hn(null)};function ci(e){return e.vnode.shapeFlag&4}let yn=!1;function gc(e,t=!1,n=!1){t&&Rs(t);const{props:s,children:r}=e.vnode,o=ci(e);jl(e,s,o,t),Kl(e,r,n||t);const i=o?mc(e,t):void 0;return t&&Rs(!1),i}function mc(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Il);const{setup:s}=n;if(s){ct();const r=e.setupContext=s.length>1?yc(e):null,o=Sn(e),i=wn(s,e,0,[e.props,r]),l=no(i);if(at(),o(),(l||e.sp)&&!Bt(e)&&No(e),l){if(i.then(hr,hr),t)return i.then(c=>{pr(e,c)}).catch(c=>{Wn(c,e,0)});e.asyncDep=i}else pr(e,i)}else ai(e)}function pr(e,t,n){W(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ge(t)&&(e.setupState=Po(t)),ai(e)}function ai(e,t,n){const s=e.type;e.render||(e.render=s.render||et);{const r=Sn(e);ct();try{kl(e)}finally{at(),r()}}}const vc={get(e,t){return Te(e,"get",""),e[t]}};function yc(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,vc),slots:e.slots,emit:e.emit,expose:t}}function Qn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Po(Ao(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in ln)return ln[n](e)},has(t,n){return n in t||n in ln}})):e.proxy}function _c(e){return W(e)&&"__vccOpts"in e}const Be=(e,t)=>fl(e,t,yn);function ui(e,t,n){const s=arguments.length;return s===2?ge(t)&&!B(t)?vn(t)?fe(e,null,[t]):fe(e,t):fe(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&vn(n)&&(n=[n]),fe(e,t,n))}const bc="3.5.17";/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let As;const gr=typeof window<"u"&&window.trustedTypes;if(gr)try{As=gr.createPolicy("vue",{createHTML:e=>e})}catch{}const fi=As?e=>As.createHTML(e):e=>e,wc="http://www.w3.org/2000/svg",Sc="http://www.w3.org/1998/Math/MathML",rt=typeof document<"u"?document:null,mr=rt&&rt.createElement("template"),Ec={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?rt.createElementNS(wc,e):t==="mathml"?rt.createElementNS(Sc,e):n?rt.createElement(e,{is:n}):rt.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>rt.createTextNode(e),createComment:e=>rt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>rt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,o){const i=n?n.previousSibling:t.lastChild;if(r&&(r===o||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===o||!(r=r.nextSibling)););else{mr.innerHTML=fi(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=mr.content;if(s==="svg"||s==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},ht="transition",Qt="animation",Wt=Symbol("_vtc"),di={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Cc=we({},wl,di),Rt=(e,t=[])=>{B(e)?e.forEach(n=>n(...t)):e&&e(...t)},vr=e=>e?B(e)?e.some(t=>t.length>1):e.length>1:!1;function xc(e){const t={};for(const L in e)L in di||(t[L]=e[L]);if(e.css===!1)return t;const{name:n="v",type:s,duration:r,enterFromClass:o=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:l=`${n}-enter-to`,appearFromClass:c=o,appearActiveClass:f=i,appearToClass:a=l,leaveFromClass:d=`${n}-leave-from`,leaveActiveClass:p=`${n}-leave-active`,leaveToClass:m=`${n}-leave-to`}=e,S=Rc(r),E=S&&S[0],N=S&&S[1],{onBeforeEnter:H,onEnter:P,onEnterCancelled:D,onLeave:k,onLeaveCancelled:q,onBeforeAppear:le=H,onAppear:re=P,onAppearCancelled:Se=D}=t,K=(L,ee,ye,Oe)=>{L._enterCancelled=Oe,gt(L,ee?a:l),gt(L,ee?f:i),ye&&ye()},X=(L,ee)=>{L._isLeaving=!1,gt(L,d),gt(L,m),gt(L,p),ee&&ee()},Z=L=>(ee,ye)=>{const Oe=L?re:P,me=()=>K(ee,L,ye);Rt(Oe,[ee,me]),yr(()=>{gt(ee,L?c:o),Qe(ee,L?a:l),vr(Oe)||_r(ee,s,E,me)})};return we(t,{onBeforeEnter(L){Rt(H,[L]),Qe(L,o),Qe(L,i)},onBeforeAppear(L){Rt(le,[L]),Qe(L,c),Qe(L,f)},onEnter:Z(!1),onAppear:Z(!0),onLeave(L,ee){L._isLeaving=!0;const ye=()=>X(L,ee);Qe(L,d),L._enterCancelled?(Qe(L,p),Ts()):(Ts(),Qe(L,p)),yr(()=>{L._isLeaving&&(gt(L,d),Qe(L,m),vr(k)||_r(L,s,N,ye))}),Rt(k,[L,ye])},onEnterCancelled(L){K(L,!1,void 0,!0),Rt(D,[L])},onAppearCancelled(L){K(L,!0,void 0,!0),Rt(Se,[L])},onLeaveCancelled(L){X(L),Rt(q,[L])}})}function Rc(e){if(e==null)return null;if(ge(e))return[cs(e.enter),cs(e.leave)];{const t=cs(e);return[t,t]}}function cs(e){return ki(e)}function Qe(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[Wt]||(e[Wt]=new Set)).add(t)}function gt(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.remove(s));const n=e[Wt];n&&(n.delete(t),n.size||(e[Wt]=void 0))}function yr(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Ac=0;function _r(e,t,n,s){const r=e._endId=++Ac,o=()=>{r===e._endId&&s()};if(n!=null)return setTimeout(o,n);const{type:i,timeout:l,propCount:c}=hi(e,t);if(!i)return s();const f=i+"end";let a=0;const d=()=>{e.removeEventListener(f,p),o()},p=m=>{m.target===e&&++a>=c&&d()};setTimeout(()=>{a<c&&d()},l+1),e.addEventListener(f,p)}function hi(e,t){const n=window.getComputedStyle(e),s=S=>(n[S]||"").split(", "),r=s(`${ht}Delay`),o=s(`${ht}Duration`),i=br(r,o),l=s(`${Qt}Delay`),c=s(`${Qt}Duration`),f=br(l,c);let a=null,d=0,p=0;t===ht?i>0&&(a=ht,d=i,p=o.length):t===Qt?f>0&&(a=Qt,d=f,p=c.length):(d=Math.max(i,f),a=d>0?i>f?ht:Qt:null,p=a?a===ht?o.length:c.length:0);const m=a===ht&&/\b(transform|all)(,|$)/.test(s(`${ht}Property`).toString());return{type:a,timeout:d,propCount:p,hasTransform:m}}function br(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,s)=>wr(n)+wr(e[s])))}function wr(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function Ts(){return document.body.offsetHeight}function Tc(e,t,n){const s=e[Wt];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Sr=Symbol("_vod"),Pc=Symbol("_vsh"),Mc=Symbol(""),Oc=/(^|;)\s*display\s*:/;function $c(e,t,n){const s=e.style,r=ve(n);let o=!1;if(n&&!r){if(t)if(ve(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&$n(s,l,"")}else for(const i in t)n[i]==null&&$n(s,i,"");for(const i in n)i==="display"&&(o=!0),$n(s,i,n[i])}else if(r){if(t!==n){const i=s[Mc];i&&(n+=";"+i),s.cssText=n,o=Oc.test(n)}}else t&&e.removeAttribute("style");Sr in e&&(e[Sr]=o?s.display:"",e[Pc]&&(s.display="none"))}const Er=/\s*!important$/;function $n(e,t,n){if(B(n))n.forEach(s=>$n(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=Ic(e,t);Er.test(n)?e.setProperty(Et(s),n.replace(Er,""),"important"):e[s]=n}}const Cr=["Webkit","Moz","ms"],as={};function Ic(e,t){const n=as[t];if(n)return n;let s=bt(t);if(s!=="filter"&&s in e)return as[t]=s;s=oo(s);for(let r=0;r<Cr.length;r++){const o=Cr[r]+s;if(o in e)return as[t]=o}return t}const xr="http://www.w3.org/1999/xlink";function Rr(e,t,n,s,r,o=Fi(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(xr,t.slice(6,t.length)):e.setAttributeNS(xr,t,n):n==null||o&&!io(n)?e.removeAttribute(t):e.setAttribute(t,o?"":ut(n)?String(n):n)}function Ar(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?fi(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,c=n==null?e.type==="checkbox"?"on":"":String(n);(l!==c||!("_value"in e))&&(e.value=c),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=io(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(r||t)}function zt(e,t,n,s){e.addEventListener(t,n,s)}function kc(e,t,n,s){e.removeEventListener(t,n,s)}const Tr=Symbol("_vei");function Lc(e,t,n,s,r=null){const o=e[Tr]||(e[Tr]={}),i=o[t];if(s&&i)i.value=s;else{const[l,c]=zc(t);if(s){const f=o[t]=Dc(s,r);zt(e,l,f,c)}else i&&(kc(e,l,i,c),o[t]=void 0)}}const Pr=/(?:Once|Passive|Capture)$/;function zc(e){let t;if(Pr.test(e)){t={};let s;for(;s=e.match(Pr);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Et(e.slice(2)),t]}let us=0;const Nc=Promise.resolve(),Hc=()=>us||(Nc.then(()=>us=0),us=Date.now());function Dc(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;Ke(Fc(s,n.value),t,5,[s])};return n.value=e,n.attached=Hc(),n}function Fc(e,t){if(B(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const Mr=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,jc=(e,t,n,s,r,o)=>{const i=r==="svg";t==="class"?Tc(e,s,i):t==="style"?$c(e,n,s):Fn(t)?Is(t)||Lc(e,t,n,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Vc(e,t,s,i))?(Ar(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Rr(e,t,s,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ve(s))?Ar(e,bt(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),Rr(e,t,s,i))};function Vc(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&Mr(t)&&W(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return Mr(t)&&ve(n)?!1:t in e}const pi=new WeakMap,gi=new WeakMap,Dn=Symbol("_moveCb"),Or=Symbol("_enterCb"),Bc=e=>(delete e.props.mode,e),Uc=Bc({name:"TransitionGroup",props:we({},Cc,{tag:String,moveClass:String}),setup(e,{slots:t}){const n=pc(),s=bl();let r,o;return Fo(()=>{if(!r.length)return;const i=e.moveClass||`${e.name||"v"}-move`;if(!Yc(r[0].el,n.vnode.el,i)){r=[];return}r.forEach(Wc),r.forEach(Gc);const l=r.filter(qc);Ts(),l.forEach(c=>{const f=c.el,a=f.style;Qe(f,i),a.transform=a.webkitTransform=a.transitionDuration="";const d=f[Dn]=p=>{p&&p.target!==f||(!p||/transform$/.test(p.propertyName))&&(f.removeEventListener("transitionend",d),f[Dn]=null,gt(f,i))};f.addEventListener("transitionend",d)}),r=[]}),()=>{const i=ne(e),l=xc(i);let c=i.tag||_e;if(r=[],o)for(let f=0;f<o.length;f++){const a=o[f];a.el&&a.el instanceof Element&&(r.push(a),pn(a,ws(a,l,s,n)),pi.set(a,a.el.getBoundingClientRect()))}o=t.default?zo(t.default()):[];for(let f=0;f<o.length;f++){const a=o[f];a.key!=null&&pn(a,ws(a,l,s,n))}return fe(c,null,o)}}}),Kc=Uc;function Wc(e){const t=e.el;t[Dn]&&t[Dn](),t[Or]&&t[Or]()}function Gc(e){gi.set(e,e.el.getBoundingClientRect())}function qc(e){const t=pi.get(e),n=gi.get(e),s=t.left-n.left,r=t.top-n.top;if(s||r){const o=e.el.style;return o.transform=o.webkitTransform=`translate(${s}px,${r}px)`,o.transitionDuration="0s",e}}function Yc(e,t,n){const s=e.cloneNode(),r=e[Wt];r&&r.forEach(l=>{l.split(/\s+/).forEach(c=>c&&s.classList.remove(c))}),n.split(/\s+/).forEach(l=>l&&s.classList.add(l)),s.style.display="none";const o=t.nodeType===1?t:t.parentNode;o.appendChild(s);const{hasTransform:i}=hi(s);return o.removeChild(s),i}const $r=e=>{const t=e.props["onUpdate:modelValue"]||!1;return B(t)?n=>Tn(t,n):t};function Jc(e){e.target.composing=!0}function Ir(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const fs=Symbol("_assign"),Qc={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e[fs]=$r(r);const o=s||r.props&&r.props.type==="number";zt(e,t?"change":"input",i=>{if(i.target.composing)return;let l=e.value;n&&(l=l.trim()),o&&(l=ms(l)),e[fs](l)}),n&&zt(e,"change",()=>{e.value=e.value.trim()}),t||(zt(e,"compositionstart",Jc),zt(e,"compositionend",Ir),zt(e,"change",Ir))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:r,number:o}},i){if(e[fs]=$r(i),e.composing)return;const l=(o||e.type==="number")&&!/^0\d/.test(e.value)?ms(e.value):e.value,c=t??"";l!==c&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||r&&e.value.trim()===c)||(e.value=c))}},Xc={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},Zc=(e,t)=>{const n=e._withKeys||(e._withKeys={}),s=t.join(".");return n[s]||(n[s]=r=>{if(!("key"in r))return;const o=Et(r.key);if(t.some(i=>i===o||Xc[i]===o))return e(r)})},ea=we({patchProp:jc},Ec);let kr;function ta(){return kr||(kr=Gl(ea))}const na=(...e)=>{const t=ta().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=ra(s);if(!r)return;const o=t._component;!W(o)&&!o.render&&!o.template&&(o.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=n(r,!1,sa(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function sa(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function ra(e){return ve(e)?document.querySelector(e):e}/*!
 * pinia v3.0.3
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const oa=Symbol();var Lr;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(Lr||(Lr={}));function ia(){const e=ji(!0),t=e.run(()=>Ce({}));let n=[],s=[];const r=Ao({install(o){r._a=o,o.provide(oa,r),o.config.globalProperties.$pinia=r,s.forEach(i=>n.push(i)),s=[]},use(o){return this._a?n.push(o):s.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}const la={class:"game-container"},ca={class:"score-container"},aa={class:"score-box"},ua={class:"score-value"},fa={class:"score-box"},da={class:"score-value"},ha={class:"grid-container"},pa={key:0,class:"game-over-overlay"},ga={class:"game-over-message"},ma={key:0,class:"game-over-actions"},va={key:1,class:"score-submission"},ya={class:"submission-actions"},_a=["disabled"],ba={key:0,class:"submission-error"},wa={key:1,class:"submission-success"},Sa="http://localhost:3001/api",Ea=Mt({__name:"GameBoard",setup(e){const t=Ce([]),n=Ce([]),s=Ce(0),r=Ce(0),o=Ce(!1);let i=1,l=0,c=0;const f=Ce(!1),a=Ce(""),d=Ce(!1),p=Ce(""),m=Ce(!1),S=(x=!1)=>{Oe(),!(!x&&L())&&(t.value=Array(4).fill(null).map(()=>Array(4).fill(0)),n.value=[],s.value=0,o.value=!1,i=1,E(),E(),Z())},E=()=>{const x=[];for(let b=0;b<4;b++)for(let z=0;z<4;z++)t.value[b][z]===0&&x.push({row:b,col:z});if(x.length>0){const b=x[Math.floor(Math.random()*x.length)],z=Math.random()<.9?2:4;t.value[b.row][b.col]=z,n.value.push({id:i++,value:z,row:b.row,col:b.col})}},N=()=>{let x=!1;const b=t.value.map(z=>[...z]);for(let z=0;z<4;z++){const G=b[z].filter(be=>be!==0),Y=[];let ce=0;for(;ce<G.length;)ce<G.length-1&&G[ce]===G[ce+1]?(Y.push(G[ce]*2),s.value+=G[ce]*2,ce+=2):(Y.push(G[ce]),ce++);for(;Y.length<4;)Y.push(0);for(let be=0;be<4;be++)b[z][be]!==Y[be]&&(x=!0);b[z]=Y}return x?(t.value=b,D(),!0):!1},H=x=>{const b=x.length,z=Array(b).fill(null).map(()=>Array(b).fill(0));for(let G=0;G<b;G++)for(let Y=0;Y<b;Y++)z[Y][b-1-G]=x[G][Y];return z},P=x=>{let b=!1,z=0;switch(x){case"left":z=0;break;case"up":z=1;break;case"right":z=2;break;case"down":z=3;break}let G=t.value;for(let Y=0;Y<z;Y++)G=H(G);t.value=G,b=N();for(let Y=0;Y<(4-z)%4;Y++)t.value=H(t.value);return b},D=()=>{const x=[];for(let b=0;b<4;b++)for(let z=0;z<4;z++)t.value[b][z]!==0&&x.push({id:i++,value:t.value[b][z],row:b,col:z});n.value=x},k=()=>{for(let x=0;x<4;x++)for(let b=0;b<4;b++)if(t.value[x][b]===0)return!1;for(let x=0;x<4;x++)for(let b=0;b<4;b++){const z=t.value[x][b];if(b<3&&t.value[x][b+1]===z||x<3&&t.value[x+1][b]===z)return!1}return!0},q=x=>{if(o.value)return;let b=!1;switch(x.key){case"ArrowLeft":x.preventDefault(),b=P("left");break;case"ArrowUp":x.preventDefault(),b=P("up");break;case"ArrowRight":x.preventDefault(),b=P("right");break;case"ArrowDown":x.preventDefault(),b=P("down");break}b&&(E(),me(),Z(),k()&&(o.value=!0,Z()))},le=x=>{x.touches.length===1&&(l=x.touches[0].clientX,c=x.touches[0].clientY)},re=x=>{if(o.value||x.changedTouches.length!==1)return;const b=x.changedTouches[0].clientX,z=x.changedTouches[0].clientY,G=b-l,Y=z-c,ce=30;if(Math.abs(G)<ce&&Math.abs(Y)<ce)return;let be=!1;Math.abs(G)>Math.abs(Y)?G>0?be=P("right"):be=P("left"):Y>0?be=P("down"):be=P("up"),be&&(E(),me(),Z(),k()&&(o.value=!0,Z()))},Se=(x,b)=>{const z=`${x}-${JSON.stringify(b)}-${Date.now()}`;let G=0;for(let Y=0;Y<z.length;Y++){const ce=z.charCodeAt(Y);G=(G<<5)-G+ce,G=G&G}return Math.abs(G).toString(16)},K=async()=>{if(!a.value.trim()){p.value="请输入昵称";return}d.value=!0,p.value="",m.value=!1;try{const x=Se(s.value,t.value),b=await fetch(`${Sa}/scores`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({playerName:a.value.trim(),score:s.value,validationPayload:x})}),z=await b.json();if(!b.ok)throw new Error(z.error||"提交失败");m.value=!0,setTimeout(()=>{f.value=!1,m.value=!1},2e3)}catch(x){p.value=x instanceof Error?x.message:"网络错误"}finally{d.value=!1}},X=()=>{ee(),f.value=!1,a.value="",p.value="",m.value=!1,S(!0)},Z=()=>{const x={board:t.value,tiles:n.value,score:s.value,isGameOver:o.value,nextTileId:i};localStorage.setItem("game2048-state",JSON.stringify(x))},L=()=>{const x=localStorage.getItem("game2048-state");if(x)try{const b=JSON.parse(x);return t.value=b.board,n.value=b.tiles,s.value=b.score,o.value=b.isGameOver,i=b.nextTileId,!0}catch(b){return console.error("Failed to load game state:",b),!1}return!1},ee=()=>{localStorage.removeItem("game2048-state")},ye=()=>{localStorage.setItem("game2048-best-score",r.value.toString())},Oe=()=>{const x=localStorage.getItem("game2048-best-score");x&&(r.value=parseInt(x,10))},me=()=>{s.value>r.value&&(r.value=s.value,ye())};return qn(()=>{S(),window.addEventListener("keydown",q)}),Ks(()=>{window.removeEventListener("keydown",q)}),(x,b)=>(J(),se("div",la,[R("div",ca,[R("div",aa,[b[3]||(b[3]=R("div",{class:"score-label"},"分数",-1)),R("div",ua,Ee(s.value),1)]),R("div",fa,[b[4]||(b[4]=R("div",{class:"score-label"},"最高分",-1)),R("div",da,Ee(r.value),1)])]),R("div",{class:"game-board",onTouchstart:le,onTouchend:re},[R("div",ha,[(J(),se(_e,null,on(4,z=>R("div",{class:"grid-row",key:z},[(J(),se(_e,null,on(4,G=>R("div",{class:"grid-cell",key:G})),64))])),64))]),fe(Kc,{name:"tile",tag:"div",class:"tile-container"},{default:xe(()=>[(J(!0),se(_e,null,on(n.value,z=>(J(),se("div",{key:z.id,class:wt(["tile",`tile-${z.value}`,`tile-position-${z.row}-${z.col}`])},Ee(z.value),3))),128))]),_:1})],32),R("div",{class:"game-controls"},[R("button",{onClick:X,class:"restart-btn"},"新游戏"),b[5]||(b[5]=R("p",{class:"auto-save-hint"},"游戏进度自动保存",-1))]),o.value?(J(),se("div",pa,[R("div",ga,[b[6]||(b[6]=R("h2",null,"游戏结束!",-1)),R("p",null,"最终分数: "+Ee(s.value),1),f.value?(J(),se("div",va,[vl(R("input",{"onUpdate:modelValue":b[1]||(b[1]=z=>a.value=z),type:"text",placeholder:"输入你的昵称",maxlength:"20",class:"player-name-input",onKeyup:Zc(K,["enter"])},null,544),[[Qc,a.value]]),R("div",ya,[R("button",{onClick:K,disabled:d.value||!a.value.trim(),class:"submit-btn"},Ee(d.value?"提交中...":"提交"),9,_a),R("button",{onClick:b[2]||(b[2]=z=>f.value=!1),class:"cancel-btn"},"取消")]),p.value?(J(),se("div",ba,Ee(p.value),1)):Kt("",!0),m.value?(J(),se("div",wa,"分数提交成功！")):Kt("",!0)])):(J(),se("div",ma,[R("button",{onClick:b[0]||(b[0]=z=>f.value=!0),class:"submit-score-btn"},"提交分数"),R("button",{onClick:X,class:"restart-btn"},"再来一局")]))])])):Kt("",!0)]))}}),dt=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n},Ca=dt(Ea,[["__scopeId","data-v-62bd1327"]]),xa={class:"leaderboard-container"},Ra={class:"period-selector"},Aa=["onClick"],Ta={key:0,class:"loading"},Pa={key:1,class:"error"},Ma={key:2,class:"leaderboard-list"},Oa={key:0,class:"empty-state"},$a={key:1},Ia={class:"rank"},ka={key:0,class:"medal"},La={key:1,class:"rank-number"},za={class:"player-info"},Na={class:"player-name"},Ha={class:"play-time"},Da={class:"score"},Fa={key:3,class:"stats-section"},ja={class:"stats-grid"},Va={class:"stat-item"},Ba={class:"stat-value"},Ua={class:"stat-item"},Ka={class:"stat-value"},Wa={class:"stat-item"},Ga={class:"stat-value"},qa={class:"stat-item"},Ya={class:"stat-value"},zr="http://localhost:3001/api",Ja=Mt({__name:"Leaderboard",setup(e){const t=Ce([]),n=Ce(null),s=Ce(!1),r=Ce(""),o=Ce("alltime"),i=[{value:"alltime",label:"总榜"},{value:"weekly",label:"周榜"},{value:"daily",label:"日榜"}],l=async()=>{s.value=!0,r.value="";try{const S=await fetch(`${zr}/leaderboard?period=${o.value}&limit=10`);if(!S.ok)throw new Error("获取排行榜失败");const E=await S.json();t.value=E.leaderboard||[]}catch(S){r.value=S instanceof Error?S.message:"网络错误",console.error("Failed to fetch leaderboard:",S)}finally{s.value=!1}},c=async()=>{try{const S=await fetch(`${zr}/stats`);if(!S.ok)throw new Error("获取统计信息失败");const E=await S.json();n.value=E}catch(S){console.error("Failed to fetch stats:",S)}},f=S=>{o.value=S,l()},a=S=>S===1?"first-place":S===2?"second-place":S===3?"third-place":"",d=S=>["🥇","🥈","🥉"][S-1]||"",p=S=>S.toLocaleString(),m=S=>{const E=new Date(S),H=new Date().getTime()-E.getTime(),P=Math.floor(H/(1e3*60*60*24));return P===0?"今天":P===1?"昨天":P<7?`${P}天前`:E.toLocaleDateString("zh-CN")};return qn(()=>{l(),c()}),(S,E)=>(J(),se("div",xa,[E[5]||(E[5]=R("h2",null,"排行榜",-1)),R("div",Ra,[(J(),se(_e,null,on(i,N=>R("button",{key:N.value,class:wt(["period-btn",{active:o.value===N.value}]),onClick:H=>f(N.value)},Ee(N.label),11,Aa)),64))]),s.value?(J(),se("div",Ta," 加载中... ")):r.value?(J(),se("div",Pa,[te(Ee(r.value)+" ",1),R("button",{onClick:l,class:"retry-btn"},"重试")])):(J(),se("div",Ma,[t.value.length===0?(J(),se("div",Oa," 暂无排行榜数据 ")):(J(),se("div",$a,[(J(!0),se(_e,null,on(t.value,N=>(J(),se("div",{key:N.rank,class:wt(["leaderboard-entry",a(N.rank)])},[R("div",Ia,[N.rank<=3?(J(),se("span",ka,Ee(d(N.rank)),1)):(J(),se("span",La,Ee(N.rank),1))]),R("div",za,[R("div",Na,Ee(N.playerName),1),R("div",Ha,Ee(m(N.createdAt)),1)]),R("div",Da,Ee(p(N.score)),1)],2))),128))]))])),n.value?(J(),se("div",Fa,[E[4]||(E[4]=R("h3",null,"游戏统计",-1)),R("div",ja,[R("div",Va,[R("div",Ba,Ee(n.value.totalGames),1),E[0]||(E[0]=R("div",{class:"stat-label"},"总游戏数",-1))]),R("div",Ua,[R("div",Ka,Ee(p(n.value.highestScore)),1),E[1]||(E[1]=R("div",{class:"stat-label"},"最高分",-1))]),R("div",Wa,[R("div",Ga,Ee(p(n.value.averageScore)),1),E[2]||(E[2]=R("div",{class:"stat-label"},"平均分",-1))]),R("div",qa,[R("div",Ya,Ee(n.value.uniquePlayers),1),E[3]||(E[3]=R("div",{class:"stat-label"},"玩家数",-1))])])])):Kt("",!0)]))}}),Qa=dt(Ja,[["__scopeId","data-v-a2ff5ee1"]]),Xa={id:"app"},Za={class:"main-nav"},eu=Mt({__name:"App",setup(e){const t=Ce("game"),n=()=>{t.value="game"},s=()=>{t.value="leaderboard"};return(r,o)=>(J(),se("div",Xa,[R("header",null,[o[0]||(o[0]=R("h1",null,"2048 游戏",-1)),o[1]||(o[1]=R("p",null,"使用方向键移动方块，合并相同数字达到2048！",-1)),R("nav",Za,[R("button",{class:wt(["nav-btn",{active:t.value==="game"}]),onClick:n}," 游戏 ",2),R("button",{class:wt(["nav-btn",{active:t.value==="leaderboard"}]),onClick:s}," 排行榜 ",2)])]),R("main",null,[t.value==="game"?(J(),mn(Ca,{key:0})):Kt("",!0),t.value==="leaderboard"?(J(),mn(Qa,{key:1})):Kt("",!0)])]))}}),tu=dt(eu,[["__scopeId","data-v-e1daa193"]]),nu="modulepreload",su=function(e){return"/"+e},Nr={},ru=function(t,n,s){let r=Promise.resolve();if(n&&n.length>0){let c=function(f){return Promise.all(f.map(a=>Promise.resolve(a).then(d=>({status:"fulfilled",value:d}),d=>({status:"rejected",reason:d}))))};document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),l=i?.nonce||i?.getAttribute("nonce");r=c(n.map(f=>{if(f=su(f),f in Nr)return;Nr[f]=!0;const a=f.endsWith(".css"),d=a?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${f}"]${d}`))return;const p=document.createElement("link");if(p.rel=a?"stylesheet":nu,a||(p.as="script"),p.crossOrigin="",p.href=f,l&&p.setAttribute("nonce",l),document.head.appendChild(p),a)return new Promise((m,S)=>{p.addEventListener("load",m),p.addEventListener("error",()=>S(new Error(`Unable to preload CSS for ${f}`)))})}))}function o(i){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=i,window.dispatchEvent(l),!l.defaultPrevented)throw i}return r.then(i=>{for(const l of i||[])l.status==="rejected"&&o(l.reason);return t().catch(o)})};/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Nt=typeof document<"u";function mi(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function ou(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&mi(e.default)}const oe=Object.assign;function ds(e,t){const n={};for(const s in t){const r=t[s];n[s]=We(r)?r.map(e):e(r)}return n}const an=()=>{},We=Array.isArray,vi=/#/g,iu=/&/g,lu=/\//g,cu=/=/g,au=/\?/g,yi=/\+/g,uu=/%5B/g,fu=/%5D/g,_i=/%5E/g,du=/%60/g,bi=/%7B/g,hu=/%7C/g,wi=/%7D/g,pu=/%20/g;function Ys(e){return encodeURI(""+e).replace(hu,"|").replace(uu,"[").replace(fu,"]")}function gu(e){return Ys(e).replace(bi,"{").replace(wi,"}").replace(_i,"^")}function Ps(e){return Ys(e).replace(yi,"%2B").replace(pu,"+").replace(vi,"%23").replace(iu,"%26").replace(du,"`").replace(bi,"{").replace(wi,"}").replace(_i,"^")}function mu(e){return Ps(e).replace(cu,"%3D")}function vu(e){return Ys(e).replace(vi,"%23").replace(au,"%3F")}function yu(e){return e==null?"":vu(e).replace(lu,"%2F")}function _n(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const _u=/\/$/,bu=e=>e.replace(_u,"");function hs(e,t,n="/"){let s,r={},o="",i="";const l=t.indexOf("#");let c=t.indexOf("?");return l<c&&l>=0&&(c=-1),c>-1&&(s=t.slice(0,c),o=t.slice(c+1,l>-1?l:t.length),r=e(o)),l>-1&&(s=s||t.slice(0,l),i=t.slice(l,t.length)),s=Cu(s??t,n),{fullPath:s+(o&&"?")+o+i,path:s,query:r,hash:_n(i)}}function wu(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Hr(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Su(e,t,n){const s=t.matched.length-1,r=n.matched.length-1;return s>-1&&s===r&&Gt(t.matched[s],n.matched[r])&&Si(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Gt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Si(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Eu(e[n],t[n]))return!1;return!0}function Eu(e,t){return We(e)?Dr(e,t):We(t)?Dr(t,e):e===t}function Dr(e,t){return We(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function Cu(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),r=s[s.length-1];(r===".."||r===".")&&s.push("");let o=n.length-1,i,l;for(i=0;i<s.length;i++)if(l=s[i],l!==".")if(l==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+s.slice(i).join("/")}const pt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var bn;(function(e){e.pop="pop",e.push="push"})(bn||(bn={}));var un;(function(e){e.back="back",e.forward="forward",e.unknown=""})(un||(un={}));function xu(e){if(!e)if(Nt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),bu(e)}const Ru=/^[^#]+#/;function Au(e,t){return e.replace(Ru,"#")+t}function Tu(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const Xn=()=>({left:window.scrollX,top:window.scrollY});function Pu(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=Tu(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Fr(e,t){return(history.state?history.state.position-t:-1)+e}const Ms=new Map;function Mu(e,t){Ms.set(e,t)}function Ou(e){const t=Ms.get(e);return Ms.delete(e),t}let $u=()=>location.protocol+"//"+location.host;function Ei(e,t){const{pathname:n,search:s,hash:r}=t,o=e.indexOf("#");if(o>-1){let l=r.includes(e.slice(o))?e.slice(o).length:1,c=r.slice(l);return c[0]!=="/"&&(c="/"+c),Hr(c,"")}return Hr(n,e)+s+r}function Iu(e,t,n,s){let r=[],o=[],i=null;const l=({state:p})=>{const m=Ei(e,location),S=n.value,E=t.value;let N=0;if(p){if(n.value=m,t.value=p,i&&i===S){i=null;return}N=E?p.position-E.position:0}else s(m);r.forEach(H=>{H(n.value,S,{delta:N,type:bn.pop,direction:N?N>0?un.forward:un.back:un.unknown})})};function c(){i=n.value}function f(p){r.push(p);const m=()=>{const S=r.indexOf(p);S>-1&&r.splice(S,1)};return o.push(m),m}function a(){const{history:p}=window;p.state&&p.replaceState(oe({},p.state,{scroll:Xn()}),"")}function d(){for(const p of o)p();o=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",a)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",a,{passive:!0}),{pauseListeners:c,listen:f,destroy:d}}function jr(e,t,n,s=!1,r=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:r?Xn():null}}function ku(e){const{history:t,location:n}=window,s={value:Ei(e,n)},r={value:t.state};r.value||o(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(c,f,a){const d=e.indexOf("#"),p=d>-1?(n.host&&document.querySelector("base")?e:e.slice(d))+c:$u()+e+c;try{t[a?"replaceState":"pushState"](f,"",p),r.value=f}catch(m){console.error(m),n[a?"replace":"assign"](p)}}function i(c,f){const a=oe({},t.state,jr(r.value.back,c,r.value.forward,!0),f,{position:r.value.position});o(c,a,!0),s.value=c}function l(c,f){const a=oe({},r.value,t.state,{forward:c,scroll:Xn()});o(a.current,a,!0);const d=oe({},jr(s.value,c,null),{position:a.position+1},f);o(c,d,!1),s.value=c}return{location:s,state:r,push:l,replace:i}}function Lu(e){e=xu(e);const t=ku(e),n=Iu(e,t.state,t.location,t.replace);function s(o,i=!0){i||n.pauseListeners(),history.go(o)}const r=oe({location:"",base:e,go:s,createHref:Au.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function zu(e){return typeof e=="string"||e&&typeof e=="object"}function Ci(e){return typeof e=="string"||typeof e=="symbol"}const xi=Symbol("");var Vr;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Vr||(Vr={}));function qt(e,t){return oe(new Error,{type:e,[xi]:!0},t)}function st(e,t){return e instanceof Error&&xi in e&&(t==null||!!(e.type&t))}const Br="[^/]+?",Nu={sensitive:!1,strict:!1,start:!0,end:!0},Hu=/[.+*?^${}()[\]/\\]/g;function Du(e,t){const n=oe({},Nu,t),s=[];let r=n.start?"^":"";const o=[];for(const f of e){const a=f.length?[]:[90];n.strict&&!f.length&&(r+="/");for(let d=0;d<f.length;d++){const p=f[d];let m=40+(n.sensitive?.25:0);if(p.type===0)d||(r+="/"),r+=p.value.replace(Hu,"\\$&"),m+=40;else if(p.type===1){const{value:S,repeatable:E,optional:N,regexp:H}=p;o.push({name:S,repeatable:E,optional:N});const P=H||Br;if(P!==Br){m+=10;try{new RegExp(`(${P})`)}catch(k){throw new Error(`Invalid custom RegExp for param "${S}" (${P}): `+k.message)}}let D=E?`((?:${P})(?:/(?:${P}))*)`:`(${P})`;d||(D=N&&f.length<2?`(?:/${D})`:"/"+D),N&&(D+="?"),r+=D,m+=20,N&&(m+=-8),E&&(m+=-20),P===".*"&&(m+=-50)}a.push(m)}s.push(a)}if(n.strict&&n.end){const f=s.length-1;s[f][s[f].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const i=new RegExp(r,n.sensitive?"":"i");function l(f){const a=f.match(i),d={};if(!a)return null;for(let p=1;p<a.length;p++){const m=a[p]||"",S=o[p-1];d[S.name]=m&&S.repeatable?m.split("/"):m}return d}function c(f){let a="",d=!1;for(const p of e){(!d||!a.endsWith("/"))&&(a+="/"),d=!1;for(const m of p)if(m.type===0)a+=m.value;else if(m.type===1){const{value:S,repeatable:E,optional:N}=m,H=S in f?f[S]:"";if(We(H)&&!E)throw new Error(`Provided param "${S}" is an array but it is not repeatable (* or + modifiers)`);const P=We(H)?H.join("/"):H;if(!P)if(N)p.length<2&&(a.endsWith("/")?a=a.slice(0,-1):d=!0);else throw new Error(`Missing required param "${S}"`);a+=P}}return a||"/"}return{re:i,score:s,keys:o,parse:l,stringify:c}}function Fu(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Ri(e,t){let n=0;const s=e.score,r=t.score;for(;n<s.length&&n<r.length;){const o=Fu(s[n],r[n]);if(o)return o;n++}if(Math.abs(r.length-s.length)===1){if(Ur(s))return 1;if(Ur(r))return-1}return r.length-s.length}function Ur(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const ju={type:0,value:""},Vu=/[a-zA-Z0-9_]/;function Bu(e){if(!e)return[[]];if(e==="/")return[[ju]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${f}": ${m}`)}let n=0,s=n;const r=[];let o;function i(){o&&r.push(o),o=[]}let l=0,c,f="",a="";function d(){f&&(n===0?o.push({type:0,value:f}):n===1||n===2||n===3?(o.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${f}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:f,regexp:a,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),f="")}function p(){f+=c}for(;l<e.length;){if(c=e[l++],c==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:c==="/"?(f&&d(),i()):c===":"?(d(),n=1):p();break;case 4:p(),n=s;break;case 1:c==="("?n=2:Vu.test(c)?p():(d(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--);break;case 2:c===")"?a[a.length-1]=="\\"?a=a.slice(0,-1)+c:n=3:a+=c;break;case 3:d(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--,a="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${f}"`),d(),i(),r}function Uu(e,t,n){const s=Du(Bu(e.path),n),r=oe(s,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function Ku(e,t){const n=[],s=new Map;t=qr({strict:!1,end:!0,sensitive:!1},t);function r(d){return s.get(d)}function o(d,p,m){const S=!m,E=Wr(d);E.aliasOf=m&&m.record;const N=qr(t,d),H=[E];if("alias"in d){const k=typeof d.alias=="string"?[d.alias]:d.alias;for(const q of k)H.push(Wr(oe({},E,{components:m?m.record.components:E.components,path:q,aliasOf:m?m.record:E})))}let P,D;for(const k of H){const{path:q}=k;if(p&&q[0]!=="/"){const le=p.record.path,re=le[le.length-1]==="/"?"":"/";k.path=p.record.path+(q&&re+q)}if(P=Uu(k,p,N),m?m.alias.push(P):(D=D||P,D!==P&&D.alias.push(P),S&&d.name&&!Gr(P)&&i(d.name)),Ai(P)&&c(P),E.children){const le=E.children;for(let re=0;re<le.length;re++)o(le[re],P,m&&m.children[re])}m=m||P}return D?()=>{i(D)}:an}function i(d){if(Ci(d)){const p=s.get(d);p&&(s.delete(d),n.splice(n.indexOf(p),1),p.children.forEach(i),p.alias.forEach(i))}else{const p=n.indexOf(d);p>-1&&(n.splice(p,1),d.record.name&&s.delete(d.record.name),d.children.forEach(i),d.alias.forEach(i))}}function l(){return n}function c(d){const p=qu(d,n);n.splice(p,0,d),d.record.name&&!Gr(d)&&s.set(d.record.name,d)}function f(d,p){let m,S={},E,N;if("name"in d&&d.name){if(m=s.get(d.name),!m)throw qt(1,{location:d});N=m.record.name,S=oe(Kr(p.params,m.keys.filter(D=>!D.optional).concat(m.parent?m.parent.keys.filter(D=>D.optional):[]).map(D=>D.name)),d.params&&Kr(d.params,m.keys.map(D=>D.name))),E=m.stringify(S)}else if(d.path!=null)E=d.path,m=n.find(D=>D.re.test(E)),m&&(S=m.parse(E),N=m.record.name);else{if(m=p.name?s.get(p.name):n.find(D=>D.re.test(p.path)),!m)throw qt(1,{location:d,currentLocation:p});N=m.record.name,S=oe({},p.params,d.params),E=m.stringify(S)}const H=[];let P=m;for(;P;)H.unshift(P.record),P=P.parent;return{name:N,path:E,params:S,matched:H,meta:Gu(H)}}e.forEach(d=>o(d));function a(){n.length=0,s.clear()}return{addRoute:o,resolve:f,removeRoute:i,clearRoutes:a,getRoutes:l,getRecordMatcher:r}}function Kr(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function Wr(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Wu(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Wu(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function Gr(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Gu(e){return e.reduce((t,n)=>oe(t,n.meta),{})}function qr(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function qu(e,t){let n=0,s=t.length;for(;n!==s;){const o=n+s>>1;Ri(e,t[o])<0?s=o:n=o+1}const r=Yu(e);return r&&(s=t.lastIndexOf(r,s-1)),s}function Yu(e){let t=e;for(;t=t.parent;)if(Ai(t)&&Ri(e,t)===0)return t}function Ai({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Ju(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<s.length;++r){const o=s[r].replace(yi," "),i=o.indexOf("="),l=_n(i<0?o:o.slice(0,i)),c=i<0?null:_n(o.slice(i+1));if(l in t){let f=t[l];We(f)||(f=t[l]=[f]),f.push(c)}else t[l]=c}return t}function Yr(e){let t="";for(let n in e){const s=e[n];if(n=mu(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(We(s)?s.map(o=>o&&Ps(o)):[s&&Ps(s)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function Qu(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=We(s)?s.map(r=>r==null?null:""+r):s==null?s:""+s)}return t}const Xu=Symbol(""),Jr=Symbol(""),Js=Symbol(""),Ti=Symbol(""),Os=Symbol("");function Xt(){let e=[];function t(s){return e.push(s),()=>{const r=e.indexOf(s);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function yt(e,t,n,s,r,o=i=>i()){const i=s&&(s.enterCallbacks[r]=s.enterCallbacks[r]||[]);return()=>new Promise((l,c)=>{const f=p=>{p===!1?c(qt(4,{from:n,to:t})):p instanceof Error?c(p):zu(p)?c(qt(2,{from:t,to:p})):(i&&s.enterCallbacks[r]===i&&typeof p=="function"&&i.push(p),l())},a=o(()=>e.call(s&&s.instances[r],t,n,f));let d=Promise.resolve(a);e.length<3&&(d=d.then(f)),d.catch(p=>c(p))})}function ps(e,t,n,s,r=o=>o()){const o=[];for(const i of e)for(const l in i.components){let c=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(mi(c)){const a=(c.__vccOpts||c)[t];a&&o.push(yt(a,n,s,i,l,r))}else{let f=c();o.push(()=>f.then(a=>{if(!a)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const d=ou(a)?a.default:a;i.mods[l]=a,i.components[l]=d;const m=(d.__vccOpts||d)[t];return m&&yt(m,n,s,i,l,r)()}))}}return o}function Qr(e){const t=lt(Js),n=lt(Ti),s=Be(()=>{const c=jt(e.to);return t.resolve(c)}),r=Be(()=>{const{matched:c}=s.value,{length:f}=c,a=c[f-1],d=n.matched;if(!a||!d.length)return-1;const p=d.findIndex(Gt.bind(null,a));if(p>-1)return p;const m=Xr(c[f-2]);return f>1&&Xr(a)===m&&d[d.length-1].path!==m?d.findIndex(Gt.bind(null,c[f-2])):p}),o=Be(()=>r.value>-1&&sf(n.params,s.value.params)),i=Be(()=>r.value>-1&&r.value===n.matched.length-1&&Si(n.params,s.value.params));function l(c={}){if(nf(c)){const f=t[jt(e.replace)?"replace":"push"](jt(e.to)).catch(an);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>f),f}return Promise.resolve()}return{route:s,href:Be(()=>s.value.href),isActive:o,isExactActive:i,navigate:l}}function Zu(e){return e.length===1?e[0]:e}const ef=Mt({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Qr,setup(e,{slots:t}){const n=Kn(Qr(e)),{options:s}=lt(Js),r=Be(()=>({[Zr(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[Zr(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&Zu(t.default(n));return e.custom?o:ui("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}}),tf=ef;function nf(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function sf(e,t){for(const n in t){const s=t[n],r=e[n];if(typeof s=="string"){if(s!==r)return!1}else if(!We(r)||r.length!==s.length||s.some((o,i)=>o!==r[i]))return!1}return!0}function Xr(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Zr=(e,t,n)=>e??t??n,rf=Mt({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=lt(Os),r=Be(()=>e.route||s.value),o=lt(Jr,0),i=Be(()=>{let f=jt(o);const{matched:a}=r.value;let d;for(;(d=a[f])&&!d.components;)f++;return f}),l=Be(()=>r.value.matched[i.value]);Pn(Jr,Be(()=>i.value+1)),Pn(Xu,l),Pn(Os,r);const c=Ce();return Mn(()=>[c.value,l.value,e.name],([f,a,d],[p,m,S])=>{a&&(a.instances[d]=f,m&&m!==a&&f&&f===p&&(a.leaveGuards.size||(a.leaveGuards=m.leaveGuards),a.updateGuards.size||(a.updateGuards=m.updateGuards))),f&&a&&(!m||!Gt(a,m)||!p)&&(a.enterCallbacks[d]||[]).forEach(E=>E(f))},{flush:"post"}),()=>{const f=r.value,a=e.name,d=l.value,p=d&&d.components[a];if(!p)return eo(n.default,{Component:p,route:f});const m=d.props[a],S=m?m===!0?f.params:typeof m=="function"?m(f):m:null,N=ui(p,oe({},S,t,{onVnodeUnmounted:H=>{H.component.isUnmounted&&(d.instances[a]=null)},ref:c}));return eo(n.default,{Component:N,route:f})||N}}});function eo(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const of=rf;function lf(e){const t=Ku(e.routes,e),n=e.parseQuery||Ju,s=e.stringifyQuery||Yr,r=e.history,o=Xt(),i=Xt(),l=Xt(),c=ll(pt);let f=pt;Nt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const a=ds.bind(null,y=>""+y),d=ds.bind(null,yu),p=ds.bind(null,_n);function m(y,I){let O,F;return Ci(y)?(O=t.getRecordMatcher(y),F=I):F=y,t.addRoute(F,O)}function S(y){const I=t.getRecordMatcher(y);I&&t.removeRoute(I)}function E(){return t.getRoutes().map(y=>y.record)}function N(y){return!!t.getRecordMatcher(y)}function H(y,I){if(I=oe({},I||c.value),typeof y=="string"){const g=hs(n,y,I.path),v=t.resolve({path:g.path},I),w=r.createHref(g.fullPath);return oe(g,v,{params:p(v.params),hash:_n(g.hash),redirectedFrom:void 0,href:w})}let O;if(y.path!=null)O=oe({},y,{path:hs(n,y.path,I.path).path});else{const g=oe({},y.params);for(const v in g)g[v]==null&&delete g[v];O=oe({},y,{params:d(g)}),I.params=d(I.params)}const F=t.resolve(O,I),ue=y.hash||"";F.params=a(p(F.params));const u=wu(s,oe({},y,{hash:gu(ue),path:F.path})),h=r.createHref(u);return oe({fullPath:u,hash:ue,query:s===Yr?Qu(y.query):y.query||{}},F,{redirectedFrom:void 0,href:h})}function P(y){return typeof y=="string"?hs(n,y,c.value.path):oe({},y)}function D(y,I){if(f!==y)return qt(8,{from:I,to:y})}function k(y){return re(y)}function q(y){return k(oe(P(y),{replace:!0}))}function le(y){const I=y.matched[y.matched.length-1];if(I&&I.redirect){const{redirect:O}=I;let F=typeof O=="function"?O(y):O;return typeof F=="string"&&(F=F.includes("?")||F.includes("#")?F=P(F):{path:F},F.params={}),oe({query:y.query,hash:y.hash,params:F.path!=null?{}:y.params},F)}}function re(y,I){const O=f=H(y),F=c.value,ue=y.state,u=y.force,h=y.replace===!0,g=le(O);if(g)return re(oe(P(g),{state:typeof g=="object"?oe({},ue,g.state):ue,force:u,replace:h}),I||O);const v=O;v.redirectedFrom=I;let w;return!u&&Su(s,F,O)&&(w=qt(16,{to:v,from:F}),Y(F,F,!0,!1)),(w?Promise.resolve(w):X(v,F)).catch(_=>st(_)?st(_,2)?_:G(_):b(_,v,F)).then(_=>{if(_){if(st(_,2))return re(oe({replace:h},P(_.to),{state:typeof _.to=="object"?oe({},ue,_.to.state):ue,force:u}),I||v)}else _=L(v,F,!0,h,ue);return Z(v,F,_),_})}function Se(y,I){const O=D(y,I);return O?Promise.reject(O):Promise.resolve()}function K(y){const I=Ot.values().next().value;return I&&typeof I.runWithContext=="function"?I.runWithContext(y):y()}function X(y,I){let O;const[F,ue,u]=cf(y,I);O=ps(F.reverse(),"beforeRouteLeave",y,I);for(const g of F)g.leaveGuards.forEach(v=>{O.push(yt(v,y,I))});const h=Se.bind(null,y,I);return O.push(h),Fe(O).then(()=>{O=[];for(const g of o.list())O.push(yt(g,y,I));return O.push(h),Fe(O)}).then(()=>{O=ps(ue,"beforeRouteUpdate",y,I);for(const g of ue)g.updateGuards.forEach(v=>{O.push(yt(v,y,I))});return O.push(h),Fe(O)}).then(()=>{O=[];for(const g of u)if(g.beforeEnter)if(We(g.beforeEnter))for(const v of g.beforeEnter)O.push(yt(v,y,I));else O.push(yt(g.beforeEnter,y,I));return O.push(h),Fe(O)}).then(()=>(y.matched.forEach(g=>g.enterCallbacks={}),O=ps(u,"beforeRouteEnter",y,I,K),O.push(h),Fe(O))).then(()=>{O=[];for(const g of i.list())O.push(yt(g,y,I));return O.push(h),Fe(O)}).catch(g=>st(g,8)?g:Promise.reject(g))}function Z(y,I,O){l.list().forEach(F=>K(()=>F(y,I,O)))}function L(y,I,O,F,ue){const u=D(y,I);if(u)return u;const h=I===pt,g=Nt?history.state:{};O&&(F||h?r.replace(y.fullPath,oe({scroll:h&&g&&g.scroll},ue)):r.push(y.fullPath,ue)),c.value=y,Y(y,I,O,h),G()}let ee;function ye(){ee||(ee=r.listen((y,I,O)=>{if(!En.listening)return;const F=H(y),ue=le(F);if(ue){re(oe(ue,{replace:!0,force:!0}),F).catch(an);return}f=F;const u=c.value;Nt&&Mu(Fr(u.fullPath,O.delta),Xn()),X(F,u).catch(h=>st(h,12)?h:st(h,2)?(re(oe(P(h.to),{force:!0}),F).then(g=>{st(g,20)&&!O.delta&&O.type===bn.pop&&r.go(-1,!1)}).catch(an),Promise.reject()):(O.delta&&r.go(-O.delta,!1),b(h,F,u))).then(h=>{h=h||L(F,u,!1),h&&(O.delta&&!st(h,8)?r.go(-O.delta,!1):O.type===bn.pop&&st(h,20)&&r.go(-1,!1)),Z(F,u,h)}).catch(an)}))}let Oe=Xt(),me=Xt(),x;function b(y,I,O){G(y);const F=me.list();return F.length?F.forEach(ue=>ue(y,I,O)):console.error(y),Promise.reject(y)}function z(){return x&&c.value!==pt?Promise.resolve():new Promise((y,I)=>{Oe.add([y,I])})}function G(y){return x||(x=!y,ye(),Oe.list().forEach(([I,O])=>y?O(y):I()),Oe.reset()),y}function Y(y,I,O,F){const{scrollBehavior:ue}=e;if(!Nt||!ue)return Promise.resolve();const u=!O&&Ou(Fr(y.fullPath,0))||(F||!O)&&history.state&&history.state.scroll||null;return Oo().then(()=>ue(y,I,u)).then(h=>h&&Pu(h)).catch(h=>b(h,y,I))}const ce=y=>r.go(y);let be;const Ot=new Set,En={currentRoute:c,listening:!0,addRoute:m,removeRoute:S,clearRoutes:t.clearRoutes,hasRoute:N,getRoutes:E,resolve:H,options:e,push:k,replace:q,go:ce,back:()=>ce(-1),forward:()=>ce(1),beforeEach:o.add,beforeResolve:i.add,afterEach:l.add,onError:me.add,isReady:z,install(y){const I=this;y.component("RouterLink",tf),y.component("RouterView",of),y.config.globalProperties.$router=I,Object.defineProperty(y.config.globalProperties,"$route",{enumerable:!0,get:()=>jt(c)}),Nt&&!be&&c.value===pt&&(be=!0,k(r.location).catch(ue=>{}));const O={};for(const ue in pt)Object.defineProperty(O,ue,{get:()=>c.value[ue],enumerable:!0});y.provide(Js,I),y.provide(Ti,xo(O)),y.provide(Os,c);const F=y.unmount;Ot.add(y),y.unmount=function(){Ot.delete(y),Ot.size<1&&(f=pt,ee&&ee(),ee=null,c.value=pt,be=!1,x=!1),F()}}};function Fe(y){return y.reduce((I,O)=>I.then(()=>K(O)),Promise.resolve())}return En}function cf(e,t){const n=[],s=[],r=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const l=t.matched[i];l&&(e.matched.find(f=>Gt(f,l))?s.push(l):n.push(l));const c=e.matched[i];c&&(t.matched.find(f=>Gt(f,c))||r.push(c))}return[n,s,r]}const af={},uf={class:"item"},ff={class:"details"};function df(e,t){return J(),se("div",uf,[R("i",null,[rs(e.$slots,"icon",{},void 0)]),R("div",ff,[R("h3",null,[rs(e.$slots,"heading",{},void 0)]),rs(e.$slots,"default",{},void 0)])])}const Zt=dt(af,[["render",df],["__scopeId","data-v-fd0742eb"]]),hf={},pf={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"17",fill:"currentColor"};function gf(e,t){return J(),se("svg",pf,t[0]||(t[0]=[R("path",{d:"M11 2.253a1 1 0 1 0-2 0h2zm-2 13a1 1 0 1 0 2 0H9zm.447-12.167a1 1 0 1 0 1.107-1.666L9.447 3.086zM1 2.253L.447 1.42A1 1 0 0 0 0 2.253h1zm0 13H0a1 1 0 0 0 1.553.833L1 15.253zm8.447.833a1 1 0 1 0 1.107-1.666l-1.107 1.666zm0-14.666a1 1 0 1 0 1.107 1.666L9.447 1.42zM19 2.253h1a1 1 0 0 0-.447-.833L19 2.253zm0 13l-.553.833A1 1 0 0 0 20 15.253h-1zm-9.553-.833a1 1 0 1 0 1.107 1.666L9.447 14.42zM9 2.253v13h2v-13H9zm1.553-.833C9.203.523 7.42 0 5.5 0v2c1.572 0 2.961.431 3.947 1.086l1.107-1.666zM5.5 0C3.58 0 1.797.523.447 1.42l1.107 1.666C2.539 2.431 3.928 2 5.5 2V0zM0 2.253v13h2v-13H0zm1.553 13.833C2.539 15.431 3.928 15 5.5 15v-2c-1.92 0-3.703.523-5.053 1.42l1.107 1.666zM5.5 15c1.572 0 2.961.431 3.947 1.086l1.107-1.666C9.203 13.523 7.42 13 5.5 13v2zm5.053-11.914C11.539 2.431 12.928 2 14.5 2V0c-1.92 0-3.703.523-5.053 1.42l1.107 1.666zM14.5 2c1.573 0 2.961.431 3.947 1.086l1.107-1.666C18.203.523 16.421 0 14.5 0v2zm3.5.253v13h2v-13h-2zm1.553 12.167C18.203 13.523 16.421 13 14.5 13v2c1.573 0 2.961.431 3.947 1.086l1.107-1.666zM14.5 13c-1.92 0-3.703.523-5.053 1.42l1.107 1.666C11.539 15.431 12.928 15 14.5 15v-2z"},null,-1)]))}const mf=dt(hf,[["render",gf]]),vf={},yf={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink","aria-hidden":"true",role:"img",class:"iconify iconify--mdi",width:"24",height:"24",preserveAspectRatio:"xMidYMid meet",viewBox:"0 0 24 24"};function _f(e,t){return J(),se("svg",yf,t[0]||(t[0]=[R("path",{d:"M20 18v-4h-3v1h-2v-1H9v1H7v-1H4v4h16M6.33 8l-1.74 4H7v-1h2v1h6v-1h2v1h2.41l-1.74-4H6.33M9 5v1h6V5H9m12.84 7.61c.1.22.16.48.16.8V18c0 .53-.21 1-.6 1.41c-.4.4-.85.59-1.4.59H4c-.55 0-1-.19-1.4-.59C2.21 19 2 18.53 2 18v-4.59c0-.32.06-.58.16-.8L4.5 7.22C4.84 6.41 5.45 6 6.33 6H7V5c0-.55.18-1 .57-1.41C7.96 3.2 8.44 3 9 3h6c.56 0 1.04.2 1.43.59c.39.41.57.86.57 1.41v1h.67c.88 0 1.49.41 1.83 1.22l2.34 5.39z",fill:"currentColor"},null,-1)]))}const bf=dt(vf,[["render",_f]]),wf={},Sf={xmlns:"http://www.w3.org/2000/svg",width:"18",height:"20",fill:"currentColor"};function Ef(e,t){return J(),se("svg",Sf,t[0]||(t[0]=[R("path",{d:"M11.447 8.894a1 1 0 1 0-.894-1.789l.894 1.789zm-2.894-.789a1 1 0 1 0 .894 1.789l-.894-1.789zm0 1.789a1 1 0 1 0 .894-1.789l-.894 1.789zM7.447 7.106a1 1 0 1 0-.894 1.789l.894-1.789zM10 9a1 1 0 1 0-2 0h2zm-2 2.5a1 1 0 1 0 2 0H8zm9.447-5.606a1 1 0 1 0-.894-1.789l.894 1.789zm-2.894-.789a1 1 0 1 0 .894 1.789l-.894-1.789zm2 .789a1 1 0 1 0 .894-1.789l-.894 1.789zm-1.106-2.789a1 1 0 1 0-.894 1.789l.894-1.789zM18 5a1 1 0 1 0-2 0h2zm-2 2.5a1 1 0 1 0 2 0h-2zm-5.447-4.606a1 1 0 1 0 .894-1.789l-.894 1.789zM9 1l.447-.894a1 1 0 0 0-.894 0L9 1zm-2.447.106a1 1 0 1 0 .894 1.789l-.894-1.789zm-6 3a1 1 0 1 0 .894 1.789L.553 4.106zm2.894.789a1 1 0 1 0-.894-1.789l.894 1.789zm-2-.789a1 1 0 1 0-.894 1.789l.894-1.789zm1.106 2.789a1 1 0 1 0 .894-1.789l-.894 1.789zM2 5a1 1 0 1 0-2 0h2zM0 7.5a1 1 0 1 0 2 0H0zm8.553 12.394a1 1 0 1 0 .894-1.789l-.894 1.789zm-1.106-2.789a1 1 0 1 0-.894 1.789l.894-1.789zm1.106 1a1 1 0 1 0 .894 1.789l-.894-1.789zm2.894.789a1 1 0 1 0-.894-1.789l.894 1.789zM8 19a1 1 0 1 0 2 0H8zm2-2.5a1 1 0 1 0-2 0h2zm-7.447.394a1 1 0 1 0 .894-1.789l-.894 1.789zM1 15H0a1 1 0 0 0 .553.894L1 15zm1-2.5a1 1 0 1 0-2 0h2zm12.553 2.606a1 1 0 1 0 .894 1.789l-.894-1.789zM17 15l.447.894A1 1 0 0 0 18 15h-1zm1-2.5a1 1 0 1 0-2 0h2zm-7.447-5.394l-2 1 .894 1.789 2-1-.894-1.789zm-1.106 1l-2-1-.894 1.789 2 1 .894-1.789zM8 9v2.5h2V9H8zm8.553-4.894l-2 1 .894 1.789 2-1-.894-1.789zm.894 0l-2-1-.894 1.789 2 1 .894-1.789zM16 5v2.5h2V5h-2zm-4.553-3.894l-2-1-.894 1.789 2 1 .894-1.789zm-2.894-1l-2 1 .894 1.789 2-1L8.553.106zM1.447 5.894l2-1-.894-1.789-2 1 .894 1.789zm-.894 0l2 1 .894-1.789-2-1-.894 1.789zM0 5v2.5h2V5H0zm9.447 13.106l-2-1-.894 1.789 2 1 .894-1.789zm0 1.789l2-1-.894-1.789-2 1 .894 1.789zM10 19v-2.5H8V19h2zm-6.553-3.894l-2-1-.894 1.789 2 1 .894-1.789zM2 15v-2.5H0V15h2zm13.447 1.894l2-1-.894-1.789-2 1 .894 1.789zM18 15v-2.5h-2V15h2z"},null,-1)]))}const Cf=dt(wf,[["render",Ef]]),xf={},Rf={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"currentColor"};function Af(e,t){return J(),se("svg",Rf,t[0]||(t[0]=[R("path",{d:"M15 4a1 1 0 1 0 0 2V4zm0 11v-1a1 1 0 0 0-1 1h1zm0 4l-.707.707A1 1 0 0 0 16 19h-1zm-4-4l.707-.707A1 1 0 0 0 11 14v1zm-4.707-1.293a1 1 0 0 0-1.414 1.414l1.414-1.414zm-.707.707l-.707-.707.707.707zM9 11v-1a1 1 0 0 0-.707.293L9 11zm-4 0h1a1 1 0 0 0-1-1v1zm0 4H4a1 1 0 0 0 1.707.707L5 15zm10-9h2V4h-2v2zm2 0a1 1 0 0 1 1 1h2a3 3 0 0 0-3-3v2zm1 1v6h2V7h-2zm0 6a1 1 0 0 1-1 1v2a3 3 0 0 0 3-3h-2zm-1 1h-2v2h2v-2zm-3 1v4h2v-4h-2zm1.707 3.293l-4-4-1.414 1.414 4 4 1.414-1.414zM11 14H7v2h4v-2zm-4 0c-.276 0-.525-.111-.707-.293l-1.414 1.414C5.42 15.663 6.172 16 7 16v-2zm-.707 1.121l3.414-3.414-1.414-1.414-3.414 3.414 1.414 1.414zM9 12h4v-2H9v2zm4 0a3 3 0 0 0 3-3h-2a1 1 0 0 1-1 1v2zm3-3V3h-2v6h2zm0-6a3 3 0 0 0-3-3v2a1 1 0 0 1 1 1h2zm-3-3H3v2h10V0zM3 0a3 3 0 0 0-3 3h2a1 1 0 0 1 1-1V0zM0 3v6h2V3H0zm0 6a3 3 0 0 0 3 3v-2a1 1 0 0 1-1-1H0zm3 3h2v-2H3v2zm1-1v4h2v-4H4zm1.707 4.707l.586-.586-1.414-1.414-.586.586 1.414 1.414z"},null,-1)]))}const Tf=dt(xf,[["render",Af]]),Pf={},Mf={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"currentColor"};function Of(e,t){return J(),se("svg",Mf,t[0]||(t[0]=[R("path",{d:"M10 3.22l-.61-.6a5.5 5.5 0 0 0-7.666.105 5.5 5.5 0 0 0-.114 7.665L10 18.78l8.39-8.4a5.5 5.5 0 0 0-.114-7.665 5.5 5.5 0 0 0-7.666-.105l-.61.61z"},null,-1)]))}const $f=dt(Pf,[["render",Of]]),If=Mt({__name:"TheWelcome",setup(e){const t=()=>fetch("/__open-in-editor?file=README.md");return(n,s)=>(J(),se(_e,null,[fe(Zt,null,{icon:xe(()=>[fe(mf)]),heading:xe(()=>s[0]||(s[0]=[te("Documentation")])),default:xe(()=>[s[1]||(s[1]=te(" Vue’s ")),s[2]||(s[2]=R("a",{href:"https://vuejs.org/",target:"_blank",rel:"noopener"},"official documentation",-1)),s[3]||(s[3]=te(" provides you with all information you need to get started. "))]),_:1,__:[1,2,3]}),fe(Zt,null,{icon:xe(()=>[fe(bf)]),heading:xe(()=>s[4]||(s[4]=[te("Tooling")])),default:xe(()=>[s[6]||(s[6]=te(" This project is served and bundled with ")),s[7]||(s[7]=R("a",{href:"https://vite.dev/guide/features.html",target:"_blank",rel:"noopener"},"Vite",-1)),s[8]||(s[8]=te(". The recommended IDE setup is ")),s[9]||(s[9]=R("a",{href:"https://code.visualstudio.com/",target:"_blank",rel:"noopener"},"VSCode",-1)),s[10]||(s[10]=te(" + ")),s[11]||(s[11]=R("a",{href:"https://github.com/vuejs/language-tools",target:"_blank",rel:"noopener"},"Vue - Official",-1)),s[12]||(s[12]=te(". If you need to test your components and web pages, check out ")),s[13]||(s[13]=R("a",{href:"https://vitest.dev/",target:"_blank",rel:"noopener"},"Vitest",-1)),s[14]||(s[14]=te(" and ")),s[15]||(s[15]=R("a",{href:"https://www.cypress.io/",target:"_blank",rel:"noopener"},"Cypress",-1)),s[16]||(s[16]=te(" / ")),s[17]||(s[17]=R("a",{href:"https://playwright.dev/",target:"_blank",rel:"noopener"},"Playwright",-1)),s[18]||(s[18]=te(". ")),s[19]||(s[19]=R("br",null,null,-1)),s[20]||(s[20]=te(" More instructions are available in ")),R("a",{href:"javascript:void(0)",onClick:t},s[5]||(s[5]=[R("code",null,"README.md",-1)])),s[21]||(s[21]=te(". "))]),_:1,__:[6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21]}),fe(Zt,null,{icon:xe(()=>[fe(Cf)]),heading:xe(()=>s[22]||(s[22]=[te("Ecosystem")])),default:xe(()=>[s[23]||(s[23]=te(" Get official tools and libraries for your project: ")),s[24]||(s[24]=R("a",{href:"https://pinia.vuejs.org/",target:"_blank",rel:"noopener"},"Pinia",-1)),s[25]||(s[25]=te(", ")),s[26]||(s[26]=R("a",{href:"https://router.vuejs.org/",target:"_blank",rel:"noopener"},"Vue Router",-1)),s[27]||(s[27]=te(", ")),s[28]||(s[28]=R("a",{href:"https://test-utils.vuejs.org/",target:"_blank",rel:"noopener"},"Vue Test Utils",-1)),s[29]||(s[29]=te(", and ")),s[30]||(s[30]=R("a",{href:"https://github.com/vuejs/devtools",target:"_blank",rel:"noopener"},"Vue Dev Tools",-1)),s[31]||(s[31]=te(". If you need more resources, we suggest paying ")),s[32]||(s[32]=R("a",{href:"https://github.com/vuejs/awesome-vue",target:"_blank",rel:"noopener"},"Awesome Vue",-1)),s[33]||(s[33]=te(" a visit. "))]),_:1,__:[23,24,25,26,27,28,29,30,31,32,33]}),fe(Zt,null,{icon:xe(()=>[fe(Tf)]),heading:xe(()=>s[34]||(s[34]=[te("Community")])),default:xe(()=>[s[35]||(s[35]=te(" Got stuck? Ask your question on ")),s[36]||(s[36]=R("a",{href:"https://chat.vuejs.org",target:"_blank",rel:"noopener"},"Vue Land",-1)),s[37]||(s[37]=te(" (our official Discord server), or ")),s[38]||(s[38]=R("a",{href:"https://stackoverflow.com/questions/tagged/vue.js",target:"_blank",rel:"noopener"},"StackOverflow",-1)),s[39]||(s[39]=te(". You should also follow the official ")),s[40]||(s[40]=R("a",{href:"https://bsky.app/profile/vuejs.org",target:"_blank",rel:"noopener"},"@vuejs.org",-1)),s[41]||(s[41]=te(" Bluesky account or the ")),s[42]||(s[42]=R("a",{href:"https://x.com/vuejs",target:"_blank",rel:"noopener"},"@vuejs",-1)),s[43]||(s[43]=te(" X account for latest news in the Vue world. "))]),_:1,__:[35,36,37,38,39,40,41,42,43]}),fe(Zt,null,{icon:xe(()=>[fe($f)]),heading:xe(()=>s[44]||(s[44]=[te("Support Vue")])),default:xe(()=>[s[45]||(s[45]=te(" As an independent project, Vue relies on community backing for its sustainability. You can help us by ")),s[46]||(s[46]=R("a",{href:"https://vuejs.org/sponsor/",target:"_blank",rel:"noopener"},"becoming a sponsor",-1)),s[47]||(s[47]=te(". "))]),_:1,__:[45,46,47]})],64))}}),kf=Mt({__name:"HomeView",setup(e){return(t,n)=>(J(),se("main",null,[fe(If)]))}}),Lf=lf({history:Lu("/"),routes:[{path:"/",name:"home",component:kf},{path:"/about",name:"about",component:()=>ru(()=>import("./AboutView-C0meWy0P.js"),__vite__mapDeps([0,1]))}]}),Qs=na(tu);Qs.use(ia());Qs.use(Lf);Qs.mount("#app");export{dt as _,R as a,se as c,J as o};
