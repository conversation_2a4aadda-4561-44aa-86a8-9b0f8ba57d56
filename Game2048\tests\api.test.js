/**
 * 后端API单元测试
 */

// 简单的测试框架
class TestRunner {
  constructor() {
    this.tests = [];
    this.passed = 0;
    this.failed = 0;
  }

  test(name, fn) {
    this.tests.push({ name, fn });
  }

  async run() {
    console.log('🧪 开始运行API测试...\n');
    
    for (const { name, fn } of this.tests) {
      try {
        await fn();
        console.log(`✅ ${name}`);
        this.passed++;
      } catch (error) {
        console.log(`❌ ${name}: ${error.message}`);
        this.failed++;
      }
    }

    console.log(`\n📊 测试结果: ${this.passed} 通过, ${this.failed} 失败`);
    return this.failed === 0;
  }
}

// 断言函数
function assertEqual(actual, expected, message = '') {
  if (JSON.stringify(actual) !== JSON.stringify(expected)) {
    throw new Error(`${message}\n期望: ${JSON.stringify(expected)}\n实际: ${JSON.stringify(actual)}`);
  }
}

function assertTrue(condition, message = '') {
  if (!condition) {
    throw new Error(message || '断言失败');
  }
}

// API基础URL
const API_BASE_URL = 'http://localhost:3001/api';

// 生成验证载荷的函数
const generateValidationPayload = (score, board) => {
  const data = `${score}-${JSON.stringify(board)}-${Date.now()}`;
  let hash = 0;
  for (let i = 0; i < data.length; i++) {
    const char = data.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash;
  }
  return Math.abs(hash).toString(16);
};

// 测试用例
const runner = new TestRunner();

// 测试健康检查
runner.test('健康检查API', async () => {
  const response = await fetch(`${API_BASE_URL}/health`);
  const data = await response.json();
  
  assertTrue(response.ok, '健康检查应该返回200状态码');
  assertEqual(data.status, 'OK', '健康检查状态应该为OK');
  assertTrue(data.timestamp, '应该包含时间戳');
});

// 测试分数提交
runner.test('分数提交API - 新玩家', async () => {
  const testBoard = [
    [2, 4, 8, 16],
    [32, 64, 128, 256],
    [512, 1024, 0, 0],
    [0, 0, 0, 0]
  ];
  
  const testScore = 1000;
  const playerName = `测试玩家_${Date.now()}`;
  const validationPayload = generateValidationPayload(testScore, testBoard);
  
  const response = await fetch(`${API_BASE_URL}/scores`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      playerName,
      score: testScore,
      validationPayload
    })
  });
  
  const data = await response.json();
  
  assertTrue(response.ok, '新玩家分数提交应该成功');
  assertTrue(data.success, '响应应该包含success字段');
  assertTrue(data.id, '响应应该包含记录ID');
  assertEqual(data.isNewRecord, false, '新玩家第一次提交不应该标记为新纪录');
});

// 测试分数提交 - 更高分数
runner.test('分数提交API - 更高分数', async () => {
  const testBoard = [
    [2, 4, 8, 16],
    [32, 64, 128, 256],
    [512, 1024, 2048, 0],
    [0, 0, 0, 0]
  ];
  
  const testScore = 5000;
  const playerName = 'mk99'; // 使用已存在的玩家
  const validationPayload = generateValidationPayload(testScore, testBoard);
  
  const response = await fetch(`${API_BASE_URL}/scores`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      playerName,
      score: testScore,
      validationPayload
    })
  });
  
  const data = await response.json();
  
  // 这个测试可能失败，因为mk99可能已经有更高的分数
  if (response.ok) {
    assertTrue(data.success, '更高分数提交应该成功');
    assertTrue(data.isNewRecord, '更高分数应该标记为新纪录');
  } else {
    // 如果分数不够高，应该返回相应的错误信息
    assertTrue(data.error.includes('未超过记录'), '应该返回分数未超过记录的错误');
  }
});

// 测试排行榜API
runner.test('排行榜API', async () => {
  const response = await fetch(`${API_BASE_URL}/leaderboard?period=alltime&limit=5`);
  const data = await response.json();
  
  assertTrue(response.ok, '排行榜API应该返回200状态码');
  assertTrue(Array.isArray(data.leaderboard), '排行榜应该是数组');
  assertEqual(data.period, 'alltime', '应该返回正确的时间段');
  
  if (data.leaderboard.length > 0) {
    const firstEntry = data.leaderboard[0];
    assertTrue(firstEntry.rank, '排行榜条目应该包含排名');
    assertTrue(firstEntry.playerName, '排行榜条目应该包含玩家名');
    assertTrue(firstEntry.score, '排行榜条目应该包含分数');
    assertTrue(firstEntry.createdAt, '排行榜条目应该包含创建时间');
  }
});

// 测试统计信息API
runner.test('统计信息API', async () => {
  const response = await fetch(`${API_BASE_URL}/stats`);
  const data = await response.json();
  
  assertTrue(response.ok, '统计信息API应该返回200状态码');
  assertTrue(typeof data.totalGames === 'number', '总游戏数应该是数字');
  assertTrue(typeof data.highestScore === 'number', '最高分应该是数字');
  assertTrue(typeof data.averageScore === 'number', '平均分应该是数字');
  assertTrue(typeof data.uniquePlayers === 'number', '独特玩家数应该是数字');
});

// 测试输入验证
runner.test('输入验证 - 缺少玩家名', async () => {
  const response = await fetch(`${API_BASE_URL}/scores`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      score: 1000,
      validationPayload: 'test'
    })
  });
  
  const data = await response.json();
  
  assertTrue(!response.ok, '缺少玩家名应该返回错误');
  assertTrue(data.error.includes('玩家名称'), '错误信息应该提到玩家名称');
});

// 测试输入验证 - 无效分数
runner.test('输入验证 - 无效分数', async () => {
  const response = await fetch(`${API_BASE_URL}/scores`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      playerName: '测试玩家',
      score: 'invalid',
      validationPayload: 'test'
    })
  });
  
  const data = await response.json();
  
  assertTrue(!response.ok, '无效分数应该返回错误');
  assertTrue(data.error.includes('分数'), '错误信息应该提到分数');
});

// 运行测试
runner.run().then(success => {
  process.exit(success ? 0 : 1);
});
