<template>
  <div class="leaderboard-container">
    <h2>排行榜</h2>
    
    <div class="period-selector">
      <button 
        v-for="period in periods" 
        :key="period.value"
        :class="['period-btn', { active: selectedPeriod === period.value }]"
        @click="selectPeriod(period.value)"
      >
        {{ period.label }}
      </button>
    </div>
    
    <div v-if="loading" class="loading">
      加载中...
    </div>
    
    <div v-else-if="error" class="error">
      {{ error }}
      <button @click="fetchLeaderboard" class="retry-btn">重试</button>
    </div>
    
    <div v-else class="leaderboard-list">
      <div v-if="leaderboard.length === 0" class="empty-state">
        暂无排行榜数据
      </div>
      
      <div v-else>
        <div 
          v-for="entry in leaderboard" 
          :key="entry.rank"
          :class="['leaderboard-entry', getRankClass(entry.rank)]"
        >
          <div class="rank">
            <span v-if="entry.rank <= 3" class="medal">{{ getMedal(entry.rank) }}</span>
            <span v-else class="rank-number">{{ entry.rank }}</span>
          </div>
          <div class="player-info">
            <div class="player-name">{{ entry.playerName }}</div>
            <div class="play-time">{{ formatDate(entry.createdAt) }}</div>
          </div>
          <div class="score">{{ formatScore(entry.score) }}</div>
        </div>
      </div>
    </div>
    
    <div v-if="stats" class="stats-section">
      <h3>游戏统计</h3>
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-value">{{ stats.totalGames }}</div>
          <div class="stat-label">总游戏数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ formatScore(stats.highestScore) }}</div>
          <div class="stat-label">最高分</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ formatScore(stats.averageScore) }}</div>
          <div class="stat-label">平均分</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ stats.uniquePlayers }}</div>
          <div class="stat-label">玩家数</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { analytics } from '../utils/analytics'

interface LeaderboardEntry {
  rank: number
  playerName: string
  score: number
  createdAt: string
}

interface Stats {
  totalGames: number
  highestScore: number
  averageScore: number
  uniquePlayers: number
}

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'

const leaderboard = ref<LeaderboardEntry[]>([])
const stats = ref<Stats | null>(null)
const loading = ref(false)
const error = ref('')
const selectedPeriod = ref('alltime')

const periods = [
  { value: 'alltime', label: '总榜' },
  { value: 'weekly', label: '周榜' },
  { value: 'daily', label: '日榜' }
]

const fetchLeaderboard = async () => {
  loading.value = true
  error.value = ''
  
  try {
    const response = await fetch(`${API_BASE_URL}/leaderboard?period=${selectedPeriod.value}&limit=10`)
    
    if (!response.ok) {
      throw new Error('获取排行榜失败')
    }
    
    const data = await response.json()
    leaderboard.value = data.leaderboard || []
  } catch (err) {
    error.value = err instanceof Error ? err.message : '网络错误'
    console.error('Failed to fetch leaderboard:', err)
  } finally {
    loading.value = false
  }
}

const fetchStats = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/stats`)
    
    if (!response.ok) {
      throw new Error('获取统计信息失败')
    }
    
    const data = await response.json()
    stats.value = data
  } catch (err) {
    console.error('Failed to fetch stats:', err)
  }
}

const selectPeriod = (period: string) => {
  selectedPeriod.value = period
  fetchLeaderboard()

  // 追踪排行榜查看
  analytics.trackLeaderboardView(period)
}

const getRankClass = (rank: number) => {
  if (rank === 1) return 'first-place'
  if (rank === 2) return 'second-place'
  if (rank === 3) return 'third-place'
  return ''
}

const getMedal = (rank: number) => {
  const medals = ['🥇', '🥈', '🥉']
  return medals[rank - 1] || ''
}

const formatScore = (score: number) => {
  return score.toLocaleString()
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  if (diffDays === 0) {
    return '今天'
  } else if (diffDays === 1) {
    return '昨天'
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

onMounted(() => {
  fetchLeaderboard()
  fetchStats()
})
</script>

<style scoped>
.leaderboard-container {
  max-width: 500px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Arial', sans-serif;
}

.leaderboard-container h2 {
  text-align: center;
  color: #776e65;
  margin-bottom: 20px;
}

.period-selector {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  gap: 10px;
}

.period-btn {
  padding: 8px 16px;
  border: 2px solid #bbada0;
  background: white;
  color: #776e65;
  border-radius: 6px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.2s;
}

.period-btn:hover {
  background: #f9f6f2;
}

.period-btn.active {
  background: #8f7a66;
  color: white;
  border-color: #8f7a66;
}

.loading, .error {
  text-align: center;
  padding: 40px;
  color: #776e65;
}

.retry-btn {
  margin-left: 10px;
  padding: 5px 10px;
  background: #8f7a66;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #776e65;
}

.leaderboard-list {
  background: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.leaderboard-entry {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  transition: background 0.2s;
}

.leaderboard-entry:hover {
  background: #f9f6f2;
}

.leaderboard-entry:last-child {
  border-bottom: none;
}

.leaderboard-entry.first-place {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
}

.leaderboard-entry.second-place {
  background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
}

.leaderboard-entry.third-place {
  background: linear-gradient(135deg, #cd7f32, #daa520);
}

.rank {
  width: 50px;
  text-align: center;
  font-weight: bold;
}

.medal {
  font-size: 24px;
}

.rank-number {
  font-size: 18px;
  color: #776e65;
}

.player-info {
  flex: 1;
  margin-left: 15px;
}

.player-name {
  font-weight: bold;
  color: #776e65;
  font-size: 16px;
}

.play-time {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.score {
  font-weight: bold;
  font-size: 18px;
  color: #8f7a66;
}

.stats-section {
  margin-top: 30px;
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.stats-section h3 {
  text-align: center;
  color: #776e65;
  margin-bottom: 15px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background: #f9f6f2;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #8f7a66;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #776e65;
  text-transform: uppercase;
}

@media (max-width: 600px) {
  .leaderboard-container {
    padding: 10px;
  }
  
  .period-selector {
    flex-wrap: wrap;
  }
  
  .period-btn {
    padding: 6px 12px;
    font-size: 14px;
  }
  
  .leaderboard-entry {
    padding: 12px 15px;
  }
  
  .player-name {
    font-size: 14px;
  }
  
  .score {
    font-size: 16px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }
}
</style>
