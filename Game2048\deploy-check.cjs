/**
 * 部署前检查脚本
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 开始部署前检查...\n');

let hasErrors = false;

// 检查必要文件
const requiredFiles = [
  'package.json',
  'src/main.ts',
  'src/App.vue',
  'src/components/GameBoard.vue',
  'src/components/Leaderboard.vue',
  'backend/server.js',
  'backend/package.json'
];

console.log('📁 检查必要文件...');
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - 文件不存在`);
    hasErrors = true;
  }
});

// 检查环境配置文件
console.log('\n🔧 检查环境配置...');
const envFiles = [
  '.env.example',
  'backend/.env.example'
];

envFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`⚠️  ${file} - 建议创建环境配置示例文件`);
  }
});

// 检查package.json中的脚本
console.log('\n📦 检查构建脚本...');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const requiredScripts = ['build', 'preview'];
  
  requiredScripts.forEach(script => {
    if (packageJson.scripts && packageJson.scripts[script]) {
      console.log(`✅ npm run ${script}`);
    } else {
      console.log(`❌ npm run ${script} - 脚本不存在`);
      hasErrors = true;
    }
  });
} catch (error) {
  console.log('❌ 无法读取package.json');
  hasErrors = true;
}

// 检查后端package.json
console.log('\n🔧 检查后端配置...');
try {
  const backendPackageJson = JSON.parse(fs.readFileSync('backend/package.json', 'utf8'));
  const requiredDeps = ['express', 'sqlite3', 'cors', 'helmet', 'morgan', 'dotenv'];
  
  requiredDeps.forEach(dep => {
    if (backendPackageJson.dependencies && backendPackageJson.dependencies[dep]) {
      console.log(`✅ ${dep}`);
    } else {
      console.log(`❌ ${dep} - 依赖缺失`);
      hasErrors = true;
    }
  });
} catch (error) {
  console.log('❌ 无法读取backend/package.json');
  hasErrors = true;
}

// 检查测试文件
console.log('\n🧪 检查测试文件...');
const testFiles = [
  'tests/game-logic.test.js',
  'tests/api.test.js',
  'tests/stress.test.js'
];

testFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`⚠️  ${file} - 测试文件不存在`);
  }
});

// 检查构建输出目录
console.log('\n🏗️  检查构建准备...');
if (fs.existsSync('dist')) {
  console.log('✅ dist目录存在（之前的构建）');
} else {
  console.log('ℹ️  dist目录不存在（需要运行构建）');
}

// 总结
console.log('\n📊 检查总结:');
if (hasErrors) {
  console.log('❌ 发现错误，请修复后再部署');
  process.exit(1);
} else {
  console.log('✅ 所有检查通过，可以开始部署！');
  
  console.log('\n📋 部署步骤建议:');
  console.log('1. 运行 npm run build 构建前端');
  console.log('2. 测试构建结果 npm run preview');
  console.log('3. 部署前端到静态托管服务（Vercel/Netlify）');
  console.log('4. 部署后端到云服务（Railway/Render）');
  console.log('5. 更新前端环境变量中的API地址');
  console.log('6. 进行最终测试');
  
  process.exit(0);
}
