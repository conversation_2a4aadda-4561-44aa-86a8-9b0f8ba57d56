# 炫酷版2048游戏

一个基于Vue.js和Node.js的现代化2048游戏，具有流畅的动画效果、响应式设计和在线排行榜功能。

## 功能特性

### 前端功能
- ✅ 完整的2048游戏逻辑
- ✅ 流畅的动画效果
- ✅ 响应式设计，支持桌面和移动端
- ✅ 触摸滑动支持
- ✅ 游戏状态自动保存
- ✅ 最高分记录
- ✅ 分数提交功能
- ✅ 在线排行榜

### 后端功能
- ✅ RESTful API
- ✅ SQLite数据库
- ✅ 分数提交和验证
- ✅ 排行榜系统（总榜、周榜、日榜）
- ✅ 游戏统计信息
- ✅ 基础反作弊机制
- ✅ 敏感词过滤
- ✅ 频率限制

## 技术栈

### 前端
- Vue.js 3
- TypeScript
- Vite
- CSS3 动画

### 后端
- Node.js
- Express.js
- SQLite3
- CORS, Helmet (安全中间件)

## 快速开始

### 前置要求
- Node.js 16+
- npm 或 yarn

### 安装和运行

1. **安装前端依赖**
```bash
npm install
```

2. **安装后端依赖**
```bash
cd backend
npm install
```

3. **启动后端服务器**
```bash
npm run dev
# 服务器将在 http://localhost:3001 启动
```

4. **启动前端开发服务器**
```bash
cd ..
npm run dev
# 前端将在 http://localhost:5173 启动
```

5. **访问游戏**
打开浏览器访问 `http://localhost:5173`

## 游戏说明

### 操作方式
- **桌面端**: 使用方向键（↑↓←→）移动方块
- **移动端**: 滑动屏幕移动方块

### 游戏规则
1. 使用方向键移动方块
2. 相同数字的方块会合并成一个更大的数字
3. 每次移动后会随机生成一个新方块（2或4）
4. 目标是创造出2048方块
5. 当无法移动时游戏结束

### 排行榜
- 游戏结束后可以提交分数到排行榜
- 支持查看总榜、周榜、日榜
- 显示游戏统计信息

## API 接口

### 健康检查
```
GET /api/health
```

### 提交分数
```
POST /api/scores
Content-Type: application/json

{
  "playerName": "玩家昵称",
  "score": 12345,
  "validationPayload": "验证载荷"
}
```

### 获取排行榜
```
GET /api/leaderboard?period=alltime&limit=10
```

参数：
- `period`: `alltime` | `weekly` | `daily`
- `limit`: 返回条数（最大100）

### 获取统计信息
```
GET /api/stats
```

## 部署

### 前端部署
```bash
npm run build
# 构建产物在 dist/ 目录
```

### 后端部署
```bash
cd backend
npm start
# 或使用 PM2
pm2 start ecosystem.config.js --env production
```

## 开发

### 运行测试
```bash
# 前端测试
npm run test:unit

# 后端API测试
cd backend
node test-api.js
```

### 代码格式化
```bash
npm run format
```

### 类型检查
```bash
npm run type-check
```

## 项目结构

```
Game2048/
├── src/                    # 前端源码
│   ├── components/         # Vue组件
│   │   ├── GameBoard.vue   # 游戏主组件
│   │   └── Leaderboard.vue # 排行榜组件
│   ├── assets/            # 静态资源
│   └── main.ts            # 入口文件
├── backend/               # 后端源码
│   ├── server.js          # 服务器主文件
│   ├── test-api.js        # API测试脚本
│   └── game2048.db        # SQLite数据库
├── dist/                  # 前端构建产物
└── README.md             # 项目说明
```

## 贡献

欢迎提交 Issue 和 Pull Request！

## 许可证

MIT License
