const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/AboutView-BChy9Mv0.js","assets/AboutView-CSIvawM9.css"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(r){if(r.ep)return;r.ep=!0;const o=n(r);fetch(r.href,o)}})();/**
* @vue/shared v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function ks(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const ce={},Vt=[],tt=()=>{},Di=()=>!1,Vn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Ls=e=>e.startsWith("onUpdate:"),be=Object.assign,Ns=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Fi=Object.prototype.hasOwnProperty,se=(e,t)=>Fi.call(e,t),F=Array.isArray,jt=e=>jn(e)==="[object Map]",oo=e=>jn(e)==="[object Set]",B=e=>typeof e=="function",ge=e=>typeof e=="string",ut=e=>typeof e=="symbol",fe=e=>e!==null&&typeof e=="object",io=e=>(fe(e)||B(e))&&B(e.then)&&B(e.catch),lo=Object.prototype.toString,jn=e=>lo.call(e),Vi=e=>jn(e).slice(8,-1),co=e=>jn(e)==="[object Object]",zs=e=>ge(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,sn=ks(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Bn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},ji=/-(\w)/g,Ct=Bn(e=>e.replace(ji,(t,n)=>n?n.toUpperCase():"")),Bi=/\B([A-Z])/g,Rt=Bn(e=>e.replace(Bi,"-$1").toLowerCase()),ao=Bn(e=>e.charAt(0).toUpperCase()+e.slice(1)),ts=Bn(e=>e?`on${ao(e)}`:""),St=(e,t)=>!Object.is(e,t),Pn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},ys=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},bs=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Ui=e=>{const t=ge(e)?Number(e):NaN;return isNaN(t)?e:t};let rr;const Un=()=>rr||(rr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Hs(e){if(F(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=ge(s)?qi(s):Hs(s);if(r)for(const o in r)t[o]=r[o]}return t}else if(ge(e)||fe(e))return e}const Ki=/;(?![^(]*\))/g,Wi=/:([^]+)/,Gi=/\/\*[^]*?\*\//g;function qi(e){const t={};return e.replace(Gi,"").split(Ki).forEach(n=>{if(n){const s=n.split(Wi);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Et(e){let t="";if(ge(e))t=e;else if(F(e))for(let n=0;n<e.length;n++){const s=Et(e[n]);s&&(t+=s+" ")}else if(fe(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Yi="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Ji=ks(Yi);function uo(e){return!!e||e===""}const fo=e=>!!(e&&e.__v_isRef===!0),ye=e=>ge(e)?e:e==null?"":F(e)||fe(e)&&(e.toString===lo||!B(e.toString))?fo(e)?ye(e.value):JSON.stringify(e,ho,2):String(e),ho=(e,t)=>fo(t)?ho(e,t.value):jt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],o)=>(n[ns(s,o)+" =>"]=r,n),{})}:oo(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>ns(n))}:ut(t)?ns(t):fe(t)&&!F(t)&&!co(t)?String(t):t,ns=(e,t="")=>{var n;return ut(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Le;class po{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Le,!t&&Le&&(this.index=(Le.scopes||(Le.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Le;try{return Le=this,t()}finally{Le=n}}}on(){++this._on===1&&(this.prevScope=Le,Le=this)}off(){this._on>0&&--this._on===0&&(Le=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function Qi(e){return new po(e)}function Xi(){return Le}let ue;const ss=new WeakSet;class go{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Le&&Le.active&&Le.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,ss.has(this)&&(ss.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||vo(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,or(this),yo(this);const t=ue,n=Ke;ue=this,Ke=!0;try{return this.fn()}finally{bo(this),ue=t,Ke=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Vs(t);this.deps=this.depsTail=void 0,or(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?ss.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){_s(this)&&this.run()}get dirty(){return _s(this)}}let mo=0,rn,on;function vo(e,t=!1){if(e.flags|=8,t){e.next=on,on=e;return}e.next=rn,rn=e}function Ds(){mo++}function Fs(){if(--mo>0)return;if(on){let t=on;for(on=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;rn;){let t=rn;for(rn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function yo(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function bo(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),Vs(s),Zi(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function _s(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(_o(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function _o(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===hn)||(e.globalVersion=hn,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!_s(e))))return;e.flags|=2;const t=e.dep,n=ue,s=Ke;ue=e,Ke=!0;try{yo(e);const r=e.fn(e._value);(t.version===0||St(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{ue=n,Ke=s,bo(e),e.flags&=-3}}function Vs(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)Vs(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Zi(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Ke=!0;const wo=[];function ct(){wo.push(Ke),Ke=!1}function at(){const e=wo.pop();Ke=e===void 0?!0:e}function or(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=ue;ue=void 0;try{t()}finally{ue=n}}}let hn=0;class el{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class js{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!ue||!Ke||ue===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==ue)n=this.activeLink=new el(ue,this),ue.deps?(n.prevDep=ue.depsTail,ue.depsTail.nextDep=n,ue.depsTail=n):ue.deps=ue.depsTail=n,So(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=ue.depsTail,n.nextDep=void 0,ue.depsTail.nextDep=n,ue.depsTail=n,ue.deps===n&&(ue.deps=s)}return n}trigger(t){this.version++,hn++,this.notify(t)}notify(t){Ds();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Fs()}}}function So(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)So(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const ws=new WeakMap,It=Symbol(""),Ss=Symbol(""),pn=Symbol("");function Te(e,t,n){if(Ke&&ue){let s=ws.get(e);s||ws.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new js),r.map=s,r.key=n),r.track()}}function ot(e,t,n,s,r,o){const i=ws.get(e);if(!i){hn++;return}const a=u=>{u&&u.trigger()};if(Ds(),t==="clear")i.forEach(a);else{const u=F(e),d=u&&zs(n);if(u&&n==="length"){const f=Number(s);i.forEach((p,g)=>{(g==="length"||g===pn||!ut(g)&&g>=f)&&a(p)})}else switch((n!==void 0||i.has(void 0))&&a(i.get(n)),d&&a(i.get(pn)),t){case"add":u?d&&a(i.get("length")):(a(i.get(It)),jt(e)&&a(i.get(Ss)));break;case"delete":u||(a(i.get(It)),jt(e)&&a(i.get(Ss)));break;case"set":jt(e)&&a(i.get(It));break}}Fs()}function zt(e){const t=Q(e);return t===e?t:(Te(t,"iterate",pn),je(e)?t:t.map(Se))}function Kn(e){return Te(e=Q(e),"iterate",pn),e}const tl={__proto__:null,[Symbol.iterator](){return rs(this,Symbol.iterator,Se)},concat(...e){return zt(this).concat(...e.map(t=>F(t)?zt(t):t))},entries(){return rs(this,"entries",e=>(e[1]=Se(e[1]),e))},every(e,t){return nt(this,"every",e,t,void 0,arguments)},filter(e,t){return nt(this,"filter",e,t,n=>n.map(Se),arguments)},find(e,t){return nt(this,"find",e,t,Se,arguments)},findIndex(e,t){return nt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return nt(this,"findLast",e,t,Se,arguments)},findLastIndex(e,t){return nt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return nt(this,"forEach",e,t,void 0,arguments)},includes(...e){return os(this,"includes",e)},indexOf(...e){return os(this,"indexOf",e)},join(e){return zt(this).join(e)},lastIndexOf(...e){return os(this,"lastIndexOf",e)},map(e,t){return nt(this,"map",e,t,void 0,arguments)},pop(){return Xt(this,"pop")},push(...e){return Xt(this,"push",e)},reduce(e,...t){return ir(this,"reduce",e,t)},reduceRight(e,...t){return ir(this,"reduceRight",e,t)},shift(){return Xt(this,"shift")},some(e,t){return nt(this,"some",e,t,void 0,arguments)},splice(...e){return Xt(this,"splice",e)},toReversed(){return zt(this).toReversed()},toSorted(e){return zt(this).toSorted(e)},toSpliced(...e){return zt(this).toSpliced(...e)},unshift(...e){return Xt(this,"unshift",e)},values(){return rs(this,"values",Se)}};function rs(e,t,n){const s=Kn(e),r=s[t]();return s!==e&&!je(e)&&(r._next=r.next,r.next=()=>{const o=r._next();return o.value&&(o.value=n(o.value)),o}),r}const nl=Array.prototype;function nt(e,t,n,s,r,o){const i=Kn(e),a=i!==e&&!je(e),u=i[t];if(u!==nl[t]){const p=u.apply(e,o);return a?Se(p):p}let d=n;i!==e&&(a?d=function(p,g){return n.call(this,Se(p),g,e)}:n.length>2&&(d=function(p,g){return n.call(this,p,g,e)}));const f=u.call(i,d,s);return a&&r?r(f):f}function ir(e,t,n,s){const r=Kn(e);let o=n;return r!==e&&(je(e)?n.length>3&&(o=function(i,a,u){return n.call(this,i,a,u,e)}):o=function(i,a,u){return n.call(this,i,Se(a),u,e)}),r[t](o,...s)}function os(e,t,n){const s=Q(e);Te(s,"iterate",pn);const r=s[t](...n);return(r===-1||r===!1)&&Ks(n[0])?(n[0]=Q(n[0]),s[t](...n)):r}function Xt(e,t,n=[]){ct(),Ds();const s=Q(e)[t].apply(e,n);return Fs(),at(),s}const sl=ks("__proto__,__v_isRef,__isVue"),Co=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(ut));function rl(e){ut(e)||(e=String(e));const t=Q(this);return Te(t,"has",e),t.hasOwnProperty(e)}class Eo{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return o;if(n==="__v_raw")return s===(r?o?pl:Ao:o?Ro:To).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const i=F(t);if(!r){let u;if(i&&(u=tl[n]))return u;if(n==="hasOwnProperty")return rl}const a=Reflect.get(t,n,Pe(t)?t:s);return(ut(n)?Co.has(n):sl(n))||(r||Te(t,"get",n),o)?a:Pe(a)?i&&zs(n)?a:a.value:fe(a)?r?Mo(a):Wn(a):a}}class xo extends Eo{constructor(t=!1){super(!1,t)}set(t,n,s,r){let o=t[n];if(!this._isShallow){const u=xt(o);if(!je(s)&&!xt(s)&&(o=Q(o),s=Q(s)),!F(t)&&Pe(o)&&!Pe(s))return u?!1:(o.value=s,!0)}const i=F(t)&&zs(n)?Number(n)<t.length:se(t,n),a=Reflect.set(t,n,s,Pe(t)?t:r);return t===Q(r)&&(i?St(s,o)&&ot(t,"set",n,s):ot(t,"add",n,s)),a}deleteProperty(t,n){const s=se(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&ot(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!ut(n)||!Co.has(n))&&Te(t,"has",n),s}ownKeys(t){return Te(t,"iterate",F(t)?"length":It),Reflect.ownKeys(t)}}class ol extends Eo{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const il=new xo,ll=new ol,cl=new xo(!0);const Cs=e=>e,xn=e=>Reflect.getPrototypeOf(e);function al(e,t,n){return function(...s){const r=this.__v_raw,o=Q(r),i=jt(o),a=e==="entries"||e===Symbol.iterator&&i,u=e==="keys"&&i,d=r[e](...s),f=n?Cs:t?kn:Se;return!t&&Te(o,"iterate",u?Ss:It),{next(){const{value:p,done:g}=d.next();return g?{value:p,done:g}:{value:a?[f(p[0]),f(p[1])]:f(p),done:g}},[Symbol.iterator](){return this}}}}function Tn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function ul(e,t){const n={get(r){const o=this.__v_raw,i=Q(o),a=Q(r);e||(St(r,a)&&Te(i,"get",r),Te(i,"get",a));const{has:u}=xn(i),d=t?Cs:e?kn:Se;if(u.call(i,r))return d(o.get(r));if(u.call(i,a))return d(o.get(a));o!==i&&o.get(r)},get size(){const r=this.__v_raw;return!e&&Te(Q(r),"iterate",It),Reflect.get(r,"size",r)},has(r){const o=this.__v_raw,i=Q(o),a=Q(r);return e||(St(r,a)&&Te(i,"has",r),Te(i,"has",a)),r===a?o.has(r):o.has(r)||o.has(a)},forEach(r,o){const i=this,a=i.__v_raw,u=Q(a),d=t?Cs:e?kn:Se;return!e&&Te(u,"iterate",It),a.forEach((f,p)=>r.call(o,d(f),d(p),i))}};return be(n,e?{add:Tn("add"),set:Tn("set"),delete:Tn("delete"),clear:Tn("clear")}:{add(r){!t&&!je(r)&&!xt(r)&&(r=Q(r));const o=Q(this);return xn(o).has.call(o,r)||(o.add(r),ot(o,"add",r,r)),this},set(r,o){!t&&!je(o)&&!xt(o)&&(o=Q(o));const i=Q(this),{has:a,get:u}=xn(i);let d=a.call(i,r);d||(r=Q(r),d=a.call(i,r));const f=u.call(i,r);return i.set(r,o),d?St(o,f)&&ot(i,"set",r,o):ot(i,"add",r,o),this},delete(r){const o=Q(this),{has:i,get:a}=xn(o);let u=i.call(o,r);u||(r=Q(r),u=i.call(o,r)),a&&a.call(o,r);const d=o.delete(r);return u&&ot(o,"delete",r,void 0),d},clear(){const r=Q(this),o=r.size!==0,i=r.clear();return o&&ot(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=al(r,e,t)}),n}function Bs(e,t){const n=ul(e,t);return(s,r,o)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(se(n,r)&&r in s?n:s,r,o)}const fl={get:Bs(!1,!1)},dl={get:Bs(!1,!0)},hl={get:Bs(!0,!1)};const To=new WeakMap,Ro=new WeakMap,Ao=new WeakMap,pl=new WeakMap;function gl(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function ml(e){return e.__v_skip||!Object.isExtensible(e)?0:gl(Vi(e))}function Wn(e){return xt(e)?e:Us(e,!1,il,fl,To)}function Po(e){return Us(e,!1,cl,dl,Ro)}function Mo(e){return Us(e,!0,ll,hl,Ao)}function Us(e,t,n,s,r){if(!fe(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=ml(e);if(o===0)return e;const i=r.get(e);if(i)return i;const a=new Proxy(e,o===2?s:n);return r.set(e,a),a}function Bt(e){return xt(e)?Bt(e.__v_raw):!!(e&&e.__v_isReactive)}function xt(e){return!!(e&&e.__v_isReadonly)}function je(e){return!!(e&&e.__v_isShallow)}function Ks(e){return e?!!e.__v_raw:!1}function Q(e){const t=e&&e.__v_raw;return t?Q(t):e}function Oo(e){return!se(e,"__v_skip")&&Object.isExtensible(e)&&ys(e,"__v_skip",!0),e}const Se=e=>fe(e)?Wn(e):e,kn=e=>fe(e)?Mo(e):e;function Pe(e){return e?e.__v_isRef===!0:!1}function de(e){return $o(e,!1)}function vl(e){return $o(e,!0)}function $o(e,t){return Pe(e)?e:new yl(e,t)}class yl{constructor(t,n){this.dep=new js,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:Q(t),this._value=n?t:Se(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||je(t)||xt(t);t=s?t:Q(t),St(t,n)&&(this._rawValue=t,this._value=s?t:Se(t),this.dep.trigger())}}function Ut(e){return Pe(e)?e.value:e}const bl={get:(e,t,n)=>t==="__v_raw"?e:Ut(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return Pe(r)&&!Pe(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function Io(e){return Bt(e)?e:new Proxy(e,bl)}class _l{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new js(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=hn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&ue!==this)return vo(this,!0),!0}get value(){const t=this.dep.track();return _o(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function wl(e,t,n=!1){let s,r;return B(e)?s=e:(s=e.get,r=e.set),new _l(s,r,n)}const Rn={},Ln=new WeakMap;let Ot;function Sl(e,t=!1,n=Ot){if(n){let s=Ln.get(n);s||Ln.set(n,s=[]),s.push(e)}}function Cl(e,t,n=ce){const{immediate:s,deep:r,once:o,scheduler:i,augmentJob:a,call:u}=n,d=L=>r?L:je(L)||r===!1||r===0?it(L,1):it(L);let f,p,g,m,x=!1,S=!1;if(Pe(e)?(p=()=>e.value,x=je(e)):Bt(e)?(p=()=>d(e),x=!0):F(e)?(S=!0,x=e.some(L=>Bt(L)||je(L)),p=()=>e.map(L=>{if(Pe(L))return L.value;if(Bt(L))return d(L);if(B(L))return u?u(L,2):L()})):B(e)?t?p=u?()=>u(e,2):e:p=()=>{if(g){ct();try{g()}finally{at()}}const L=Ot;Ot=f;try{return u?u(e,3,[m]):e(m)}finally{Ot=L}}:p=tt,t&&r){const L=p,K=r===!0?1/0:r;p=()=>it(L(),K)}const $=Xi(),P=()=>{f.stop(),$&&$.active&&Ns($.effects,f)};if(o&&t){const L=t;t=(...K)=>{L(...K),P()}}let I=S?new Array(e.length).fill(Rn):Rn;const N=L=>{if(!(!(f.flags&1)||!f.dirty&&!L))if(t){const K=f.run();if(r||x||(S?K.some((re,Z)=>St(re,I[Z])):St(K,I))){g&&g();const re=Ot;Ot=f;try{const Z=[K,I===Rn?void 0:S&&I[0]===Rn?[]:I,m];I=K,u?u(t,3,Z):t(...Z)}finally{Ot=re}}}else f.run()};return a&&a(N),f=new go(p),f.scheduler=i?()=>i(N,!1):N,m=L=>Sl(L,!1,f),g=f.onStop=()=>{const L=Ln.get(f);if(L){if(u)u(L,4);else for(const K of L)K();Ln.delete(f)}},t?s?N(!0):I=f.run():i?i(N.bind(null,!0),!0):f.run(),P.pause=f.pause.bind(f),P.resume=f.resume.bind(f),P.stop=P,P}function it(e,t=1/0,n){if(t<=0||!fe(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,Pe(e))it(e.value,t,n);else if(F(e))for(let s=0;s<e.length;s++)it(e[s],t,n);else if(oo(e)||jt(e))e.forEach(s=>{it(s,t,n)});else if(co(e)){for(const s in e)it(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&it(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Cn(e,t,n,s){try{return s?e(...s):e()}catch(r){Gn(r,t,n)}}function We(e,t,n,s){if(B(e)){const r=Cn(e,t,n,s);return r&&io(r)&&r.catch(o=>{Gn(o,t,n)}),r}if(F(e)){const r=[];for(let o=0;o<e.length;o++)r.push(We(e[o],t,n,s));return r}}function Gn(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||ce;if(t){let a=t.parent;const u=t.proxy,d=`https://vuejs.org/error-reference/#runtime-${n}`;for(;a;){const f=a.ec;if(f){for(let p=0;p<f.length;p++)if(f[p](e,u,d)===!1)return}a=a.parent}if(o){ct(),Cn(o,null,10,[e,u,d]),at();return}}El(e,n,r,s,i)}function El(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const Oe=[];let Ze=-1;const Kt=[];let vt=null,Ht=0;const ko=Promise.resolve();let Nn=null;function Lo(e){const t=Nn||ko;return e?t.then(this?e.bind(this):e):t}function xl(e){let t=Ze+1,n=Oe.length;for(;t<n;){const s=t+n>>>1,r=Oe[s],o=gn(r);o<e||o===e&&r.flags&2?t=s+1:n=s}return t}function Ws(e){if(!(e.flags&1)){const t=gn(e),n=Oe[Oe.length-1];!n||!(e.flags&2)&&t>=gn(n)?Oe.push(e):Oe.splice(xl(t),0,e),e.flags|=1,No()}}function No(){Nn||(Nn=ko.then(Ho))}function Tl(e){F(e)?Kt.push(...e):vt&&e.id===-1?vt.splice(Ht+1,0,e):e.flags&1||(Kt.push(e),e.flags|=1),No()}function lr(e,t,n=Ze+1){for(;n<Oe.length;n++){const s=Oe[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;Oe.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function zo(e){if(Kt.length){const t=[...new Set(Kt)].sort((n,s)=>gn(n)-gn(s));if(Kt.length=0,vt){vt.push(...t);return}for(vt=t,Ht=0;Ht<vt.length;Ht++){const n=vt[Ht];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}vt=null,Ht=0}}const gn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Ho(e){try{for(Ze=0;Ze<Oe.length;Ze++){const t=Oe[Ze];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Cn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Ze<Oe.length;Ze++){const t=Oe[Ze];t&&(t.flags&=-2)}Ze=-1,Oe.length=0,zo(),Nn=null,(Oe.length||Kt.length)&&Ho()}}let Ce=null,Do=null;function zn(e){const t=Ce;return Ce=e,Do=e&&e.type.__scopeId||null,t}function _e(e,t=Ce,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&vr(-1);const o=zn(t);let i;try{i=e(...r)}finally{zn(o),s._d&&vr(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function Rl(e,t){if(Ce===null)return e;const n=Zn(Ce),s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[o,i,a,u=ce]=t[r];o&&(B(o)&&(o={mounted:o,updated:o}),o.deep&&it(i),s.push({dir:o,instance:n,value:i,oldValue:void 0,arg:a,modifiers:u}))}return e}function At(e,t,n,s){const r=e.dirs,o=t&&t.dirs;for(let i=0;i<r.length;i++){const a=r[i];o&&(a.oldValue=o[i].value);let u=a.dir[s];u&&(ct(),We(u,n,8,[e.el,a,e,t]),at())}}const Al=Symbol("_vte"),Fo=e=>e.__isTeleport,yt=Symbol("_leaveCb"),An=Symbol("_enterCb");function Vo(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Jn(()=>{e.isMounted=!0}),Yo(()=>{e.isUnmounting=!0}),e}const Ve=[Function,Array],jo={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Ve,onEnter:Ve,onAfterEnter:Ve,onEnterCancelled:Ve,onBeforeLeave:Ve,onLeave:Ve,onAfterLeave:Ve,onLeaveCancelled:Ve,onBeforeAppear:Ve,onAppear:Ve,onAfterAppear:Ve,onAppearCancelled:Ve},Bo=e=>{const t=e.subTree;return t.component?Bo(t.component):t},Pl={name:"BaseTransition",props:jo,setup(e,{slots:t}){const n=mi(),s=Vo();return()=>{const r=t.default&&Gs(t.default(),!0);if(!r||!r.length)return;const o=Uo(r),i=Q(e),{mode:a}=i;if(s.isLeaving)return is(o);const u=cr(o);if(!u)return is(o);let d=mn(u,i,s,n,p=>d=p);u.type!==Re&&kt(u,d);let f=n.subTree&&cr(n.subTree);if(f&&f.type!==Re&&!$t(u,f)&&Bo(n).type!==Re){let p=mn(f,i,s,n);if(kt(f,p),a==="out-in"&&u.type!==Re)return s.isLeaving=!0,p.afterLeave=()=>{s.isLeaving=!1,n.job.flags&8||n.update(),delete p.afterLeave,f=void 0},is(o);a==="in-out"&&u.type!==Re?p.delayLeave=(g,m,x)=>{const S=Ko(s,f);S[String(f.key)]=f,g[yt]=()=>{m(),g[yt]=void 0,delete d.delayedLeave,f=void 0},d.delayedLeave=()=>{x(),delete d.delayedLeave,f=void 0}}:f=void 0}else f&&(f=void 0);return o}}};function Uo(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==Re){t=n;break}}return t}const Ml=Pl;function Ko(e,t){const{leavingVNodes:n}=e;let s=n.get(t.type);return s||(s=Object.create(null),n.set(t.type,s)),s}function mn(e,t,n,s,r){const{appear:o,mode:i,persisted:a=!1,onBeforeEnter:u,onEnter:d,onAfterEnter:f,onEnterCancelled:p,onBeforeLeave:g,onLeave:m,onAfterLeave:x,onLeaveCancelled:S,onBeforeAppear:$,onAppear:P,onAfterAppear:I,onAppearCancelled:N}=t,L=String(e.key),K=Ko(n,e),re=(V,G)=>{V&&We(V,s,9,G)},Z=(V,G)=>{const oe=G[1];re(V,G),F(V)?V.every(O=>O.length<=1)&&oe():V.length<=1&&oe()},ve={mode:i,persisted:a,beforeEnter(V){let G=u;if(!n.isMounted)if(o)G=$||u;else return;V[yt]&&V[yt](!0);const oe=K[L];oe&&$t(e,oe)&&oe.el[yt]&&oe.el[yt](),re(G,[V])},enter(V){let G=d,oe=f,O=p;if(!n.isMounted)if(o)G=P||d,oe=I||f,O=N||p;else return;let Y=!1;const pe=V[An]=Ee=>{Y||(Y=!0,Ee?re(O,[V]):re(oe,[V]),ve.delayedLeave&&ve.delayedLeave(),V[An]=void 0)};G?Z(G,[V,pe]):pe()},leave(V,G){const oe=String(e.key);if(V[An]&&V[An](!0),n.isUnmounting)return G();re(g,[V]);let O=!1;const Y=V[yt]=pe=>{O||(O=!0,G(),pe?re(S,[V]):re(x,[V]),V[yt]=void 0,K[oe]===e&&delete K[oe])};K[oe]=e,m?Z(m,[V,Y]):Y()},clone(V){const G=mn(V,t,n,s,r);return r&&r(G),G}};return ve}function is(e){if(qn(e))return e=Tt(e),e.children=null,e}function cr(e){if(!qn(e))return Fo(e.type)&&e.children?Uo(e.children):e;if(e.component)return e.component.subTree;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&B(n.default))return n.default()}}function kt(e,t){e.shapeFlag&6&&e.component?(e.transition=t,kt(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Gs(e,t=!1,n){let s=[],r=0;for(let o=0;o<e.length;o++){let i=e[o];const a=n==null?i.key:String(n)+String(i.key!=null?i.key:o);i.type===me?(i.patchFlag&128&&r++,s=s.concat(Gs(i.children,t,a))):(t||i.type!==Re)&&s.push(a!=null?Tt(i,{key:a}):i)}if(r>1)for(let o=0;o<s.length;o++)s[o].patchFlag=-2;return s}/*! #__NO_SIDE_EFFECTS__ */function Lt(e,t){return B(e)?be({name:e.name},t,{setup:e}):e}function Wo(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function ln(e,t,n,s,r=!1){if(F(e)){e.forEach((x,S)=>ln(x,t&&(F(t)?t[S]:t),n,s,r));return}if(Wt(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&ln(e,t,n,s.component.subTree);return}const o=s.shapeFlag&4?Zn(s.component):s.el,i=r?null:o,{i:a,r:u}=e,d=t&&t.r,f=a.refs===ce?a.refs={}:a.refs,p=a.setupState,g=Q(p),m=p===ce?()=>!1:x=>se(g,x);if(d!=null&&d!==u&&(ge(d)?(f[d]=null,m(d)&&(p[d]=null)):Pe(d)&&(d.value=null)),B(u))Cn(u,a,12,[i,f]);else{const x=ge(u),S=Pe(u);if(x||S){const $=()=>{if(e.f){const P=x?m(u)?p[u]:f[u]:u.value;r?F(P)&&Ns(P,o):F(P)?P.includes(o)||P.push(o):x?(f[u]=[o],m(u)&&(p[u]=f[u])):(u.value=[o],e.k&&(f[e.k]=u.value))}else x?(f[u]=i,m(u)&&(p[u]=i)):S&&(u.value=i,e.k&&(f[e.k]=i))};i?($.id=-1,ze($,n)):$()}}}Un().requestIdleCallback;Un().cancelIdleCallback;const Wt=e=>!!e.type.__asyncLoader,qn=e=>e.type.__isKeepAlive;function Ol(e,t){Go(e,"a",t)}function $l(e,t){Go(e,"da",t)}function Go(e,t,n=Ae){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(Yn(t,s,n),n){let r=n.parent;for(;r&&r.parent;)qn(r.parent.vnode)&&Il(s,t,n,r),r=r.parent}}function Il(e,t,n,s){const r=Yn(t,e,s,!0);qs(()=>{Ns(s[t],r)},n)}function Yn(e,t,n=Ae,s=!1){if(n){const r=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{ct();const a=En(n),u=We(t,n,e,i);return a(),at(),u});return s?r.unshift(o):r.push(o),o}}const ft=e=>(t,n=Ae)=>{(!_n||e==="sp")&&Yn(e,(...s)=>t(...s),n)},kl=ft("bm"),Jn=ft("m"),Ll=ft("bu"),qo=ft("u"),Yo=ft("bum"),qs=ft("um"),Nl=ft("sp"),zl=ft("rtg"),Hl=ft("rtc");function Dl(e,t=Ae){Yn("ec",e,t)}const Fl=Symbol.for("v-ndc");function cn(e,t,n,s){let r;const o=n,i=F(e);if(i||ge(e)){const a=i&&Bt(e);let u=!1,d=!1;a&&(u=!je(e),d=xt(e),e=Kn(e)),r=new Array(e.length);for(let f=0,p=e.length;f<p;f++)r[f]=t(u?d?kn(Se(e[f])):Se(e[f]):e[f],f,void 0,o)}else if(typeof e=="number"){r=new Array(e);for(let a=0;a<e;a++)r[a]=t(a+1,a,void 0,o)}else if(fe(e))if(e[Symbol.iterator])r=Array.from(e,(a,u)=>t(a,u,void 0,o));else{const a=Object.keys(e);r=new Array(a.length);for(let u=0,d=a.length;u<d;u++){const f=a[u];r[u]=t(e[f],f,u,o)}}else r=[];return r}function ls(e,t,n={},s,r){if(Ce.ce||Ce.parent&&Wt(Ce.parent)&&Ce.parent.ce)return t!=="default"&&(n.name=t),U(),yn(me,null,[le("slot",n,s)],64);let o=e[t];o&&o._c&&(o._d=!1),U();const i=o&&Jo(o(n)),a=n.key||i&&i.key,u=yn(me,{key:(a&&!ut(a)?a:`_${t}`)+""},i||[],i&&e._===1?64:-2);return o&&o._c&&(o._d=!0),u}function Jo(e){return e.some(t=>bn(t)?!(t.type===Re||t.type===me&&!Jo(t.children)):!0)?e:null}const Es=e=>e?vi(e)?Zn(e):Es(e.parent):null,an=be(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Es(e.parent),$root:e=>Es(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Xo(e),$forceUpdate:e=>e.f||(e.f=()=>{Ws(e.update)}),$nextTick:e=>e.n||(e.n=Lo.bind(e.proxy)),$watch:e=>lc.bind(e)}),cs=(e,t)=>e!==ce&&!e.__isScriptSetup&&se(e,t),Vl={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:o,accessCache:i,type:a,appContext:u}=e;let d;if(t[0]!=="$"){const m=i[t];if(m!==void 0)switch(m){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return o[t]}else{if(cs(s,t))return i[t]=1,s[t];if(r!==ce&&se(r,t))return i[t]=2,r[t];if((d=e.propsOptions[0])&&se(d,t))return i[t]=3,o[t];if(n!==ce&&se(n,t))return i[t]=4,n[t];xs&&(i[t]=0)}}const f=an[t];let p,g;if(f)return t==="$attrs"&&Te(e.attrs,"get",""),f(e);if((p=a.__cssModules)&&(p=p[t]))return p;if(n!==ce&&se(n,t))return i[t]=4,n[t];if(g=u.config.globalProperties,se(g,t))return g[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:o}=e;return cs(r,t)?(r[t]=n,!0):s!==ce&&se(s,t)?(s[t]=n,!0):se(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:o}},i){let a;return!!n[i]||e!==ce&&se(e,i)||cs(t,i)||(a=o[0])&&se(a,i)||se(s,i)||se(an,i)||se(r.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:se(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function ar(e){return F(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let xs=!0;function jl(e){const t=Xo(e),n=e.proxy,s=e.ctx;xs=!1,t.beforeCreate&&ur(t.beforeCreate,e,"bc");const{data:r,computed:o,methods:i,watch:a,provide:u,inject:d,created:f,beforeMount:p,mounted:g,beforeUpdate:m,updated:x,activated:S,deactivated:$,beforeDestroy:P,beforeUnmount:I,destroyed:N,unmounted:L,render:K,renderTracked:re,renderTriggered:Z,errorCaptured:ve,serverPrefetch:V,expose:G,inheritAttrs:oe,components:O,directives:Y,filters:pe}=t;if(d&&Bl(d,s,null),i)for(const ee in i){const J=i[ee];B(J)&&(s[ee]=J.bind(n))}if(r){const ee=r.call(n,n);fe(ee)&&(e.data=Wn(ee))}if(xs=!0,o)for(const ee in o){const J=o[ee],De=B(J)?J.bind(n,n):B(J.get)?J.get.bind(n,n):tt,Be=!B(J)&&B(J.set)?J.set.bind(n):tt,we=Ue({get:De,set:Be});Object.defineProperty(s,ee,{enumerable:!0,configurable:!0,get:()=>we.value,set:xe=>we.value=xe})}if(a)for(const ee in a)Qo(a[ee],s,n,ee);if(u){const ee=B(u)?u.call(n):u;Reflect.ownKeys(ee).forEach(J=>{Mn(J,ee[J])})}f&&ur(f,e,"c");function he(ee,J){F(J)?J.forEach(De=>ee(De.bind(n))):J&&ee(J.bind(n))}if(he(kl,p),he(Jn,g),he(Ll,m),he(qo,x),he(Ol,S),he($l,$),he(Dl,ve),he(Hl,re),he(zl,Z),he(Yo,I),he(qs,L),he(Nl,V),F(G))if(G.length){const ee=e.exposed||(e.exposed={});G.forEach(J=>{Object.defineProperty(ee,J,{get:()=>n[J],set:De=>n[J]=De})})}else e.exposed||(e.exposed={});K&&e.render===tt&&(e.render=K),oe!=null&&(e.inheritAttrs=oe),O&&(e.components=O),Y&&(e.directives=Y),V&&Wo(e)}function Bl(e,t,n=tt){F(e)&&(e=Ts(e));for(const s in e){const r=e[s];let o;fe(r)?"default"in r?o=lt(r.from||s,r.default,!0):o=lt(r.from||s):o=lt(r),Pe(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[s]=o}}function ur(e,t,n){We(F(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Qo(e,t,n,s){let r=s.includes(".")?fi(n,s):()=>n[s];if(ge(e)){const o=t[e];B(o)&&On(r,o)}else if(B(e))On(r,e.bind(n));else if(fe(e))if(F(e))e.forEach(o=>Qo(o,t,n,s));else{const o=B(e.handler)?e.handler.bind(n):t[e.handler];B(o)&&On(r,o,e)}}function Xo(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,a=o.get(t);let u;return a?u=a:!r.length&&!n&&!s?u=t:(u={},r.length&&r.forEach(d=>Hn(u,d,i,!0)),Hn(u,t,i)),fe(t)&&o.set(t,u),u}function Hn(e,t,n,s=!1){const{mixins:r,extends:o}=t;o&&Hn(e,o,n,!0),r&&r.forEach(i=>Hn(e,i,n,!0));for(const i in t)if(!(s&&i==="expose")){const a=Ul[i]||n&&n[i];e[i]=a?a(e[i],t[i]):t[i]}return e}const Ul={data:fr,props:dr,emits:dr,methods:nn,computed:nn,beforeCreate:Me,created:Me,beforeMount:Me,mounted:Me,beforeUpdate:Me,updated:Me,beforeDestroy:Me,beforeUnmount:Me,destroyed:Me,unmounted:Me,activated:Me,deactivated:Me,errorCaptured:Me,serverPrefetch:Me,components:nn,directives:nn,watch:Wl,provide:fr,inject:Kl};function fr(e,t){return t?e?function(){return be(B(e)?e.call(this,this):e,B(t)?t.call(this,this):t)}:t:e}function Kl(e,t){return nn(Ts(e),Ts(t))}function Ts(e){if(F(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Me(e,t){return e?[...new Set([].concat(e,t))]:t}function nn(e,t){return e?be(Object.create(null),e,t):t}function dr(e,t){return e?F(e)&&F(t)?[...new Set([...e,...t])]:be(Object.create(null),ar(e),ar(t??{})):t}function Wl(e,t){if(!e)return t;if(!t)return e;const n=be(Object.create(null),e);for(const s in t)n[s]=Me(e[s],t[s]);return n}function Zo(){return{app:null,config:{isNativeTag:Di,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Gl=0;function ql(e,t){return function(s,r=null){B(s)||(s=be({},s)),r!=null&&!fe(r)&&(r=null);const o=Zo(),i=new WeakSet,a=[];let u=!1;const d=o.app={_uid:Gl++,_component:s,_props:r,_container:null,_context:o,_instance:null,version:Rc,get config(){return o.config},set config(f){},use(f,...p){return i.has(f)||(f&&B(f.install)?(i.add(f),f.install(d,...p)):B(f)&&(i.add(f),f(d,...p))),d},mixin(f){return o.mixins.includes(f)||o.mixins.push(f),d},component(f,p){return p?(o.components[f]=p,d):o.components[f]},directive(f,p){return p?(o.directives[f]=p,d):o.directives[f]},mount(f,p,g){if(!u){const m=d._ceVNode||le(s,r);return m.appContext=o,g===!0?g="svg":g===!1&&(g=void 0),e(m,f,g),u=!0,d._container=f,f.__vue_app__=d,Zn(m.component)}},onUnmount(f){a.push(f)},unmount(){u&&(We(a,d._instance,16),e(null,d._container),delete d._container.__vue_app__)},provide(f,p){return o.provides[f]=p,d},runWithContext(f){const p=Gt;Gt=d;try{return f()}finally{Gt=p}}};return d}}let Gt=null;function Mn(e,t){if(Ae){let n=Ae.provides;const s=Ae.parent&&Ae.parent.provides;s===n&&(n=Ae.provides=Object.create(s)),n[e]=t}}function lt(e,t,n=!1){const s=Ae||Ce;if(s||Gt){let r=Gt?Gt._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&B(t)?t.call(s&&s.proxy):t}}const ei={},ti=()=>Object.create(ei),ni=e=>Object.getPrototypeOf(e)===ei;function Yl(e,t,n,s=!1){const r={},o=ti();e.propsDefaults=Object.create(null),si(e,t,r,o);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);n?e.props=s?r:Po(r):e.type.props?e.props=r:e.props=o,e.attrs=o}function Jl(e,t,n,s){const{props:r,attrs:o,vnode:{patchFlag:i}}=e,a=Q(r),[u]=e.propsOptions;let d=!1;if((s||i>0)&&!(i&16)){if(i&8){const f=e.vnode.dynamicProps;for(let p=0;p<f.length;p++){let g=f[p];if(Qn(e.emitsOptions,g))continue;const m=t[g];if(u)if(se(o,g))m!==o[g]&&(o[g]=m,d=!0);else{const x=Ct(g);r[x]=Rs(u,a,x,m,e,!1)}else m!==o[g]&&(o[g]=m,d=!0)}}}else{si(e,t,r,o)&&(d=!0);let f;for(const p in a)(!t||!se(t,p)&&((f=Rt(p))===p||!se(t,f)))&&(u?n&&(n[p]!==void 0||n[f]!==void 0)&&(r[p]=Rs(u,a,p,void 0,e,!0)):delete r[p]);if(o!==a)for(const p in o)(!t||!se(t,p))&&(delete o[p],d=!0)}d&&ot(e.attrs,"set","")}function si(e,t,n,s){const[r,o]=e.propsOptions;let i=!1,a;if(t)for(let u in t){if(sn(u))continue;const d=t[u];let f;r&&se(r,f=Ct(u))?!o||!o.includes(f)?n[f]=d:(a||(a={}))[f]=d:Qn(e.emitsOptions,u)||(!(u in s)||d!==s[u])&&(s[u]=d,i=!0)}if(o){const u=Q(n),d=a||ce;for(let f=0;f<o.length;f++){const p=o[f];n[p]=Rs(r,u,p,d[p],e,!se(d,p))}}return i}function Rs(e,t,n,s,r,o){const i=e[n];if(i!=null){const a=se(i,"default");if(a&&s===void 0){const u=i.default;if(i.type!==Function&&!i.skipFactory&&B(u)){const{propsDefaults:d}=r;if(n in d)s=d[n];else{const f=En(r);s=d[n]=u.call(null,t),f()}}else s=u;r.ce&&r.ce._setProp(n,s)}i[0]&&(o&&!a?s=!1:i[1]&&(s===""||s===Rt(n))&&(s=!0))}return s}const Ql=new WeakMap;function ri(e,t,n=!1){const s=n?Ql:t.propsCache,r=s.get(e);if(r)return r;const o=e.props,i={},a=[];let u=!1;if(!B(e)){const f=p=>{u=!0;const[g,m]=ri(p,t,!0);be(i,g),m&&a.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(f),e.extends&&f(e.extends),e.mixins&&e.mixins.forEach(f)}if(!o&&!u)return fe(e)&&s.set(e,Vt),Vt;if(F(o))for(let f=0;f<o.length;f++){const p=Ct(o[f]);hr(p)&&(i[p]=ce)}else if(o)for(const f in o){const p=Ct(f);if(hr(p)){const g=o[f],m=i[p]=F(g)||B(g)?{type:g}:be({},g),x=m.type;let S=!1,$=!0;if(F(x))for(let P=0;P<x.length;++P){const I=x[P],N=B(I)&&I.name;if(N==="Boolean"){S=!0;break}else N==="String"&&($=!1)}else S=B(x)&&x.name==="Boolean";m[0]=S,m[1]=$,(S||se(m,"default"))&&a.push(p)}}const d=[i,a];return fe(e)&&s.set(e,d),d}function hr(e){return e[0]!=="$"&&!sn(e)}const Ys=e=>e[0]==="_"||e==="$stable",Js=e=>F(e)?e.map(et):[et(e)],Xl=(e,t,n)=>{if(t._n)return t;const s=_e((...r)=>Js(t(...r)),n);return s._c=!1,s},oi=(e,t,n)=>{const s=e._ctx;for(const r in e){if(Ys(r))continue;const o=e[r];if(B(o))t[r]=Xl(r,o,s);else if(o!=null){const i=Js(o);t[r]=()=>i}}},ii=(e,t)=>{const n=Js(t);e.slots.default=()=>n},li=(e,t,n)=>{for(const s in t)(n||!Ys(s))&&(e[s]=t[s])},Zl=(e,t,n)=>{const s=e.slots=ti();if(e.vnode.shapeFlag&32){const r=t.__;r&&ys(s,"__",r,!0);const o=t._;o?(li(s,t,n),n&&ys(s,"_",o,!0)):oi(t,s)}else t&&ii(e,t)},ec=(e,t,n)=>{const{vnode:s,slots:r}=e;let o=!0,i=ce;if(s.shapeFlag&32){const a=t._;a?n&&a===1?o=!1:li(r,t,n):(o=!t.$stable,oi(t,r)),i=t}else t&&(ii(e,t),i={default:1});if(o)for(const a in r)!Ys(a)&&i[a]==null&&delete r[a]},ze=pc;function tc(e){return nc(e)}function nc(e,t){const n=Un();n.__VUE__=!0;const{insert:s,remove:r,patchProp:o,createElement:i,createText:a,createComment:u,setText:d,setElementText:f,parentNode:p,nextSibling:g,setScopeId:m=tt,insertStaticContent:x}=e,S=(c,l,h,v=null,y=null,b=null,E=void 0,R=null,T=!!l.dynamicChildren)=>{if(c===l)return;c&&!$t(c,l)&&(v=_(c),xe(c,y,b,!0),c=null),l.patchFlag===-2&&(T=!1,l.dynamicChildren=null);const{type:w,ref:D,shapeFlag:M}=l;switch(w){case Xn:$(c,l,h,v);break;case Re:P(c,l,h,v);break;case us:c==null&&I(l,h,v,E);break;case me:O(c,l,h,v,y,b,E,R,T);break;default:M&1?K(c,l,h,v,y,b,E,R,T):M&6?Y(c,l,h,v,y,b,E,R,T):(M&64||M&128)&&w.process(c,l,h,v,y,b,E,R,T,z)}D!=null&&y?ln(D,c&&c.ref,b,l||c,!l):D==null&&c&&c.ref!=null&&ln(c.ref,null,b,c,!0)},$=(c,l,h,v)=>{if(c==null)s(l.el=a(l.children),h,v);else{const y=l.el=c.el;l.children!==c.children&&d(y,l.children)}},P=(c,l,h,v)=>{c==null?s(l.el=u(l.children||""),h,v):l.el=c.el},I=(c,l,h,v)=>{[c.el,c.anchor]=x(c.children,l,h,v,c.el,c.anchor)},N=({el:c,anchor:l},h,v)=>{let y;for(;c&&c!==l;)y=g(c),s(c,h,v),c=y;s(l,h,v)},L=({el:c,anchor:l})=>{let h;for(;c&&c!==l;)h=g(c),r(c),c=h;r(l)},K=(c,l,h,v,y,b,E,R,T)=>{l.type==="svg"?E="svg":l.type==="math"&&(E="mathml"),c==null?re(l,h,v,y,b,E,R,T):V(c,l,y,b,E,R,T)},re=(c,l,h,v,y,b,E,R)=>{let T,w;const{props:D,shapeFlag:M,transition:H,dirs:j}=c;if(T=c.el=i(c.type,b,D&&D.is,D),M&8?f(T,c.children):M&16&&ve(c.children,T,null,v,y,as(c,b),E,R),j&&At(c,null,v,"created"),Z(T,c,c.scopeId,E,v),D){for(const ae in D)ae!=="value"&&!sn(ae)&&o(T,ae,null,D[ae],b,v);"value"in D&&o(T,"value",null,D.value,b),(w=D.onVnodeBeforeMount)&&Qe(w,v,c)}j&&At(c,null,v,"beforeMount");const W=sc(y,H);W&&H.beforeEnter(T),s(T,l,h),((w=D&&D.onVnodeMounted)||W||j)&&ze(()=>{w&&Qe(w,v,c),W&&H.enter(T),j&&At(c,null,v,"mounted")},y)},Z=(c,l,h,v,y)=>{if(h&&m(c,h),v)for(let b=0;b<v.length;b++)m(c,v[b]);if(y){let b=y.subTree;if(l===b||hi(b.type)&&(b.ssContent===l||b.ssFallback===l)){const E=y.vnode;Z(c,E,E.scopeId,E.slotScopeIds,y.parent)}}},ve=(c,l,h,v,y,b,E,R,T=0)=>{for(let w=T;w<c.length;w++){const D=c[w]=R?bt(c[w]):et(c[w]);S(null,D,l,h,v,y,b,E,R)}},V=(c,l,h,v,y,b,E)=>{const R=l.el=c.el;let{patchFlag:T,dynamicChildren:w,dirs:D}=l;T|=c.patchFlag&16;const M=c.props||ce,H=l.props||ce;let j;if(h&&Pt(h,!1),(j=H.onVnodeBeforeUpdate)&&Qe(j,h,l,c),D&&At(l,c,h,"beforeUpdate"),h&&Pt(h,!0),(M.innerHTML&&H.innerHTML==null||M.textContent&&H.textContent==null)&&f(R,""),w?G(c.dynamicChildren,w,R,h,v,as(l,y),b):E||J(c,l,R,null,h,v,as(l,y),b,!1),T>0){if(T&16)oe(R,M,H,h,y);else if(T&2&&M.class!==H.class&&o(R,"class",null,H.class,y),T&4&&o(R,"style",M.style,H.style,y),T&8){const W=l.dynamicProps;for(let ae=0;ae<W.length;ae++){const ie=W[ae],Ie=M[ie],ke=H[ie];(ke!==Ie||ie==="value")&&o(R,ie,Ie,ke,y,h)}}T&1&&c.children!==l.children&&f(R,l.children)}else!E&&w==null&&oe(R,M,H,h,y);((j=H.onVnodeUpdated)||D)&&ze(()=>{j&&Qe(j,h,l,c),D&&At(l,c,h,"updated")},v)},G=(c,l,h,v,y,b,E)=>{for(let R=0;R<l.length;R++){const T=c[R],w=l[R],D=T.el&&(T.type===me||!$t(T,w)||T.shapeFlag&198)?p(T.el):h;S(T,w,D,null,v,y,b,E,!0)}},oe=(c,l,h,v,y)=>{if(l!==h){if(l!==ce)for(const b in l)!sn(b)&&!(b in h)&&o(c,b,l[b],null,y,v);for(const b in h){if(sn(b))continue;const E=h[b],R=l[b];E!==R&&b!=="value"&&o(c,b,R,E,y,v)}"value"in h&&o(c,"value",l.value,h.value,y)}},O=(c,l,h,v,y,b,E,R,T)=>{const w=l.el=c?c.el:a(""),D=l.anchor=c?c.anchor:a("");let{patchFlag:M,dynamicChildren:H,slotScopeIds:j}=l;j&&(R=R?R.concat(j):j),c==null?(s(w,h,v),s(D,h,v),ve(l.children||[],h,D,y,b,E,R,T)):M>0&&M&64&&H&&c.dynamicChildren?(G(c.dynamicChildren,H,h,y,b,E,R),(l.key!=null||y&&l===y.subTree)&&ci(c,l,!0)):J(c,l,h,D,y,b,E,R,T)},Y=(c,l,h,v,y,b,E,R,T)=>{l.slotScopeIds=R,c==null?l.shapeFlag&512?y.ctx.activate(l,h,v,E,T):pe(l,h,v,y,b,E,T):Ee(c,l,T)},pe=(c,l,h,v,y,b,E)=>{const R=c.component=wc(c,v,y);if(qn(c)&&(R.ctx.renderer=z),Sc(R,!1,E),R.asyncDep){if(y&&y.registerDep(R,he,E),!c.el){const T=R.subTree=le(Re);P(null,T,l,h)}}else he(R,c,l,h,y,b,E)},Ee=(c,l,h)=>{const v=l.component=c.component;if(dc(c,l,h))if(v.asyncDep&&!v.asyncResolved){ee(v,l,h);return}else v.next=l,v.update();else l.el=c.el,v.vnode=l},he=(c,l,h,v,y,b,E)=>{const R=()=>{if(c.isMounted){let{next:M,bu:H,u:j,parent:W,vnode:ae}=c;{const Ye=ai(c);if(Ye){M&&(M.el=ae.el,ee(c,M,E)),Ye.asyncDep.then(()=>{c.isUnmounted||R()});return}}let ie=M,Ie;Pt(c,!1),M?(M.el=ae.el,ee(c,M,E)):M=ae,H&&Pn(H),(Ie=M.props&&M.props.onVnodeBeforeUpdate)&&Qe(Ie,W,M,ae),Pt(c,!0);const ke=gr(c),qe=c.subTree;c.subTree=ke,S(qe,ke,p(qe.el),_(qe),c,y,b),M.el=ke.el,ie===null&&hc(c,ke.el),j&&ze(j,y),(Ie=M.props&&M.props.onVnodeUpdated)&&ze(()=>Qe(Ie,W,M,ae),y)}else{let M;const{el:H,props:j}=l,{bm:W,m:ae,parent:ie,root:Ie,type:ke}=c,qe=Wt(l);Pt(c,!1),W&&Pn(W),!qe&&(M=j&&j.onVnodeBeforeMount)&&Qe(M,ie,l),Pt(c,!0);{Ie.ce&&Ie.ce._def.shadowRoot!==!1&&Ie.ce._injectChildStyle(ke);const Ye=c.subTree=gr(c);S(null,Ye,h,v,c,y,b),l.el=Ye.el}if(ae&&ze(ae,y),!qe&&(M=j&&j.onVnodeMounted)){const Ye=l;ze(()=>Qe(M,ie,Ye),y)}(l.shapeFlag&256||ie&&Wt(ie.vnode)&&ie.vnode.shapeFlag&256)&&c.a&&ze(c.a,y),c.isMounted=!0,l=h=v=null}};c.scope.on();const T=c.effect=new go(R);c.scope.off();const w=c.update=T.run.bind(T),D=c.job=T.runIfDirty.bind(T);D.i=c,D.id=c.uid,T.scheduler=()=>Ws(D),Pt(c,!0),w()},ee=(c,l,h)=>{l.component=c;const v=c.vnode.props;c.vnode=l,c.next=null,Jl(c,l.props,v,h),ec(c,l.children,h),ct(),lr(c),at()},J=(c,l,h,v,y,b,E,R,T=!1)=>{const w=c&&c.children,D=c?c.shapeFlag:0,M=l.children,{patchFlag:H,shapeFlag:j}=l;if(H>0){if(H&128){Be(w,M,h,v,y,b,E,R,T);return}else if(H&256){De(w,M,h,v,y,b,E,R,T);return}}j&8?(D&16&&$e(w,y,b),M!==w&&f(h,M)):D&16?j&16?Be(w,M,h,v,y,b,E,R,T):$e(w,y,b,!0):(D&8&&f(h,""),j&16&&ve(M,h,v,y,b,E,R,T))},De=(c,l,h,v,y,b,E,R,T)=>{c=c||Vt,l=l||Vt;const w=c.length,D=l.length,M=Math.min(w,D);let H;for(H=0;H<M;H++){const j=l[H]=T?bt(l[H]):et(l[H]);S(c[H],j,h,null,y,b,E,R,T)}w>D?$e(c,y,b,!0,!1,M):ve(l,h,v,y,b,E,R,T,M)},Be=(c,l,h,v,y,b,E,R,T)=>{let w=0;const D=l.length;let M=c.length-1,H=D-1;for(;w<=M&&w<=H;){const j=c[w],W=l[w]=T?bt(l[w]):et(l[w]);if($t(j,W))S(j,W,h,null,y,b,E,R,T);else break;w++}for(;w<=M&&w<=H;){const j=c[M],W=l[H]=T?bt(l[H]):et(l[H]);if($t(j,W))S(j,W,h,null,y,b,E,R,T);else break;M--,H--}if(w>M){if(w<=H){const j=H+1,W=j<D?l[j].el:v;for(;w<=H;)S(null,l[w]=T?bt(l[w]):et(l[w]),h,W,y,b,E,R,T),w++}}else if(w>H)for(;w<=M;)xe(c[w],y,b,!0),w++;else{const j=w,W=w,ae=new Map;for(w=W;w<=H;w++){const Ne=l[w]=T?bt(l[w]):et(l[w]);Ne.key!=null&&ae.set(Ne.key,w)}let ie,Ie=0;const ke=H-W+1;let qe=!1,Ye=0;const Qt=new Array(ke);for(w=0;w<ke;w++)Qt[w]=0;for(w=j;w<=M;w++){const Ne=c[w];if(Ie>=ke){xe(Ne,y,b,!0);continue}let Je;if(Ne.key!=null)Je=ae.get(Ne.key);else for(ie=W;ie<=H;ie++)if(Qt[ie-W]===0&&$t(Ne,l[ie])){Je=ie;break}Je===void 0?xe(Ne,y,b,!0):(Qt[Je-W]=w+1,Je>=Ye?Ye=Je:qe=!0,S(Ne,l[Je],h,null,y,b,E,R,T),Ie++)}const nr=qe?rc(Qt):Vt;for(ie=nr.length-1,w=ke-1;w>=0;w--){const Ne=W+w,Je=l[Ne],sr=Ne+1<D?l[Ne+1].el:v;Qt[w]===0?S(null,Je,h,sr,y,b,E,R,T):qe&&(ie<0||w!==nr[ie]?we(Je,h,sr,2):ie--)}}},we=(c,l,h,v,y=null)=>{const{el:b,type:E,transition:R,children:T,shapeFlag:w}=c;if(w&6){we(c.component.subTree,l,h,v);return}if(w&128){c.suspense.move(l,h,v);return}if(w&64){E.move(c,l,h,z);return}if(E===me){s(b,l,h);for(let M=0;M<T.length;M++)we(T[M],l,h,v);s(c.anchor,l,h);return}if(E===us){N(c,l,h);return}if(v!==2&&w&1&&R)if(v===0)R.beforeEnter(b),s(b,l,h),ze(()=>R.enter(b),y);else{const{leave:M,delayLeave:H,afterLeave:j}=R,W=()=>{c.ctx.isUnmounted?r(b):s(b,l,h)},ae=()=>{M(b,()=>{W(),j&&j()})};H?H(b,W,ae):ae()}else s(b,l,h)},xe=(c,l,h,v=!1,y=!1)=>{const{type:b,props:E,ref:R,children:T,dynamicChildren:w,shapeFlag:D,patchFlag:M,dirs:H,cacheIndex:j}=c;if(M===-2&&(y=!1),R!=null&&(ct(),ln(R,null,h,c,!0),at()),j!=null&&(l.renderCache[j]=void 0),D&256){l.ctx.deactivate(c);return}const W=D&1&&H,ae=!Wt(c);let ie;if(ae&&(ie=E&&E.onVnodeBeforeUnmount)&&Qe(ie,l,c),D&6)Nt(c.component,h,v);else{if(D&128){c.suspense.unmount(h,v);return}W&&At(c,null,l,"beforeUnmount"),D&64?c.type.remove(c,l,h,z,v):w&&!w.hasOnce&&(b!==me||M>0&&M&64)?$e(w,l,h,!1,!0):(b===me&&M&384||!y&&D&16)&&$e(T,l,h),v&&Fe(c)}(ae&&(ie=E&&E.onVnodeUnmounted)||W)&&ze(()=>{ie&&Qe(ie,l,c),W&&At(c,null,l,"unmounted")},h)},Fe=c=>{const{type:l,el:h,anchor:v,transition:y}=c;if(l===me){ht(h,v);return}if(l===us){L(c);return}const b=()=>{r(h),y&&!y.persisted&&y.afterLeave&&y.afterLeave()};if(c.shapeFlag&1&&y&&!y.persisted){const{leave:E,delayLeave:R}=y,T=()=>E(h,b);R?R(c.el,b,T):T()}else b()},ht=(c,l)=>{let h;for(;c!==l;)h=g(c),r(c),c=h;r(l)},Nt=(c,l,h)=>{const{bum:v,scope:y,job:b,subTree:E,um:R,m:T,a:w,parent:D,slots:{__:M}}=c;pr(T),pr(w),v&&Pn(v),D&&F(M)&&M.forEach(H=>{D.renderCache[H]=void 0}),y.stop(),b&&(b.flags|=8,xe(E,c,l,h)),R&&ze(R,l),ze(()=>{c.isUnmounted=!0},l),l&&l.pendingBranch&&!l.isUnmounted&&c.asyncDep&&!c.asyncResolved&&c.suspenseId===l.pendingId&&(l.deps--,l.deps===0&&l.resolve())},$e=(c,l,h,v=!1,y=!1,b=0)=>{for(let E=b;E<c.length;E++)xe(c[E],l,h,v,y)},_=c=>{if(c.shapeFlag&6)return _(c.component.subTree);if(c.shapeFlag&128)return c.suspense.next();const l=g(c.anchor||c.el),h=l&&l[Al];return h?g(h):l};let k=!1;const A=(c,l,h)=>{c==null?l._vnode&&xe(l._vnode,null,null,!0):S(l._vnode||null,c,l,null,null,null,h),l._vnode=c,k||(k=!0,lr(),zo(),k=!1)},z={p:S,um:xe,m:we,r:Fe,mt:pe,mc:ve,pc:J,pbc:G,n:_,o:e};return{render:A,hydrate:void 0,createApp:ql(A)}}function as({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Pt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function sc(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function ci(e,t,n=!1){const s=e.children,r=t.children;if(F(s)&&F(r))for(let o=0;o<s.length;o++){const i=s[o];let a=r[o];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=r[o]=bt(r[o]),a.el=i.el),!n&&a.patchFlag!==-2&&ci(i,a)),a.type===Xn&&(a.el=i.el),a.type===Re&&!a.el&&(a.el=i.el)}}function rc(e){const t=e.slice(),n=[0];let s,r,o,i,a;const u=e.length;for(s=0;s<u;s++){const d=e[s];if(d!==0){if(r=n[n.length-1],e[r]<d){t[s]=r,n.push(s);continue}for(o=0,i=n.length-1;o<i;)a=o+i>>1,e[n[a]]<d?o=a+1:i=a;d<e[n[o]]&&(o>0&&(t[s]=n[o-1]),n[o]=s)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function ai(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:ai(t)}function pr(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const oc=Symbol.for("v-scx"),ic=()=>lt(oc);function On(e,t,n){return ui(e,t,n)}function ui(e,t,n=ce){const{immediate:s,deep:r,flush:o,once:i}=n,a=be({},n),u=t&&s||!t&&o!=="post";let d;if(_n){if(o==="sync"){const m=ic();d=m.__watcherHandles||(m.__watcherHandles=[])}else if(!u){const m=()=>{};return m.stop=tt,m.resume=tt,m.pause=tt,m}}const f=Ae;a.call=(m,x,S)=>We(m,f,x,S);let p=!1;o==="post"?a.scheduler=m=>{ze(m,f&&f.suspense)}:o!=="sync"&&(p=!0,a.scheduler=(m,x)=>{x?m():Ws(m)}),a.augmentJob=m=>{t&&(m.flags|=4),p&&(m.flags|=2,f&&(m.id=f.uid,m.i=f))};const g=Cl(e,t,a);return _n&&(d?d.push(g):u&&g()),g}function lc(e,t,n){const s=this.proxy,r=ge(e)?e.includes(".")?fi(s,e):()=>s[e]:e.bind(s,s);let o;B(t)?o=t:(o=t.handler,n=t);const i=En(this),a=ui(r,o.bind(s),n);return i(),a}function fi(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const cc=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Ct(t)}Modifiers`]||e[`${Rt(t)}Modifiers`];function ac(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||ce;let r=n;const o=t.startsWith("update:"),i=o&&cc(s,t.slice(7));i&&(i.trim&&(r=n.map(f=>ge(f)?f.trim():f)),i.number&&(r=n.map(bs)));let a,u=s[a=ts(t)]||s[a=ts(Ct(t))];!u&&o&&(u=s[a=ts(Rt(t))]),u&&We(u,e,6,r);const d=s[a+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,We(d,e,6,r)}}function di(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const o=e.emits;let i={},a=!1;if(!B(e)){const u=d=>{const f=di(d,t,!0);f&&(a=!0,be(i,f))};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}return!o&&!a?(fe(e)&&s.set(e,null),null):(F(o)?o.forEach(u=>i[u]=null):be(i,o),fe(e)&&s.set(e,i),i)}function Qn(e,t){return!e||!Vn(t)?!1:(t=t.slice(2).replace(/Once$/,""),se(e,t[0].toLowerCase()+t.slice(1))||se(e,Rt(t))||se(e,t))}function gr(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[o],slots:i,attrs:a,emit:u,render:d,renderCache:f,props:p,data:g,setupState:m,ctx:x,inheritAttrs:S}=e,$=zn(e);let P,I;try{if(n.shapeFlag&4){const L=r||s,K=L;P=et(d.call(K,L,f,p,m,g,x)),I=a}else{const L=t;P=et(L.length>1?L(p,{attrs:a,slots:i,emit:u}):L(p,null)),I=t.props?a:uc(a)}}catch(L){un.length=0,Gn(L,e,1),P=le(Re)}let N=P;if(I&&S!==!1){const L=Object.keys(I),{shapeFlag:K}=N;L.length&&K&7&&(o&&L.some(Ls)&&(I=fc(I,o)),N=Tt(N,I,!1,!0))}return n.dirs&&(N=Tt(N,null,!1,!0),N.dirs=N.dirs?N.dirs.concat(n.dirs):n.dirs),n.transition&&kt(N,n.transition),P=N,zn($),P}const uc=e=>{let t;for(const n in e)(n==="class"||n==="style"||Vn(n))&&((t||(t={}))[n]=e[n]);return t},fc=(e,t)=>{const n={};for(const s in e)(!Ls(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function dc(e,t,n){const{props:s,children:r,component:o}=e,{props:i,children:a,patchFlag:u}=t,d=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&u>=0){if(u&1024)return!0;if(u&16)return s?mr(s,i,d):!!i;if(u&8){const f=t.dynamicProps;for(let p=0;p<f.length;p++){const g=f[p];if(i[g]!==s[g]&&!Qn(d,g))return!0}}}else return(r||a)&&(!a||!a.$stable)?!0:s===i?!1:s?i?mr(s,i,d):!0:!!i;return!1}function mr(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const o=s[r];if(t[o]!==e[o]&&!Qn(n,o))return!0}return!1}function hc({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const hi=e=>e.__isSuspense;function pc(e,t){t&&t.pendingBranch?F(e)?t.effects.push(...e):t.effects.push(e):Tl(e)}const me=Symbol.for("v-fgt"),Xn=Symbol.for("v-txt"),Re=Symbol.for("v-cmt"),us=Symbol.for("v-stc"),un=[];let He=null;function U(e=!1){un.push(He=e?null:[])}function gc(){un.pop(),He=un[un.length-1]||null}let vn=1;function vr(e,t=!1){vn+=e,e<0&&He&&t&&(He.hasOnce=!0)}function pi(e){return e.dynamicChildren=vn>0?He||Vt:null,gc(),vn>0&&He&&He.push(e),e}function q(e,t,n,s,r,o){return pi(C(e,t,n,s,r,o,!0))}function yn(e,t,n,s,r){return pi(le(e,t,n,s,r,!0))}function bn(e){return e?e.__v_isVNode===!0:!1}function $t(e,t){return e.type===t.type&&e.key===t.key}const gi=({key:e})=>e??null,$n=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?ge(e)||Pe(e)||B(e)?{i:Ce,r:e,k:t,f:!!n}:e:null);function C(e,t=null,n=null,s=0,r=null,o=e===me?0:1,i=!1,a=!1){const u={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&gi(t),ref:t&&$n(t),scopeId:Do,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Ce};return a?(Qs(u,n),o&128&&e.normalize(u)):n&&(u.shapeFlag|=ge(n)?8:16),vn>0&&!i&&He&&(u.patchFlag>0||o&6)&&u.patchFlag!==32&&He.push(u),u}const le=mc;function mc(e,t=null,n=null,s=0,r=null,o=!1){if((!e||e===Fl)&&(e=Re),bn(e)){const a=Tt(e,t,!0);return n&&Qs(a,n),vn>0&&!o&&He&&(a.shapeFlag&6?He[He.indexOf(e)]=a:He.push(a)),a.patchFlag=-2,a}if(Tc(e)&&(e=e.__vccOpts),t){t=vc(t);let{class:a,style:u}=t;a&&!ge(a)&&(t.class=Et(a)),fe(u)&&(Ks(u)&&!F(u)&&(u=be({},u)),t.style=Hs(u))}const i=ge(e)?1:hi(e)?128:Fo(e)?64:fe(e)?4:B(e)?2:0;return C(e,t,n,s,r,i,o,!0)}function vc(e){return e?Ks(e)||ni(e)?be({},e):e:null}function Tt(e,t,n=!1,s=!1){const{props:r,ref:o,patchFlag:i,children:a,transition:u}=e,d=t?yc(r||{},t):r,f={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&gi(d),ref:t&&t.ref?n&&o?F(o)?o.concat($n(t)):[o,$n(t)]:$n(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==me?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:u,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Tt(e.ssContent),ssFallback:e.ssFallback&&Tt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return u&&s&&kt(f,u.clone(f)),f}function X(e=" ",t=0){return le(Xn,null,e,t)}function wt(e="",t=!1){return t?(U(),yn(Re,null,e)):le(Re,null,e)}function et(e){return e==null||typeof e=="boolean"?le(Re):F(e)?le(me,null,e.slice()):bn(e)?bt(e):le(Xn,null,String(e))}function bt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Tt(e)}function Qs(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(F(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),Qs(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!ni(t)?t._ctx=Ce:r===3&&Ce&&(Ce.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else B(t)?(t={default:t,_ctx:Ce},n=32):(t=String(t),s&64?(n=16,t=[X(t)]):n=8);e.children=t,e.shapeFlag|=n}function yc(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=Et([t.class,s.class]));else if(r==="style")t.style=Hs([t.style,s.style]);else if(Vn(r)){const o=t[r],i=s[r];i&&o!==i&&!(F(o)&&o.includes(i))&&(t[r]=o?[].concat(o,i):i)}else r!==""&&(t[r]=s[r])}return t}function Qe(e,t,n,s=null){We(e,t,7,[n,s])}const bc=Zo();let _c=0;function wc(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||bc,o={uid:_c++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new po(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:ri(s,r),emitsOptions:di(s,r),emit:null,emitted:null,propsDefaults:ce,inheritAttrs:s.inheritAttrs,ctx:ce,data:ce,props:ce,attrs:ce,slots:ce,refs:ce,setupState:ce,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=ac.bind(null,o),e.ce&&e.ce(o),o}let Ae=null;const mi=()=>Ae||Ce;let Dn,As;{const e=Un(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),o=>{r.length>1?r.forEach(i=>i(o)):r[0](o)}};Dn=t("__VUE_INSTANCE_SETTERS__",n=>Ae=n),As=t("__VUE_SSR_SETTERS__",n=>_n=n)}const En=e=>{const t=Ae;return Dn(e),e.scope.on(),()=>{e.scope.off(),Dn(t)}},yr=()=>{Ae&&Ae.scope.off(),Dn(null)};function vi(e){return e.vnode.shapeFlag&4}let _n=!1;function Sc(e,t=!1,n=!1){t&&As(t);const{props:s,children:r}=e.vnode,o=vi(e);Yl(e,s,o,t),Zl(e,r,n||t);const i=o?Cc(e,t):void 0;return t&&As(!1),i}function Cc(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Vl);const{setup:s}=n;if(s){ct();const r=e.setupContext=s.length>1?xc(e):null,o=En(e),i=Cn(s,e,0,[e.props,r]),a=io(i);if(at(),o(),(a||e.sp)&&!Wt(e)&&Wo(e),a){if(i.then(yr,yr),t)return i.then(u=>{br(e,u)}).catch(u=>{Gn(u,e,0)});e.asyncDep=i}else br(e,i)}else yi(e)}function br(e,t,n){B(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:fe(t)&&(e.setupState=Io(t)),yi(e)}function yi(e,t,n){const s=e.type;e.render||(e.render=s.render||tt);{const r=En(e);ct();try{jl(e)}finally{at(),r()}}}const Ec={get(e,t){return Te(e,"get",""),e[t]}};function xc(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Ec),slots:e.slots,emit:e.emit,expose:t}}function Zn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Io(Oo(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in an)return an[n](e)},has(t,n){return n in t||n in an}})):e.proxy}function Tc(e){return B(e)&&"__vccOpts"in e}const Ue=(e,t)=>wl(e,t,_n);function Xs(e,t,n){const s=arguments.length;return s===2?fe(t)&&!F(t)?bn(t)?le(e,null,[t]):le(e,t):le(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&bn(n)&&(n=[n]),le(e,t,n))}const Rc="3.5.17";/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ps;const _r=typeof window<"u"&&window.trustedTypes;if(_r)try{Ps=_r.createPolicy("vue",{createHTML:e=>e})}catch{}const bi=Ps?e=>Ps.createHTML(e):e=>e,Ac="http://www.w3.org/2000/svg",Pc="http://www.w3.org/1998/Math/MathML",rt=typeof document<"u"?document:null,wr=rt&&rt.createElement("template"),Mc={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?rt.createElementNS(Ac,e):t==="mathml"?rt.createElementNS(Pc,e):n?rt.createElement(e,{is:n}):rt.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>rt.createTextNode(e),createComment:e=>rt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>rt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,o){const i=n?n.previousSibling:t.lastChild;if(r&&(r===o||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===o||!(r=r.nextSibling)););else{wr.innerHTML=bi(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const a=wr.content;if(s==="svg"||s==="mathml"){const u=a.firstChild;for(;u.firstChild;)a.appendChild(u.firstChild);a.removeChild(u)}t.insertBefore(a,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},pt="transition",Zt="animation",qt=Symbol("_vtc"),_i={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},wi=be({},jo,_i),Oc=e=>(e.displayName="Transition",e.props=wi,e),$c=Oc((e,{slots:t})=>Xs(Ml,Si(e),t)),Mt=(e,t=[])=>{F(e)?e.forEach(n=>n(...t)):e&&e(...t)},Sr=e=>e?F(e)?e.some(t=>t.length>1):e.length>1:!1;function Si(e){const t={};for(const O in e)O in _i||(t[O]=e[O]);if(e.css===!1)return t;const{name:n="v",type:s,duration:r,enterFromClass:o=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:u=o,appearActiveClass:d=i,appearToClass:f=a,leaveFromClass:p=`${n}-leave-from`,leaveActiveClass:g=`${n}-leave-active`,leaveToClass:m=`${n}-leave-to`}=e,x=Ic(r),S=x&&x[0],$=x&&x[1],{onBeforeEnter:P,onEnter:I,onEnterCancelled:N,onLeave:L,onLeaveCancelled:K,onBeforeAppear:re=P,onAppear:Z=I,onAppearCancelled:ve=N}=t,V=(O,Y,pe,Ee)=>{O._enterCancelled=Ee,mt(O,Y?f:a),mt(O,Y?d:i),pe&&pe()},G=(O,Y)=>{O._isLeaving=!1,mt(O,p),mt(O,m),mt(O,g),Y&&Y()},oe=O=>(Y,pe)=>{const Ee=O?Z:I,he=()=>V(Y,O,pe);Mt(Ee,[Y,he]),Cr(()=>{mt(Y,O?u:o),Xe(Y,O?f:a),Sr(Ee)||Er(Y,s,S,he)})};return be(t,{onBeforeEnter(O){Mt(P,[O]),Xe(O,o),Xe(O,i)},onBeforeAppear(O){Mt(re,[O]),Xe(O,u),Xe(O,d)},onEnter:oe(!1),onAppear:oe(!0),onLeave(O,Y){O._isLeaving=!0;const pe=()=>G(O,Y);Xe(O,p),O._enterCancelled?(Xe(O,g),Ms()):(Ms(),Xe(O,g)),Cr(()=>{O._isLeaving&&(mt(O,p),Xe(O,m),Sr(L)||Er(O,s,$,pe))}),Mt(L,[O,pe])},onEnterCancelled(O){V(O,!1,void 0,!0),Mt(N,[O])},onAppearCancelled(O){V(O,!0,void 0,!0),Mt(ve,[O])},onLeaveCancelled(O){G(O),Mt(K,[O])}})}function Ic(e){if(e==null)return null;if(fe(e))return[fs(e.enter),fs(e.leave)];{const t=fs(e);return[t,t]}}function fs(e){return Ui(e)}function Xe(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[qt]||(e[qt]=new Set)).add(t)}function mt(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.remove(s));const n=e[qt];n&&(n.delete(t),n.size||(e[qt]=void 0))}function Cr(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let kc=0;function Er(e,t,n,s){const r=e._endId=++kc,o=()=>{r===e._endId&&s()};if(n!=null)return setTimeout(o,n);const{type:i,timeout:a,propCount:u}=Ci(e,t);if(!i)return s();const d=i+"end";let f=0;const p=()=>{e.removeEventListener(d,g),o()},g=m=>{m.target===e&&++f>=u&&p()};setTimeout(()=>{f<u&&p()},a+1),e.addEventListener(d,g)}function Ci(e,t){const n=window.getComputedStyle(e),s=x=>(n[x]||"").split(", "),r=s(`${pt}Delay`),o=s(`${pt}Duration`),i=xr(r,o),a=s(`${Zt}Delay`),u=s(`${Zt}Duration`),d=xr(a,u);let f=null,p=0,g=0;t===pt?i>0&&(f=pt,p=i,g=o.length):t===Zt?d>0&&(f=Zt,p=d,g=u.length):(p=Math.max(i,d),f=p>0?i>d?pt:Zt:null,g=f?f===pt?o.length:u.length:0);const m=f===pt&&/\b(transform|all)(,|$)/.test(s(`${pt}Property`).toString());return{type:f,timeout:p,propCount:g,hasTransform:m}}function xr(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,s)=>Tr(n)+Tr(e[s])))}function Tr(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function Ms(){return document.body.offsetHeight}function Lc(e,t,n){const s=e[qt];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Rr=Symbol("_vod"),Nc=Symbol("_vsh"),zc=Symbol(""),Hc=/(^|;)\s*display\s*:/;function Dc(e,t,n){const s=e.style,r=ge(n);let o=!1;if(n&&!r){if(t)if(ge(t))for(const i of t.split(";")){const a=i.slice(0,i.indexOf(":")).trim();n[a]==null&&In(s,a,"")}else for(const i in t)n[i]==null&&In(s,i,"");for(const i in n)i==="display"&&(o=!0),In(s,i,n[i])}else if(r){if(t!==n){const i=s[zc];i&&(n+=";"+i),s.cssText=n,o=Hc.test(n)}}else t&&e.removeAttribute("style");Rr in e&&(e[Rr]=o?s.display:"",e[Nc]&&(s.display="none"))}const Ar=/\s*!important$/;function In(e,t,n){if(F(n))n.forEach(s=>In(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=Fc(e,t);Ar.test(n)?e.setProperty(Rt(s),n.replace(Ar,""),"important"):e[s]=n}}const Pr=["Webkit","Moz","ms"],ds={};function Fc(e,t){const n=ds[t];if(n)return n;let s=Ct(t);if(s!=="filter"&&s in e)return ds[t]=s;s=ao(s);for(let r=0;r<Pr.length;r++){const o=Pr[r]+s;if(o in e)return ds[t]=o}return t}const Mr="http://www.w3.org/1999/xlink";function Or(e,t,n,s,r,o=Ji(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Mr,t.slice(6,t.length)):e.setAttributeNS(Mr,t,n):n==null||o&&!uo(n)?e.removeAttribute(t):e.setAttribute(t,o?"":ut(n)?String(n):n)}function $r(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?bi(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const a=o==="OPTION"?e.getAttribute("value")||"":e.value,u=n==null?e.type==="checkbox"?"on":"":String(n);(a!==u||!("_value"in e))&&(e.value=u),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const a=typeof e[t];a==="boolean"?n=uo(n):n==null&&a==="string"?(n="",i=!0):a==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(r||t)}function Dt(e,t,n,s){e.addEventListener(t,n,s)}function Vc(e,t,n,s){e.removeEventListener(t,n,s)}const Ir=Symbol("_vei");function jc(e,t,n,s,r=null){const o=e[Ir]||(e[Ir]={}),i=o[t];if(s&&i)i.value=s;else{const[a,u]=Bc(t);if(s){const d=o[t]=Wc(s,r);Dt(e,a,d,u)}else i&&(Vc(e,a,i,u),o[t]=void 0)}}const kr=/(?:Once|Passive|Capture)$/;function Bc(e){let t;if(kr.test(e)){t={};let s;for(;s=e.match(kr);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Rt(e.slice(2)),t]}let hs=0;const Uc=Promise.resolve(),Kc=()=>hs||(Uc.then(()=>hs=0),hs=Date.now());function Wc(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;We(Gc(s,n.value),t,5,[s])};return n.value=e,n.attached=Kc(),n}function Gc(e,t){if(F(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const Lr=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,qc=(e,t,n,s,r,o)=>{const i=r==="svg";t==="class"?Lc(e,s,i):t==="style"?Dc(e,n,s):Vn(t)?Ls(t)||jc(e,t,n,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Yc(e,t,s,i))?($r(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Or(e,t,s,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ge(s))?$r(e,Ct(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),Or(e,t,s,i))};function Yc(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&Lr(t)&&B(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return Lr(t)&&ge(n)?!1:t in e}const Ei=new WeakMap,xi=new WeakMap,Fn=Symbol("_moveCb"),Nr=Symbol("_enterCb"),Jc=e=>(delete e.props.mode,e),Qc=Jc({name:"TransitionGroup",props:be({},wi,{tag:String,moveClass:String}),setup(e,{slots:t}){const n=mi(),s=Vo();let r,o;return qo(()=>{if(!r.length)return;const i=e.moveClass||`${e.name||"v"}-move`;if(!na(r[0].el,n.vnode.el,i)){r=[];return}r.forEach(Zc),r.forEach(ea);const a=r.filter(ta);Ms(),a.forEach(u=>{const d=u.el,f=d.style;Xe(d,i),f.transform=f.webkitTransform=f.transitionDuration="";const p=d[Fn]=g=>{g&&g.target!==d||(!g||/transform$/.test(g.propertyName))&&(d.removeEventListener("transitionend",p),d[Fn]=null,mt(d,i))};d.addEventListener("transitionend",p)}),r=[]}),()=>{const i=Q(e),a=Si(i);let u=i.tag||me;if(r=[],o)for(let d=0;d<o.length;d++){const f=o[d];f.el&&f.el instanceof Element&&(r.push(f),kt(f,mn(f,a,s,n)),Ei.set(f,f.el.getBoundingClientRect()))}o=t.default?Gs(t.default()):[];for(let d=0;d<o.length;d++){const f=o[d];f.key!=null&&kt(f,mn(f,a,s,n))}return le(u,null,o)}}}),Xc=Qc;function Zc(e){const t=e.el;t[Fn]&&t[Fn](),t[Nr]&&t[Nr]()}function ea(e){xi.set(e,e.el.getBoundingClientRect())}function ta(e){const t=Ei.get(e),n=xi.get(e),s=t.left-n.left,r=t.top-n.top;if(s||r){const o=e.el.style;return o.transform=o.webkitTransform=`translate(${s}px,${r}px)`,o.transitionDuration="0s",e}}function na(e,t,n){const s=e.cloneNode(),r=e[qt];r&&r.forEach(a=>{a.split(/\s+/).forEach(u=>u&&s.classList.remove(u))}),n.split(/\s+/).forEach(a=>a&&s.classList.add(a)),s.style.display="none";const o=t.nodeType===1?t:t.parentNode;o.appendChild(s);const{hasTransform:i}=Ci(s);return o.removeChild(s),i}const zr=e=>{const t=e.props["onUpdate:modelValue"]||!1;return F(t)?n=>Pn(t,n):t};function sa(e){e.target.composing=!0}function Hr(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const ps=Symbol("_assign"),ra={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e[ps]=zr(r);const o=s||r.props&&r.props.type==="number";Dt(e,t?"change":"input",i=>{if(i.target.composing)return;let a=e.value;n&&(a=a.trim()),o&&(a=bs(a)),e[ps](a)}),n&&Dt(e,"change",()=>{e.value=e.value.trim()}),t||(Dt(e,"compositionstart",sa),Dt(e,"compositionend",Hr),Dt(e,"change",Hr))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:r,number:o}},i){if(e[ps]=zr(i),e.composing)return;const a=(o||e.type==="number")&&!/^0\d/.test(e.value)?bs(e.value):e.value,u=t??"";a!==u&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||r&&e.value.trim()===u)||(e.value=u))}},oa={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},ia=(e,t)=>{const n=e._withKeys||(e._withKeys={}),s=t.join(".");return n[s]||(n[s]=r=>{if(!("key"in r))return;const o=Rt(r.key);if(t.some(i=>i===o||oa[i]===o))return e(r)})},la=be({patchProp:qc},Mc);let Dr;function ca(){return Dr||(Dr=tc(la))}const aa=(...e)=>{const t=ca().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=fa(s);if(!r)return;const o=t._component;!B(o)&&!o.render&&!o.template&&(o.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=n(r,!1,ua(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function ua(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function fa(e){return ge(e)?document.querySelector(e):e}/*!
 * pinia v3.0.3
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const da=Symbol();var Fr;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(Fr||(Fr={}));function ha(){const e=Qi(!0),t=e.run(()=>de({}));let n=[],s=[];const r=Oo({install(o){r._a=o,o.provide(da,r),o.config.globalProperties.$pinia=r,s.forEach(i=>n.push(i)),s=[]},use(o){return this._a?n.push(o):s.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}const pa={class:"game-container"},ga={class:"score-container"},ma={class:"score-box"},va={class:"score-box"},ya={class:"score-value"},ba={class:"grid-container"},_a=["data-id"],wa={class:"game-controls"},Sa={key:0,class:"test-controls"},Ca={class:"test-buttons"},Ea={key:0,class:"restore-notification"},xa={key:0,class:"game-over-overlay"},Ta={class:"game-over-message"},Ra={key:0,class:"game-over-actions"},Aa={key:1,class:"score-submission"},Pa={class:"submission-actions"},Ma=["disabled"],Oa={key:0,class:"submission-error"},$a={key:1,class:"submission-success"},Ia=Lt({__name:"GameBoard",setup(e){const t=de([]),n=de([]),s=de(0),r=de(0),o=de(!1),i=de(),a=de();let u=1,d=0,f=0;const p=de(!1),g=de(""),m=de(!1),x=de(""),S=de(!1),$=de("分数提交成功！"),P=de(!1),I=de(0),N=de(!1),L=c=>c<=1e3?Math.min(c/1e3*.2,.2):c<=5e3?.2+(c-1e3)/4e3*.3:c<=15e3?.5+(c-5e3)/1e4*.3:Math.min(.8+(c-15e3)/2e4*.2,1),K=c=>{const l=240-c*180,h=60+c*40,v=50+c*10;return`hsl(${l}, ${h}%, ${v}%)`},re=c=>c<.3?`linear-gradient(135deg,
      hsl(240, 60%, 70%) 0%,
      hsl(260, 50%, 75%) 100%)`:c<.6?`linear-gradient(135deg,
      hsl(280, 70%, 65%) 0%,
      hsl(320, 60%, 70%) 100%)`:c<.8?`linear-gradient(135deg,
      hsl(20, 80%, 60%) 0%,
      hsl(40, 75%, 65%) 100%)`:`linear-gradient(135deg,
      hsl(0, 90%, 55%) 0%,
      hsl(15, 85%, 60%) 50%,
      hsl(30, 80%, 65%) 100%)`,Z="http://localhost:3001/api",ve=(c=!1)=>{if(_(),!c&&ht()){P.value=!0,setTimeout(()=>{P.value=!1},3e3),A();return}t.value=Array(4).fill(null).map(()=>Array(4).fill(0)),n.value=[],s.value=0,o.value=!1,u=1,V(),V(),A(),Fe()},V=()=>{const c=[];for(let l=0;l<4;l++)for(let h=0;h<4;h++)t.value[l][h]===0&&c.push({row:l,col:h});if(c.length>0){const l=c[Math.floor(Math.random()*c.length)],h=Math.random()<.9?2:4;t.value[l.row][l.col]=h,n.value.push({id:u++,value:h,row:l.row,col:l.col})}},G=()=>{let c=!1;const l=t.value.map(h=>[...h]);for(let h=0;h<4;h++){const v=l[h].filter(E=>E!==0),y=[];let b=0;for(;b<v.length;)b<v.length-1&&v[b]===v[b+1]?(y.push(v[b]*2),s.value+=v[b]*2,b+=2):(y.push(v[b]),b++);for(;y.length<4;)y.push(0);for(let E=0;E<4;E++)l[h][E]!==y[E]&&(c=!0);l[h]=y}return c?(t.value=l,Y(),!0):!1},oe=c=>{const l=c.length,h=Array(l).fill(null).map(()=>Array(l).fill(0));for(let v=0;v<l;v++)for(let y=0;y<l;y++)h[y][l-1-v]=c[v][y];return h},O=c=>{let l=!1,h=0;switch(c){case"left":h=0;break;case"up":h=1;break;case"right":h=2;break;case"down":h=3;break}let v=t.value;for(let y=0;y<h;y++)v=oe(v);t.value=v,l=G();for(let y=0;y<(4-h)%4;y++)t.value=oe(t.value);return l},Y=()=>{const c=[];for(let l=0;l<4;l++)for(let h=0;h<4;h++)t.value[l][h]!==0&&c.push({id:u++,value:t.value[l][h],row:l,col:h});n.value=c},pe=()=>{for(let c=0;c<4;c++)for(let l=0;l<4;l++)if(t.value[c][l]===0)return!1;for(let c=0;c<4;c++)for(let l=0;l<4;l++){const h=t.value[c][l];if(l<3&&t.value[c][l+1]===h||c<3&&t.value[c+1][l]===h)return!1}return!0},Ee=c=>{if(o.value)return;let l=!1;switch(c.key){case"ArrowLeft":c.preventDefault(),l=O("left");break;case"ArrowUp":c.preventDefault(),l=O("up");break;case"ArrowRight":c.preventDefault(),l=O("right");break;case"ArrowDown":c.preventDefault(),l=O("down");break}l?(V(),z(),k(),Fe(),pe()&&(o.value=!0,Fe())):te()},he=c=>{c.touches.length===1&&(d=c.touches[0].clientX,f=c.touches[0].clientY)},ee=c=>{if(o.value||c.changedTouches.length!==1)return;const l=c.changedTouches[0].clientX,h=c.changedTouches[0].clientY,v=l-d,y=h-f,b=30;if(Math.abs(v)<b&&Math.abs(y)<b)return;let E=!1;Math.abs(v)>Math.abs(y)?v>0?E=O("right"):E=O("left"):y>0?E=O("down"):E=O("up"),E?(V(),z(),k(),A(),Fe(),pe()&&(o.value=!0,Fe())):te()},J=(c,l)=>{const h=`${c}-${JSON.stringify(l)}-${Date.now()}`;let v=0;for(let y=0;y<h.length;y++){const b=h.charCodeAt(y);v=(v<<5)-v+b,v=v&v}return Math.abs(v).toString(16)},De=async()=>{if(!g.value.trim()){x.value="请输入昵称";return}m.value=!0,x.value="",S.value=!1;try{const c=J(s.value,t.value),l=await fetch(`${Z}/scores`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({playerName:g.value.trim(),score:s.value,validationPayload:c})}),h=await l.json();if(!l.ok)throw new Error(h.error||"提交失败");S.value=!0,x.value="",h.isNewRecord?($.value="🎉 新纪录！分数更新成功！",setTimeout(()=>{p.value=!1,S.value=!1},4e3)):($.value="分数提交成功！",setTimeout(()=>{p.value=!1,S.value=!1},3e3))}catch(c){let l="网络错误";c instanceof Error&&(l=c.message,l.includes("服务器存储空间不足")&&(l="🚫 服务器存储空间不足，请稍后再试或联系管理员")),x.value=l}finally{m.value=!1}},Be=()=>{Nt(),p.value=!1,g.value="",x.value="",S.value=!1,ve(!0)},we=c=>{s.value=c,A(),z(),console.log(`🎨 设置测试分数: ${c}, 强度: ${I.value.toFixed(2)}`)},xe=()=>{console.log("🎨 开始自动颜色测试...");const c=[0,500,1500,3e3,8e3,12e3,2e4,5e4];let l=0;const h=setInterval(()=>{if(l>=c.length){clearInterval(h),console.log("✅ 动态颜色测试完成");return}we(c[l]),l++},2e3)},Fe=()=>{const c={board:t.value,tiles:n.value,score:s.value,isGameOver:o.value,nextTileId:u};localStorage.setItem("game2048-state",JSON.stringify(c))},ht=()=>{const c=localStorage.getItem("game2048-state");if(c)try{const l=JSON.parse(c);return t.value=l.board,n.value=l.tiles,s.value=l.score,o.value=l.isGameOver,u=l.nextTileId,!0}catch(l){return console.error("Failed to load game state:",l),!1}return!1},Nt=()=>{localStorage.removeItem("game2048-state")},$e=()=>{localStorage.setItem("game2048-best-score",r.value.toString())},_=()=>{const c=localStorage.getItem("game2048-best-score");c&&(r.value=parseInt(c,10))},k=()=>{s.value>r.value&&(r.value=s.value,$e())},A=()=>{I.value=L(s.value);const c=document.querySelector(".score-container"),l=document.querySelector(".game-container");if(c&&l){const h=I.value,v=K(h),y=re(h),b=c.querySelectorAll(".score-value"),E=c.querySelectorAll(".score-label");b.forEach(w=>{w.style.color=v});const R=h>.5?"rgba(255, 255, 255, 0.9)":"rgba(102, 126, 234, 0.8)";E.forEach(w=>{w.style.color=R}),c.querySelectorAll(".score-box").forEach(w=>{w.style.background=y,h>.8?w.classList.add("score-pulse-intense"):h>.6?w.classList.add("score-pulse-medium"):w.classList.remove("score-pulse-intense","score-pulse-medium")}),h>.7?l.classList.add("game-intense"):h>.4?l.classList.add("game-heated"):l.classList.remove("game-intense","game-heated")}},z=()=>{if(a.value){const c=I.value;c>.8?a.value.classList.add("score-increase-intense"):c>.5?a.value.classList.add("score-increase-heated"):a.value.classList.add("score-increase"),setTimeout(()=>{a.value?.classList.remove("score-increase","score-increase-heated","score-increase-intense")},500)}},te=()=>{i.value&&(i.value.classList.add("shake"),setTimeout(()=>{i.value?.classList.remove("shake")},500))};return Jn(()=>{ve(),window.addEventListener("keydown",Ee),typeof window<"u"&&(window.testDynamicColors=()=>{console.log("🎨 测试动态颜色系统...");const c=[0,500,1500,3e3,8e3,12e3,2e4,5e4];let l=0;const h=setInterval(()=>{if(l>=c.length){clearInterval(h),console.log("✅ 动态颜色测试完成");return}s.value=c[l],A(),z(),console.log(`分数: ${s.value}, 强度: ${I.value.toFixed(2)}`),l++},2e3)})}),qs(()=>{window.removeEventListener("keydown",Ee)}),(c,l)=>(U(),q("div",pa,[C("div",ga,[C("div",ma,[l[8]||(l[8]=C("div",{class:"score-label"},"分数",-1)),C("div",{class:"score-value",ref_key:"scoreValue",ref:a},ye(s.value),513)]),C("div",va,[l[9]||(l[9]=C("div",{class:"score-label"},"最高分",-1)),C("div",ya,ye(r.value),1)])]),C("div",{class:"game-board",ref_key:"gameBoard",ref:i,onTouchstart:he,onTouchend:ee},[C("div",ba,[(U(),q(me,null,cn(4,h=>C("div",{class:"grid-row",key:h},[(U(),q(me,null,cn(4,v=>C("div",{class:"grid-cell",key:v})),64))])),64))]),le(Xc,{name:"tile",tag:"div",class:"tile-container"},{default:_e(()=>[(U(!0),q(me,null,cn(n.value,h=>(U(),q("div",{key:h.id,"data-id":h.id,class:Et(["tile",`tile-${h.value}`,`tile-position-${h.row}-${h.col}`])},ye(h.value),11,_a))),128))]),_:1})],544),C("div",wa,[C("button",{onClick:Be,class:"restart-btn"},"新游戏"),l[11]||(l[11]=C("p",{class:"auto-save-hint"},"游戏进度自动保存",-1)),N.value?(U(),q("div",Sa,[l[10]||(l[10]=C("h4",null,"🎨 动态颜色测试",-1)),C("div",Ca,[C("button",{onClick:l[0]||(l[0]=h=>we(500)),class:"test-btn"},"500分"),C("button",{onClick:l[1]||(l[1]=h=>we(2e3)),class:"test-btn"},"2K分"),C("button",{onClick:l[2]||(l[2]=h=>we(8e3)),class:"test-btn"},"8K分"),C("button",{onClick:l[3]||(l[3]=h=>we(15e3)),class:"test-btn"},"15K分"),C("button",{onClick:l[4]||(l[4]=h=>we(3e4)),class:"test-btn"},"30K分"),C("button",{onClick:xe,class:"test-btn-auto"},"自动测试")])])):wt("",!0)]),le($c,{name:"notification"},{default:_e(()=>[P.value?(U(),q("div",Ea,l[12]||(l[12]=[C("div",{class:"notification-content"},[C("span",{class:"notification-icon"},"🎮"),C("span",{class:"notification-text"},"游戏进度已恢复")],-1)]))):wt("",!0)]),_:1}),o.value?(U(),q("div",xa,[C("div",Ta,[l[13]||(l[13]=C("h2",null,"游戏结束!",-1)),C("p",null,"最终分数: "+ye(s.value),1),p.value?(U(),q("div",Aa,[Rl(C("input",{"onUpdate:modelValue":l[6]||(l[6]=h=>g.value=h),type:"text",placeholder:"输入你的昵称",maxlength:"20",class:"player-name-input",onKeyup:ia(De,["enter"])},null,544),[[ra,g.value]]),C("div",Pa,[C("button",{onClick:De,disabled:m.value||!g.value.trim(),class:"submit-btn"},ye(m.value?"提交中...":"提交"),9,Ma),C("button",{onClick:l[7]||(l[7]=h=>p.value=!1),class:"cancel-btn"},"取消")]),x.value?(U(),q("div",Oa,ye(x.value),1)):wt("",!0),S.value?(U(),q("div",$a,ye($.value),1)):wt("",!0)])):(U(),q("div",Ra,[C("button",{onClick:l[5]||(l[5]=h=>p.value=!0),class:"submit-score-btn"},"提交分数"),C("button",{onClick:Be,class:"restart-btn"},"再来一局")]))])])):wt("",!0)]))}}),dt=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n},ka=dt(Ia,[["__scopeId","data-v-86b608d8"]]),La={class:"leaderboard-container"},Na={class:"period-selector"},za=["onClick"],Ha={key:0,class:"loading"},Da={key:1,class:"error"},Fa={key:2,class:"leaderboard-list"},Va={key:0,class:"empty-state"},ja={key:1},Ba={class:"rank"},Ua={key:0,class:"medal"},Ka={key:1,class:"rank-number"},Wa={class:"player-info"},Ga={class:"player-name"},qa={class:"play-time"},Ya={class:"score"},Ja={key:3,class:"stats-section"},Qa={class:"stats-grid"},Xa={class:"stat-item"},Za={class:"stat-value"},eu={class:"stat-item"},tu={class:"stat-value"},nu={class:"stat-item"},su={class:"stat-value"},ru={class:"stat-item"},ou={class:"stat-value"},iu=Lt({__name:"Leaderboard",setup(e){const t="http://localhost:3001/api",n=de([]),s=de(null),r=de(!1),o=de(""),i=de("alltime"),a=[{value:"alltime",label:"总榜"},{value:"weekly",label:"周榜"},{value:"daily",label:"日榜"}],u=async()=>{r.value=!0,o.value="";try{const S=await fetch(`${t}/leaderboard?period=${i.value}&limit=10`);if(!S.ok)throw new Error("获取排行榜失败");const $=await S.json();n.value=$.leaderboard||[]}catch(S){o.value=S instanceof Error?S.message:"网络错误",console.error("Failed to fetch leaderboard:",S)}finally{r.value=!1}},d=async()=>{try{const S=await fetch(`${t}/stats`);if(!S.ok)throw new Error("获取统计信息失败");const $=await S.json();s.value=$}catch(S){console.error("Failed to fetch stats:",S)}},f=S=>{i.value=S,u()},p=S=>S===1?"first-place":S===2?"second-place":S===3?"third-place":"",g=S=>["🥇","🥈","🥉"][S-1]||"",m=S=>S.toLocaleString(),x=S=>{const $=new Date(S),I=new Date().getTime()-$.getTime(),N=Math.floor(I/(1e3*60*60*24));return N===0?"今天":N===1?"昨天":N<7?`${N}天前`:$.toLocaleDateString("zh-CN")};return Jn(()=>{u(),d()}),(S,$)=>(U(),q("div",La,[$[5]||($[5]=C("h2",null,"排行榜",-1)),C("div",Na,[(U(),q(me,null,cn(a,P=>C("button",{key:P.value,class:Et(["period-btn",{active:i.value===P.value}]),onClick:I=>f(P.value)},ye(P.label),11,za)),64))]),r.value?(U(),q("div",Ha," 加载中... ")):o.value?(U(),q("div",Da,[X(ye(o.value)+" ",1),C("button",{onClick:u,class:"retry-btn"},"重试")])):(U(),q("div",Fa,[n.value.length===0?(U(),q("div",Va," 暂无排行榜数据 ")):(U(),q("div",ja,[(U(!0),q(me,null,cn(n.value,P=>(U(),q("div",{key:P.rank,class:Et(["leaderboard-entry",p(P.rank)])},[C("div",Ba,[P.rank<=3?(U(),q("span",Ua,ye(g(P.rank)),1)):(U(),q("span",Ka,ye(P.rank),1))]),C("div",Wa,[C("div",Ga,ye(P.playerName),1),C("div",qa,ye(x(P.createdAt)),1)]),C("div",Ya,ye(m(P.score)),1)],2))),128))]))])),s.value?(U(),q("div",Ja,[$[4]||($[4]=C("h3",null,"游戏统计",-1)),C("div",Qa,[C("div",Xa,[C("div",Za,ye(s.value.totalGames),1),$[0]||($[0]=C("div",{class:"stat-label"},"总游戏数",-1))]),C("div",eu,[C("div",tu,ye(m(s.value.highestScore)),1),$[1]||($[1]=C("div",{class:"stat-label"},"最高分",-1))]),C("div",nu,[C("div",su,ye(m(s.value.averageScore)),1),$[2]||($[2]=C("div",{class:"stat-label"},"平均分",-1))]),C("div",ru,[C("div",ou,ye(s.value.uniquePlayers),1),$[3]||($[3]=C("div",{class:"stat-label"},"玩家数",-1))])])])):wt("",!0)]))}}),lu=dt(iu,[["__scopeId","data-v-636ba4c8"]]),cu={id:"app"},au={class:"main-nav"},uu=Lt({__name:"App",setup(e){const t=de("game"),n=()=>{t.value="game"},s=()=>{t.value="leaderboard"};return(r,o)=>(U(),q("div",cu,[C("header",null,[o[0]||(o[0]=C("h1",null,"2048 游戏",-1)),o[1]||(o[1]=C("p",null,"使用方向键移动方块，合并相同数字达到2048！",-1)),C("nav",au,[C("button",{class:Et(["nav-btn",{active:t.value==="game"}]),onClick:n}," 游戏 ",2),C("button",{class:Et(["nav-btn",{active:t.value==="leaderboard"}]),onClick:s}," 排行榜 ",2)])]),C("main",null,[t.value==="game"?(U(),yn(ka,{key:0})):wt("",!0),t.value==="leaderboard"?(U(),yn(lu,{key:1})):wt("",!0)])]))}}),fu=dt(uu,[["__scopeId","data-v-eb64361f"]]),du="modulepreload",hu=function(e){return"/"+e},Vr={},pu=function(t,n,s){let r=Promise.resolve();if(n&&n.length>0){let u=function(d){return Promise.all(d.map(f=>Promise.resolve(f).then(p=>({status:"fulfilled",value:p}),p=>({status:"rejected",reason:p}))))};document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),a=i?.nonce||i?.getAttribute("nonce");r=u(n.map(d=>{if(d=hu(d),d in Vr)return;Vr[d]=!0;const f=d.endsWith(".css"),p=f?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${d}"]${p}`))return;const g=document.createElement("link");if(g.rel=f?"stylesheet":du,f||(g.as="script"),g.crossOrigin="",g.href=d,a&&g.setAttribute("nonce",a),document.head.appendChild(g),f)return new Promise((m,x)=>{g.addEventListener("load",m),g.addEventListener("error",()=>x(new Error(`Unable to preload CSS for ${d}`)))})}))}function o(i){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=i,window.dispatchEvent(a),!a.defaultPrevented)throw i}return r.then(i=>{for(const a of i||[])a.status==="rejected"&&o(a.reason);return t().catch(o)})};/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Ft=typeof document<"u";function Ti(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function gu(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Ti(e.default)}const ne=Object.assign;function gs(e,t){const n={};for(const s in t){const r=t[s];n[s]=Ge(r)?r.map(e):e(r)}return n}const fn=()=>{},Ge=Array.isArray,Ri=/#/g,mu=/&/g,vu=/\//g,yu=/=/g,bu=/\?/g,Ai=/\+/g,_u=/%5B/g,wu=/%5D/g,Pi=/%5E/g,Su=/%60/g,Mi=/%7B/g,Cu=/%7C/g,Oi=/%7D/g,Eu=/%20/g;function Zs(e){return encodeURI(""+e).replace(Cu,"|").replace(_u,"[").replace(wu,"]")}function xu(e){return Zs(e).replace(Mi,"{").replace(Oi,"}").replace(Pi,"^")}function Os(e){return Zs(e).replace(Ai,"%2B").replace(Eu,"+").replace(Ri,"%23").replace(mu,"%26").replace(Su,"`").replace(Mi,"{").replace(Oi,"}").replace(Pi,"^")}function Tu(e){return Os(e).replace(yu,"%3D")}function Ru(e){return Zs(e).replace(Ri,"%23").replace(bu,"%3F")}function Au(e){return e==null?"":Ru(e).replace(vu,"%2F")}function wn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Pu=/\/$/,Mu=e=>e.replace(Pu,"");function ms(e,t,n="/"){let s,r={},o="",i="";const a=t.indexOf("#");let u=t.indexOf("?");return a<u&&a>=0&&(u=-1),u>-1&&(s=t.slice(0,u),o=t.slice(u+1,a>-1?a:t.length),r=e(o)),a>-1&&(s=s||t.slice(0,a),i=t.slice(a,t.length)),s=ku(s??t,n),{fullPath:s+(o&&"?")+o+i,path:s,query:r,hash:wn(i)}}function Ou(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function jr(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function $u(e,t,n){const s=t.matched.length-1,r=n.matched.length-1;return s>-1&&s===r&&Yt(t.matched[s],n.matched[r])&&$i(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Yt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function $i(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Iu(e[n],t[n]))return!1;return!0}function Iu(e,t){return Ge(e)?Br(e,t):Ge(t)?Br(t,e):e===t}function Br(e,t){return Ge(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function ku(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),r=s[s.length-1];(r===".."||r===".")&&s.push("");let o=n.length-1,i,a;for(i=0;i<s.length;i++)if(a=s[i],a!==".")if(a==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+s.slice(i).join("/")}const gt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Sn;(function(e){e.pop="pop",e.push="push"})(Sn||(Sn={}));var dn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(dn||(dn={}));function Lu(e){if(!e)if(Ft){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Mu(e)}const Nu=/^[^#]+#/;function zu(e,t){return e.replace(Nu,"#")+t}function Hu(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const es=()=>({left:window.scrollX,top:window.scrollY});function Du(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=Hu(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Ur(e,t){return(history.state?history.state.position-t:-1)+e}const $s=new Map;function Fu(e,t){$s.set(e,t)}function Vu(e){const t=$s.get(e);return $s.delete(e),t}let ju=()=>location.protocol+"//"+location.host;function Ii(e,t){const{pathname:n,search:s,hash:r}=t,o=e.indexOf("#");if(o>-1){let a=r.includes(e.slice(o))?e.slice(o).length:1,u=r.slice(a);return u[0]!=="/"&&(u="/"+u),jr(u,"")}return jr(n,e)+s+r}function Bu(e,t,n,s){let r=[],o=[],i=null;const a=({state:g})=>{const m=Ii(e,location),x=n.value,S=t.value;let $=0;if(g){if(n.value=m,t.value=g,i&&i===x){i=null;return}$=S?g.position-S.position:0}else s(m);r.forEach(P=>{P(n.value,x,{delta:$,type:Sn.pop,direction:$?$>0?dn.forward:dn.back:dn.unknown})})};function u(){i=n.value}function d(g){r.push(g);const m=()=>{const x=r.indexOf(g);x>-1&&r.splice(x,1)};return o.push(m),m}function f(){const{history:g}=window;g.state&&g.replaceState(ne({},g.state,{scroll:es()}),"")}function p(){for(const g of o)g();o=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",f)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",f,{passive:!0}),{pauseListeners:u,listen:d,destroy:p}}function Kr(e,t,n,s=!1,r=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:r?es():null}}function Uu(e){const{history:t,location:n}=window,s={value:Ii(e,n)},r={value:t.state};r.value||o(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(u,d,f){const p=e.indexOf("#"),g=p>-1?(n.host&&document.querySelector("base")?e:e.slice(p))+u:ju()+e+u;try{t[f?"replaceState":"pushState"](d,"",g),r.value=d}catch(m){console.error(m),n[f?"replace":"assign"](g)}}function i(u,d){const f=ne({},t.state,Kr(r.value.back,u,r.value.forward,!0),d,{position:r.value.position});o(u,f,!0),s.value=u}function a(u,d){const f=ne({},r.value,t.state,{forward:u,scroll:es()});o(f.current,f,!0);const p=ne({},Kr(s.value,u,null),{position:f.position+1},d);o(u,p,!1),s.value=u}return{location:s,state:r,push:a,replace:i}}function Ku(e){e=Lu(e);const t=Uu(e),n=Bu(e,t.state,t.location,t.replace);function s(o,i=!0){i||n.pauseListeners(),history.go(o)}const r=ne({location:"",base:e,go:s,createHref:zu.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function Wu(e){return typeof e=="string"||e&&typeof e=="object"}function ki(e){return typeof e=="string"||typeof e=="symbol"}const Li=Symbol("");var Wr;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Wr||(Wr={}));function Jt(e,t){return ne(new Error,{type:e,[Li]:!0},t)}function st(e,t){return e instanceof Error&&Li in e&&(t==null||!!(e.type&t))}const Gr="[^/]+?",Gu={sensitive:!1,strict:!1,start:!0,end:!0},qu=/[.+*?^${}()[\]/\\]/g;function Yu(e,t){const n=ne({},Gu,t),s=[];let r=n.start?"^":"";const o=[];for(const d of e){const f=d.length?[]:[90];n.strict&&!d.length&&(r+="/");for(let p=0;p<d.length;p++){const g=d[p];let m=40+(n.sensitive?.25:0);if(g.type===0)p||(r+="/"),r+=g.value.replace(qu,"\\$&"),m+=40;else if(g.type===1){const{value:x,repeatable:S,optional:$,regexp:P}=g;o.push({name:x,repeatable:S,optional:$});const I=P||Gr;if(I!==Gr){m+=10;try{new RegExp(`(${I})`)}catch(L){throw new Error(`Invalid custom RegExp for param "${x}" (${I}): `+L.message)}}let N=S?`((?:${I})(?:/(?:${I}))*)`:`(${I})`;p||(N=$&&d.length<2?`(?:/${N})`:"/"+N),$&&(N+="?"),r+=N,m+=20,$&&(m+=-8),S&&(m+=-20),I===".*"&&(m+=-50)}f.push(m)}s.push(f)}if(n.strict&&n.end){const d=s.length-1;s[d][s[d].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const i=new RegExp(r,n.sensitive?"":"i");function a(d){const f=d.match(i),p={};if(!f)return null;for(let g=1;g<f.length;g++){const m=f[g]||"",x=o[g-1];p[x.name]=m&&x.repeatable?m.split("/"):m}return p}function u(d){let f="",p=!1;for(const g of e){(!p||!f.endsWith("/"))&&(f+="/"),p=!1;for(const m of g)if(m.type===0)f+=m.value;else if(m.type===1){const{value:x,repeatable:S,optional:$}=m,P=x in d?d[x]:"";if(Ge(P)&&!S)throw new Error(`Provided param "${x}" is an array but it is not repeatable (* or + modifiers)`);const I=Ge(P)?P.join("/"):P;if(!I)if($)g.length<2&&(f.endsWith("/")?f=f.slice(0,-1):p=!0);else throw new Error(`Missing required param "${x}"`);f+=I}}return f||"/"}return{re:i,score:s,keys:o,parse:a,stringify:u}}function Ju(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Ni(e,t){let n=0;const s=e.score,r=t.score;for(;n<s.length&&n<r.length;){const o=Ju(s[n],r[n]);if(o)return o;n++}if(Math.abs(r.length-s.length)===1){if(qr(s))return 1;if(qr(r))return-1}return r.length-s.length}function qr(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Qu={type:0,value:""},Xu=/[a-zA-Z0-9_]/;function Zu(e){if(!e)return[[]];if(e==="/")return[[Qu]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${d}": ${m}`)}let n=0,s=n;const r=[];let o;function i(){o&&r.push(o),o=[]}let a=0,u,d="",f="";function p(){d&&(n===0?o.push({type:0,value:d}):n===1||n===2||n===3?(o.length>1&&(u==="*"||u==="+")&&t(`A repeatable param (${d}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:d,regexp:f,repeatable:u==="*"||u==="+",optional:u==="*"||u==="?"})):t("Invalid state to consume buffer"),d="")}function g(){d+=u}for(;a<e.length;){if(u=e[a++],u==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:u==="/"?(d&&p(),i()):u===":"?(p(),n=1):g();break;case 4:g(),n=s;break;case 1:u==="("?n=2:Xu.test(u)?g():(p(),n=0,u!=="*"&&u!=="?"&&u!=="+"&&a--);break;case 2:u===")"?f[f.length-1]=="\\"?f=f.slice(0,-1)+u:n=3:f+=u;break;case 3:p(),n=0,u!=="*"&&u!=="?"&&u!=="+"&&a--,f="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${d}"`),p(),i(),r}function ef(e,t,n){const s=Yu(Zu(e.path),n),r=ne(s,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function tf(e,t){const n=[],s=new Map;t=Xr({strict:!1,end:!0,sensitive:!1},t);function r(p){return s.get(p)}function o(p,g,m){const x=!m,S=Jr(p);S.aliasOf=m&&m.record;const $=Xr(t,p),P=[S];if("alias"in p){const L=typeof p.alias=="string"?[p.alias]:p.alias;for(const K of L)P.push(Jr(ne({},S,{components:m?m.record.components:S.components,path:K,aliasOf:m?m.record:S})))}let I,N;for(const L of P){const{path:K}=L;if(g&&K[0]!=="/"){const re=g.record.path,Z=re[re.length-1]==="/"?"":"/";L.path=g.record.path+(K&&Z+K)}if(I=ef(L,g,$),m?m.alias.push(I):(N=N||I,N!==I&&N.alias.push(I),x&&p.name&&!Qr(I)&&i(p.name)),zi(I)&&u(I),S.children){const re=S.children;for(let Z=0;Z<re.length;Z++)o(re[Z],I,m&&m.children[Z])}m=m||I}return N?()=>{i(N)}:fn}function i(p){if(ki(p)){const g=s.get(p);g&&(s.delete(p),n.splice(n.indexOf(g),1),g.children.forEach(i),g.alias.forEach(i))}else{const g=n.indexOf(p);g>-1&&(n.splice(g,1),p.record.name&&s.delete(p.record.name),p.children.forEach(i),p.alias.forEach(i))}}function a(){return n}function u(p){const g=rf(p,n);n.splice(g,0,p),p.record.name&&!Qr(p)&&s.set(p.record.name,p)}function d(p,g){let m,x={},S,$;if("name"in p&&p.name){if(m=s.get(p.name),!m)throw Jt(1,{location:p});$=m.record.name,x=ne(Yr(g.params,m.keys.filter(N=>!N.optional).concat(m.parent?m.parent.keys.filter(N=>N.optional):[]).map(N=>N.name)),p.params&&Yr(p.params,m.keys.map(N=>N.name))),S=m.stringify(x)}else if(p.path!=null)S=p.path,m=n.find(N=>N.re.test(S)),m&&(x=m.parse(S),$=m.record.name);else{if(m=g.name?s.get(g.name):n.find(N=>N.re.test(g.path)),!m)throw Jt(1,{location:p,currentLocation:g});$=m.record.name,x=ne({},g.params,p.params),S=m.stringify(x)}const P=[];let I=m;for(;I;)P.unshift(I.record),I=I.parent;return{name:$,path:S,params:x,matched:P,meta:sf(P)}}e.forEach(p=>o(p));function f(){n.length=0,s.clear()}return{addRoute:o,resolve:d,removeRoute:i,clearRoutes:f,getRoutes:a,getRecordMatcher:r}}function Yr(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function Jr(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:nf(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function nf(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function Qr(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function sf(e){return e.reduce((t,n)=>ne(t,n.meta),{})}function Xr(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function rf(e,t){let n=0,s=t.length;for(;n!==s;){const o=n+s>>1;Ni(e,t[o])<0?s=o:n=o+1}const r=of(e);return r&&(s=t.lastIndexOf(r,s-1)),s}function of(e){let t=e;for(;t=t.parent;)if(zi(t)&&Ni(e,t)===0)return t}function zi({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function lf(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<s.length;++r){const o=s[r].replace(Ai," "),i=o.indexOf("="),a=wn(i<0?o:o.slice(0,i)),u=i<0?null:wn(o.slice(i+1));if(a in t){let d=t[a];Ge(d)||(d=t[a]=[d]),d.push(u)}else t[a]=u}return t}function Zr(e){let t="";for(let n in e){const s=e[n];if(n=Tu(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(Ge(s)?s.map(o=>o&&Os(o)):[s&&Os(s)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function cf(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=Ge(s)?s.map(r=>r==null?null:""+r):s==null?s:""+s)}return t}const af=Symbol(""),eo=Symbol(""),er=Symbol(""),Hi=Symbol(""),Is=Symbol("");function en(){let e=[];function t(s){return e.push(s),()=>{const r=e.indexOf(s);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function _t(e,t,n,s,r,o=i=>i()){const i=s&&(s.enterCallbacks[r]=s.enterCallbacks[r]||[]);return()=>new Promise((a,u)=>{const d=g=>{g===!1?u(Jt(4,{from:n,to:t})):g instanceof Error?u(g):Wu(g)?u(Jt(2,{from:t,to:g})):(i&&s.enterCallbacks[r]===i&&typeof g=="function"&&i.push(g),a())},f=o(()=>e.call(s&&s.instances[r],t,n,d));let p=Promise.resolve(f);e.length<3&&(p=p.then(d)),p.catch(g=>u(g))})}function vs(e,t,n,s,r=o=>o()){const o=[];for(const i of e)for(const a in i.components){let u=i.components[a];if(!(t!=="beforeRouteEnter"&&!i.instances[a]))if(Ti(u)){const f=(u.__vccOpts||u)[t];f&&o.push(_t(f,n,s,i,a,r))}else{let d=u();o.push(()=>d.then(f=>{if(!f)throw new Error(`Couldn't resolve component "${a}" at "${i.path}"`);const p=gu(f)?f.default:f;i.mods[a]=f,i.components[a]=p;const m=(p.__vccOpts||p)[t];return m&&_t(m,n,s,i,a,r)()}))}}return o}function to(e){const t=lt(er),n=lt(Hi),s=Ue(()=>{const u=Ut(e.to);return t.resolve(u)}),r=Ue(()=>{const{matched:u}=s.value,{length:d}=u,f=u[d-1],p=n.matched;if(!f||!p.length)return-1;const g=p.findIndex(Yt.bind(null,f));if(g>-1)return g;const m=no(u[d-2]);return d>1&&no(f)===m&&p[p.length-1].path!==m?p.findIndex(Yt.bind(null,u[d-2])):g}),o=Ue(()=>r.value>-1&&pf(n.params,s.value.params)),i=Ue(()=>r.value>-1&&r.value===n.matched.length-1&&$i(n.params,s.value.params));function a(u={}){if(hf(u)){const d=t[Ut(e.replace)?"replace":"push"](Ut(e.to)).catch(fn);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>d),d}return Promise.resolve()}return{route:s,href:Ue(()=>s.value.href),isActive:o,isExactActive:i,navigate:a}}function uf(e){return e.length===1?e[0]:e}const ff=Lt({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:to,setup(e,{slots:t}){const n=Wn(to(e)),{options:s}=lt(er),r=Ue(()=>({[so(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[so(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&uf(t.default(n));return e.custom?o:Xs("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}}),df=ff;function hf(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function pf(e,t){for(const n in t){const s=t[n],r=e[n];if(typeof s=="string"){if(s!==r)return!1}else if(!Ge(r)||r.length!==s.length||s.some((o,i)=>o!==r[i]))return!1}return!0}function no(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const so=(e,t,n)=>e??t??n,gf=Lt({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=lt(Is),r=Ue(()=>e.route||s.value),o=lt(eo,0),i=Ue(()=>{let d=Ut(o);const{matched:f}=r.value;let p;for(;(p=f[d])&&!p.components;)d++;return d}),a=Ue(()=>r.value.matched[i.value]);Mn(eo,Ue(()=>i.value+1)),Mn(af,a),Mn(Is,r);const u=de();return On(()=>[u.value,a.value,e.name],([d,f,p],[g,m,x])=>{f&&(f.instances[p]=d,m&&m!==f&&d&&d===g&&(f.leaveGuards.size||(f.leaveGuards=m.leaveGuards),f.updateGuards.size||(f.updateGuards=m.updateGuards))),d&&f&&(!m||!Yt(f,m)||!g)&&(f.enterCallbacks[p]||[]).forEach(S=>S(d))},{flush:"post"}),()=>{const d=r.value,f=e.name,p=a.value,g=p&&p.components[f];if(!g)return ro(n.default,{Component:g,route:d});const m=p.props[f],x=m?m===!0?d.params:typeof m=="function"?m(d):m:null,$=Xs(g,ne({},x,t,{onVnodeUnmounted:P=>{P.component.isUnmounted&&(p.instances[f]=null)},ref:u}));return ro(n.default,{Component:$,route:d})||$}}});function ro(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const mf=gf;function vf(e){const t=tf(e.routes,e),n=e.parseQuery||lf,s=e.stringifyQuery||Zr,r=e.history,o=en(),i=en(),a=en(),u=vl(gt);let d=gt;Ft&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const f=gs.bind(null,_=>""+_),p=gs.bind(null,Au),g=gs.bind(null,wn);function m(_,k){let A,z;return ki(_)?(A=t.getRecordMatcher(_),z=k):z=_,t.addRoute(z,A)}function x(_){const k=t.getRecordMatcher(_);k&&t.removeRoute(k)}function S(){return t.getRoutes().map(_=>_.record)}function $(_){return!!t.getRecordMatcher(_)}function P(_,k){if(k=ne({},k||u.value),typeof _=="string"){const h=ms(n,_,k.path),v=t.resolve({path:h.path},k),y=r.createHref(h.fullPath);return ne(h,v,{params:g(v.params),hash:wn(h.hash),redirectedFrom:void 0,href:y})}let A;if(_.path!=null)A=ne({},_,{path:ms(n,_.path,k.path).path});else{const h=ne({},_.params);for(const v in h)h[v]==null&&delete h[v];A=ne({},_,{params:p(h)}),k.params=p(k.params)}const z=t.resolve(A,k),te=_.hash||"";z.params=f(g(z.params));const c=Ou(s,ne({},_,{hash:xu(te),path:z.path})),l=r.createHref(c);return ne({fullPath:c,hash:te,query:s===Zr?cf(_.query):_.query||{}},z,{redirectedFrom:void 0,href:l})}function I(_){return typeof _=="string"?ms(n,_,u.value.path):ne({},_)}function N(_,k){if(d!==_)return Jt(8,{from:k,to:_})}function L(_){return Z(_)}function K(_){return L(ne(I(_),{replace:!0}))}function re(_){const k=_.matched[_.matched.length-1];if(k&&k.redirect){const{redirect:A}=k;let z=typeof A=="function"?A(_):A;return typeof z=="string"&&(z=z.includes("?")||z.includes("#")?z=I(z):{path:z},z.params={}),ne({query:_.query,hash:_.hash,params:z.path!=null?{}:_.params},z)}}function Z(_,k){const A=d=P(_),z=u.value,te=_.state,c=_.force,l=_.replace===!0,h=re(A);if(h)return Z(ne(I(h),{state:typeof h=="object"?ne({},te,h.state):te,force:c,replace:l}),k||A);const v=A;v.redirectedFrom=k;let y;return!c&&$u(s,z,A)&&(y=Jt(16,{to:v,from:z}),we(z,z,!0,!1)),(y?Promise.resolve(y):G(v,z)).catch(b=>st(b)?st(b,2)?b:Be(b):J(b,v,z)).then(b=>{if(b){if(st(b,2))return Z(ne({replace:l},I(b.to),{state:typeof b.to=="object"?ne({},te,b.to.state):te,force:c}),k||v)}else b=O(v,z,!0,l,te);return oe(v,z,b),b})}function ve(_,k){const A=N(_,k);return A?Promise.reject(A):Promise.resolve()}function V(_){const k=ht.values().next().value;return k&&typeof k.runWithContext=="function"?k.runWithContext(_):_()}function G(_,k){let A;const[z,te,c]=yf(_,k);A=vs(z.reverse(),"beforeRouteLeave",_,k);for(const h of z)h.leaveGuards.forEach(v=>{A.push(_t(v,_,k))});const l=ve.bind(null,_,k);return A.push(l),$e(A).then(()=>{A=[];for(const h of o.list())A.push(_t(h,_,k));return A.push(l),$e(A)}).then(()=>{A=vs(te,"beforeRouteUpdate",_,k);for(const h of te)h.updateGuards.forEach(v=>{A.push(_t(v,_,k))});return A.push(l),$e(A)}).then(()=>{A=[];for(const h of c)if(h.beforeEnter)if(Ge(h.beforeEnter))for(const v of h.beforeEnter)A.push(_t(v,_,k));else A.push(_t(h.beforeEnter,_,k));return A.push(l),$e(A)}).then(()=>(_.matched.forEach(h=>h.enterCallbacks={}),A=vs(c,"beforeRouteEnter",_,k,V),A.push(l),$e(A))).then(()=>{A=[];for(const h of i.list())A.push(_t(h,_,k));return A.push(l),$e(A)}).catch(h=>st(h,8)?h:Promise.reject(h))}function oe(_,k,A){a.list().forEach(z=>V(()=>z(_,k,A)))}function O(_,k,A,z,te){const c=N(_,k);if(c)return c;const l=k===gt,h=Ft?history.state:{};A&&(z||l?r.replace(_.fullPath,ne({scroll:l&&h&&h.scroll},te)):r.push(_.fullPath,te)),u.value=_,we(_,k,A,l),Be()}let Y;function pe(){Y||(Y=r.listen((_,k,A)=>{if(!Nt.listening)return;const z=P(_),te=re(z);if(te){Z(ne(te,{replace:!0,force:!0}),z).catch(fn);return}d=z;const c=u.value;Ft&&Fu(Ur(c.fullPath,A.delta),es()),G(z,c).catch(l=>st(l,12)?l:st(l,2)?(Z(ne(I(l.to),{force:!0}),z).then(h=>{st(h,20)&&!A.delta&&A.type===Sn.pop&&r.go(-1,!1)}).catch(fn),Promise.reject()):(A.delta&&r.go(-A.delta,!1),J(l,z,c))).then(l=>{l=l||O(z,c,!1),l&&(A.delta&&!st(l,8)?r.go(-A.delta,!1):A.type===Sn.pop&&st(l,20)&&r.go(-1,!1)),oe(z,c,l)}).catch(fn)}))}let Ee=en(),he=en(),ee;function J(_,k,A){Be(_);const z=he.list();return z.length?z.forEach(te=>te(_,k,A)):console.error(_),Promise.reject(_)}function De(){return ee&&u.value!==gt?Promise.resolve():new Promise((_,k)=>{Ee.add([_,k])})}function Be(_){return ee||(ee=!_,pe(),Ee.list().forEach(([k,A])=>_?A(_):k()),Ee.reset()),_}function we(_,k,A,z){const{scrollBehavior:te}=e;if(!Ft||!te)return Promise.resolve();const c=!A&&Vu(Ur(_.fullPath,0))||(z||!A)&&history.state&&history.state.scroll||null;return Lo().then(()=>te(_,k,c)).then(l=>l&&Du(l)).catch(l=>J(l,_,k))}const xe=_=>r.go(_);let Fe;const ht=new Set,Nt={currentRoute:u,listening:!0,addRoute:m,removeRoute:x,clearRoutes:t.clearRoutes,hasRoute:$,getRoutes:S,resolve:P,options:e,push:L,replace:K,go:xe,back:()=>xe(-1),forward:()=>xe(1),beforeEach:o.add,beforeResolve:i.add,afterEach:a.add,onError:he.add,isReady:De,install(_){const k=this;_.component("RouterLink",df),_.component("RouterView",mf),_.config.globalProperties.$router=k,Object.defineProperty(_.config.globalProperties,"$route",{enumerable:!0,get:()=>Ut(u)}),Ft&&!Fe&&u.value===gt&&(Fe=!0,L(r.location).catch(te=>{}));const A={};for(const te in gt)Object.defineProperty(A,te,{get:()=>u.value[te],enumerable:!0});_.provide(er,k),_.provide(Hi,Po(A)),_.provide(Is,u);const z=_.unmount;ht.add(_),_.unmount=function(){ht.delete(_),ht.size<1&&(d=gt,Y&&Y(),Y=null,u.value=gt,Fe=!1,ee=!1),z()}}};function $e(_){return _.reduce((k,A)=>k.then(()=>V(A)),Promise.resolve())}return Nt}function yf(e,t){const n=[],s=[],r=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const a=t.matched[i];a&&(e.matched.find(d=>Yt(d,a))?s.push(a):n.push(a));const u=e.matched[i];u&&(t.matched.find(d=>Yt(d,u))||r.push(u))}return[n,s,r]}const bf={},_f={class:"item"},wf={class:"details"};function Sf(e,t){return U(),q("div",_f,[C("i",null,[ls(e.$slots,"icon",{},void 0)]),C("div",wf,[C("h3",null,[ls(e.$slots,"heading",{},void 0)]),ls(e.$slots,"default",{},void 0)])])}const tn=dt(bf,[["render",Sf],["__scopeId","data-v-fd0742eb"]]),Cf={},Ef={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"17",fill:"currentColor"};function xf(e,t){return U(),q("svg",Ef,t[0]||(t[0]=[C("path",{d:"M11 2.253a1 1 0 1 0-2 0h2zm-2 13a1 1 0 1 0 2 0H9zm.447-12.167a1 1 0 1 0 1.107-1.666L9.447 3.086zM1 2.253L.447 1.42A1 1 0 0 0 0 2.253h1zm0 13H0a1 1 0 0 0 1.553.833L1 15.253zm8.447.833a1 1 0 1 0 1.107-1.666l-1.107 1.666zm0-14.666a1 1 0 1 0 1.107 1.666L9.447 1.42zM19 2.253h1a1 1 0 0 0-.447-.833L19 2.253zm0 13l-.553.833A1 1 0 0 0 20 15.253h-1zm-9.553-.833a1 1 0 1 0 1.107 1.666L9.447 14.42zM9 2.253v13h2v-13H9zm1.553-.833C9.203.523 7.42 0 5.5 0v2c1.572 0 2.961.431 3.947 1.086l1.107-1.666zM5.5 0C3.58 0 1.797.523.447 1.42l1.107 1.666C2.539 2.431 3.928 2 5.5 2V0zM0 2.253v13h2v-13H0zm1.553 13.833C2.539 15.431 3.928 15 5.5 15v-2c-1.92 0-3.703.523-5.053 1.42l1.107 1.666zM5.5 15c1.572 0 2.961.431 3.947 1.086l1.107-1.666C9.203 13.523 7.42 13 5.5 13v2zm5.053-11.914C11.539 2.431 12.928 2 14.5 2V0c-1.92 0-3.703.523-5.053 1.42l1.107 1.666zM14.5 2c1.573 0 2.961.431 3.947 1.086l1.107-1.666C18.203.523 16.421 0 14.5 0v2zm3.5.253v13h2v-13h-2zm1.553 12.167C18.203 13.523 16.421 13 14.5 13v2c1.573 0 2.961.431 3.947 1.086l1.107-1.666zM14.5 13c-1.92 0-3.703.523-5.053 1.42l1.107 1.666C11.539 15.431 12.928 15 14.5 15v-2z"},null,-1)]))}const Tf=dt(Cf,[["render",xf]]),Rf={},Af={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink","aria-hidden":"true",role:"img",class:"iconify iconify--mdi",width:"24",height:"24",preserveAspectRatio:"xMidYMid meet",viewBox:"0 0 24 24"};function Pf(e,t){return U(),q("svg",Af,t[0]||(t[0]=[C("path",{d:"M20 18v-4h-3v1h-2v-1H9v1H7v-1H4v4h16M6.33 8l-1.74 4H7v-1h2v1h6v-1h2v1h2.41l-1.74-4H6.33M9 5v1h6V5H9m12.84 7.61c.1.22.16.48.16.8V18c0 .53-.21 1-.6 1.41c-.4.4-.85.59-1.4.59H4c-.55 0-1-.19-1.4-.59C2.21 19 2 18.53 2 18v-4.59c0-.32.06-.58.16-.8L4.5 7.22C4.84 6.41 5.45 6 6.33 6H7V5c0-.55.18-1 .57-1.41C7.96 3.2 8.44 3 9 3h6c.56 0 1.04.2 1.43.59c.39.41.57.86.57 1.41v1h.67c.88 0 1.49.41 1.83 1.22l2.34 5.39z",fill:"currentColor"},null,-1)]))}const Mf=dt(Rf,[["render",Pf]]),Of={},$f={xmlns:"http://www.w3.org/2000/svg",width:"18",height:"20",fill:"currentColor"};function If(e,t){return U(),q("svg",$f,t[0]||(t[0]=[C("path",{d:"M11.447 8.894a1 1 0 1 0-.894-1.789l.894 1.789zm-2.894-.789a1 1 0 1 0 .894 1.789l-.894-1.789zm0 1.789a1 1 0 1 0 .894-1.789l-.894 1.789zM7.447 7.106a1 1 0 1 0-.894 1.789l.894-1.789zM10 9a1 1 0 1 0-2 0h2zm-2 2.5a1 1 0 1 0 2 0H8zm9.447-5.606a1 1 0 1 0-.894-1.789l.894 1.789zm-2.894-.789a1 1 0 1 0 .894 1.789l-.894-1.789zm2 .789a1 1 0 1 0 .894-1.789l-.894 1.789zm-1.106-2.789a1 1 0 1 0-.894 1.789l.894-1.789zM18 5a1 1 0 1 0-2 0h2zm-2 2.5a1 1 0 1 0 2 0h-2zm-5.447-4.606a1 1 0 1 0 .894-1.789l-.894 1.789zM9 1l.447-.894a1 1 0 0 0-.894 0L9 1zm-2.447.106a1 1 0 1 0 .894 1.789l-.894-1.789zm-6 3a1 1 0 1 0 .894 1.789L.553 4.106zm2.894.789a1 1 0 1 0-.894-1.789l.894 1.789zm-2-.789a1 1 0 1 0-.894 1.789l.894-1.789zm1.106 2.789a1 1 0 1 0 .894-1.789l-.894 1.789zM2 5a1 1 0 1 0-2 0h2zM0 7.5a1 1 0 1 0 2 0H0zm8.553 12.394a1 1 0 1 0 .894-1.789l-.894 1.789zm-1.106-2.789a1 1 0 1 0-.894 1.789l.894-1.789zm1.106 1a1 1 0 1 0 .894 1.789l-.894-1.789zm2.894.789a1 1 0 1 0-.894-1.789l.894 1.789zM8 19a1 1 0 1 0 2 0H8zm2-2.5a1 1 0 1 0-2 0h2zm-7.447.394a1 1 0 1 0 .894-1.789l-.894 1.789zM1 15H0a1 1 0 0 0 .553.894L1 15zm1-2.5a1 1 0 1 0-2 0h2zm12.553 2.606a1 1 0 1 0 .894 1.789l-.894-1.789zM17 15l.447.894A1 1 0 0 0 18 15h-1zm1-2.5a1 1 0 1 0-2 0h2zm-7.447-5.394l-2 1 .894 1.789 2-1-.894-1.789zm-1.106 1l-2-1-.894 1.789 2 1 .894-1.789zM8 9v2.5h2V9H8zm8.553-4.894l-2 1 .894 1.789 2-1-.894-1.789zm.894 0l-2-1-.894 1.789 2 1 .894-1.789zM16 5v2.5h2V5h-2zm-4.553-3.894l-2-1-.894 1.789 2 1 .894-1.789zm-2.894-1l-2 1 .894 1.789 2-1L8.553.106zM1.447 5.894l2-1-.894-1.789-2 1 .894 1.789zm-.894 0l2 1 .894-1.789-2-1-.894 1.789zM0 5v2.5h2V5H0zm9.447 13.106l-2-1-.894 1.789 2 1 .894-1.789zm0 1.789l2-1-.894-1.789-2 1 .894 1.789zM10 19v-2.5H8V19h2zm-6.553-3.894l-2-1-.894 1.789 2 1 .894-1.789zM2 15v-2.5H0V15h2zm13.447 1.894l2-1-.894-1.789-2 1 .894 1.789zM18 15v-2.5h-2V15h2z"},null,-1)]))}const kf=dt(Of,[["render",If]]),Lf={},Nf={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"currentColor"};function zf(e,t){return U(),q("svg",Nf,t[0]||(t[0]=[C("path",{d:"M15 4a1 1 0 1 0 0 2V4zm0 11v-1a1 1 0 0 0-1 1h1zm0 4l-.707.707A1 1 0 0 0 16 19h-1zm-4-4l.707-.707A1 1 0 0 0 11 14v1zm-4.707-1.293a1 1 0 0 0-1.414 1.414l1.414-1.414zm-.707.707l-.707-.707.707.707zM9 11v-1a1 1 0 0 0-.707.293L9 11zm-4 0h1a1 1 0 0 0-1-1v1zm0 4H4a1 1 0 0 0 1.707.707L5 15zm10-9h2V4h-2v2zm2 0a1 1 0 0 1 1 1h2a3 3 0 0 0-3-3v2zm1 1v6h2V7h-2zm0 6a1 1 0 0 1-1 1v2a3 3 0 0 0 3-3h-2zm-1 1h-2v2h2v-2zm-3 1v4h2v-4h-2zm1.707 3.293l-4-4-1.414 1.414 4 4 1.414-1.414zM11 14H7v2h4v-2zm-4 0c-.276 0-.525-.111-.707-.293l-1.414 1.414C5.42 15.663 6.172 16 7 16v-2zm-.707 1.121l3.414-3.414-1.414-1.414-3.414 3.414 1.414 1.414zM9 12h4v-2H9v2zm4 0a3 3 0 0 0 3-3h-2a1 1 0 0 1-1 1v2zm3-3V3h-2v6h2zm0-6a3 3 0 0 0-3-3v2a1 1 0 0 1 1 1h2zm-3-3H3v2h10V0zM3 0a3 3 0 0 0-3 3h2a1 1 0 0 1 1-1V0zM0 3v6h2V3H0zm0 6a3 3 0 0 0 3 3v-2a1 1 0 0 1-1-1H0zm3 3h2v-2H3v2zm1-1v4h2v-4H4zm1.707 4.707l.586-.586-1.414-1.414-.586.586 1.414 1.414z"},null,-1)]))}const Hf=dt(Lf,[["render",zf]]),Df={},Ff={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"currentColor"};function Vf(e,t){return U(),q("svg",Ff,t[0]||(t[0]=[C("path",{d:"M10 3.22l-.61-.6a5.5 5.5 0 0 0-7.666.105 5.5 5.5 0 0 0-.114 7.665L10 18.78l8.39-8.4a5.5 5.5 0 0 0-.114-7.665 5.5 5.5 0 0 0-7.666-.105l-.61.61z"},null,-1)]))}const jf=dt(Df,[["render",Vf]]),Bf=Lt({__name:"TheWelcome",setup(e){const t=()=>fetch("/__open-in-editor?file=README.md");return(n,s)=>(U(),q(me,null,[le(tn,null,{icon:_e(()=>[le(Tf)]),heading:_e(()=>s[0]||(s[0]=[X("Documentation")])),default:_e(()=>[s[1]||(s[1]=X(" Vue’s ")),s[2]||(s[2]=C("a",{href:"https://vuejs.org/",target:"_blank",rel:"noopener"},"official documentation",-1)),s[3]||(s[3]=X(" provides you with all information you need to get started. "))]),_:1,__:[1,2,3]}),le(tn,null,{icon:_e(()=>[le(Mf)]),heading:_e(()=>s[4]||(s[4]=[X("Tooling")])),default:_e(()=>[s[6]||(s[6]=X(" This project is served and bundled with ")),s[7]||(s[7]=C("a",{href:"https://vite.dev/guide/features.html",target:"_blank",rel:"noopener"},"Vite",-1)),s[8]||(s[8]=X(". The recommended IDE setup is ")),s[9]||(s[9]=C("a",{href:"https://code.visualstudio.com/",target:"_blank",rel:"noopener"},"VSCode",-1)),s[10]||(s[10]=X(" + ")),s[11]||(s[11]=C("a",{href:"https://github.com/vuejs/language-tools",target:"_blank",rel:"noopener"},"Vue - Official",-1)),s[12]||(s[12]=X(". If you need to test your components and web pages, check out ")),s[13]||(s[13]=C("a",{href:"https://vitest.dev/",target:"_blank",rel:"noopener"},"Vitest",-1)),s[14]||(s[14]=X(" and ")),s[15]||(s[15]=C("a",{href:"https://www.cypress.io/",target:"_blank",rel:"noopener"},"Cypress",-1)),s[16]||(s[16]=X(" / ")),s[17]||(s[17]=C("a",{href:"https://playwright.dev/",target:"_blank",rel:"noopener"},"Playwright",-1)),s[18]||(s[18]=X(". ")),s[19]||(s[19]=C("br",null,null,-1)),s[20]||(s[20]=X(" More instructions are available in ")),C("a",{href:"javascript:void(0)",onClick:t},s[5]||(s[5]=[C("code",null,"README.md",-1)])),s[21]||(s[21]=X(". "))]),_:1,__:[6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21]}),le(tn,null,{icon:_e(()=>[le(kf)]),heading:_e(()=>s[22]||(s[22]=[X("Ecosystem")])),default:_e(()=>[s[23]||(s[23]=X(" Get official tools and libraries for your project: ")),s[24]||(s[24]=C("a",{href:"https://pinia.vuejs.org/",target:"_blank",rel:"noopener"},"Pinia",-1)),s[25]||(s[25]=X(", ")),s[26]||(s[26]=C("a",{href:"https://router.vuejs.org/",target:"_blank",rel:"noopener"},"Vue Router",-1)),s[27]||(s[27]=X(", ")),s[28]||(s[28]=C("a",{href:"https://test-utils.vuejs.org/",target:"_blank",rel:"noopener"},"Vue Test Utils",-1)),s[29]||(s[29]=X(", and ")),s[30]||(s[30]=C("a",{href:"https://github.com/vuejs/devtools",target:"_blank",rel:"noopener"},"Vue Dev Tools",-1)),s[31]||(s[31]=X(". If you need more resources, we suggest paying ")),s[32]||(s[32]=C("a",{href:"https://github.com/vuejs/awesome-vue",target:"_blank",rel:"noopener"},"Awesome Vue",-1)),s[33]||(s[33]=X(" a visit. "))]),_:1,__:[23,24,25,26,27,28,29,30,31,32,33]}),le(tn,null,{icon:_e(()=>[le(Hf)]),heading:_e(()=>s[34]||(s[34]=[X("Community")])),default:_e(()=>[s[35]||(s[35]=X(" Got stuck? Ask your question on ")),s[36]||(s[36]=C("a",{href:"https://chat.vuejs.org",target:"_blank",rel:"noopener"},"Vue Land",-1)),s[37]||(s[37]=X(" (our official Discord server), or ")),s[38]||(s[38]=C("a",{href:"https://stackoverflow.com/questions/tagged/vue.js",target:"_blank",rel:"noopener"},"StackOverflow",-1)),s[39]||(s[39]=X(". You should also follow the official ")),s[40]||(s[40]=C("a",{href:"https://bsky.app/profile/vuejs.org",target:"_blank",rel:"noopener"},"@vuejs.org",-1)),s[41]||(s[41]=X(" Bluesky account or the ")),s[42]||(s[42]=C("a",{href:"https://x.com/vuejs",target:"_blank",rel:"noopener"},"@vuejs",-1)),s[43]||(s[43]=X(" X account for latest news in the Vue world. "))]),_:1,__:[35,36,37,38,39,40,41,42,43]}),le(tn,null,{icon:_e(()=>[le(jf)]),heading:_e(()=>s[44]||(s[44]=[X("Support Vue")])),default:_e(()=>[s[45]||(s[45]=X(" As an independent project, Vue relies on community backing for its sustainability. You can help us by ")),s[46]||(s[46]=C("a",{href:"https://vuejs.org/sponsor/",target:"_blank",rel:"noopener"},"becoming a sponsor",-1)),s[47]||(s[47]=X(". "))]),_:1,__:[45,46,47]})],64))}}),Uf=Lt({__name:"HomeView",setup(e){return(t,n)=>(U(),q("main",null,[le(Bf)]))}}),Kf=vf({history:Ku("/"),routes:[{path:"/",name:"home",component:Uf},{path:"/about",name:"about",component:()=>pu(()=>import("./AboutView-BChy9Mv0.js"),__vite__mapDeps([0,1]))}]}),tr=aa(fu);tr.use(ha());tr.use(Kf);tr.mount("#app");export{dt as _,C as a,q as c,U as o};
