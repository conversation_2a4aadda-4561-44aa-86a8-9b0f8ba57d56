{"name": "path-key", "version": "4.0.0", "description": "Get the PATH environment variable key cross-platform", "license": "MIT", "repository": "sindresorhus/path-key", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["path", "key", "environment", "env", "variable", "get", "cross-platform", "windows"], "devDependencies": {"@types/node": "^14.14.37", "ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}}