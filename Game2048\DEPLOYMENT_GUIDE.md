
# 🚀 部署指南

您的2048游戏已准备好部署！请按照以下步骤操作：

## 第1步：部署后端到Railway

1. 访问 https://railway.app
2. 使用GitHub账号登录
3. 点击 "New Project" -> "Deploy from GitHub repo"
4. 选择您的仓库
5. 选择 "backend" 文件夹作为根目录
6. 设置环境变量：
   - NODE_ENV=production
   - PORT=3001
   - CORS_ORIGIN=https://your-frontend-domain.vercel.app (稍后更新)
7. 点击部署
8. 记录生成的API URL (例如: https://your-app.railway.app)

## 第2步：部署前端到Vercel

1. 访问 https://vercel.com
2. 使用GitHub账号登录
3. 点击 "New Project"
4. 选择您的仓库
5. 设置环境变量：
   - VITE_API_BASE_URL=https://your-app.railway.app/api
6. 点击部署
7. 记录生成的前端URL (例如: https://your-app.vercel.app)

## 第3步：更新CORS配置

1. 回到Railway控制台
2. 更新环境变量：
   - CORS_ORIGIN=https://your-app.vercel.app
3. 重新部署后端

## 第4步：测试部署

1. 访问您的前端URL
2. 测试游戏功能
3. 测试分数提交
4. 测试排行榜

## 🎉 完成！

您的2048游戏现在已经在线了！分享URL给朋友们吧！

---

如果遇到问题，请检查：
- 环境变量是否正确设置
- API地址是否正确
- CORS配置是否匹配
