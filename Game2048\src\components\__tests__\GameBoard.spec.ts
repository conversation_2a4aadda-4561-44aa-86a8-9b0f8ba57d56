import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import GameBoard from '../GameBoard.vue'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
vi.stubGlobal('localStorage', localStorageMock)

describe('GameBoard', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders properly', () => {
    const wrapper = mount(GameBoard)
    expect(wrapper.find('.game-container').exists()).toBe(true)
    expect(wrapper.find('.game-board').exists()).toBe(true)
    expect(wrapper.find('.score-container').exists()).toBe(true)
  })

  it('initializes with correct score', () => {
    const wrapper = mount(GameBoard)
    expect(wrapper.find('.score-value').text()).toBe('0')
  })

  it('shows restart button', () => {
    const wrapper = mount(GameBoard)
    const restartBtn = wrapper.find('.restart-btn')
    expect(restartBtn.exists()).toBe(true)
    expect(restartBtn.text()).toBe('新游戏')
  })

  it('shows auto-save hint', () => {
    const wrapper = mount(GameBoard)
    const hint = wrapper.find('.auto-save-hint')
    expect(hint.exists()).toBe(true)
    expect(hint.text()).toBe('游戏进度自动保存')
  })

  it('has 16 grid cells', () => {
    const wrapper = mount(GameBoard)
    const gridCells = wrapper.findAll('.grid-cell')
    expect(gridCells).toHaveLength(16)
  })

  it('handles keyboard events', async () => {
    const wrapper = mount(GameBoard)
    
    // Simulate arrow key press
    await wrapper.trigger('keydown', { key: 'ArrowLeft' })
    
    // Should not throw error
    expect(wrapper.vm).toBeTruthy()
  })

  it('handles touch events', async () => {
    const wrapper = mount(GameBoard)
    const gameBoard = wrapper.find('.game-board')
    
    // Simulate touch start
    await gameBoard.trigger('touchstart', {
      touches: [{ clientX: 100, clientY: 100 }]
    })
    
    // Simulate touch end (swipe right)
    await gameBoard.trigger('touchend', {
      changedTouches: [{ clientX: 200, clientY: 100 }]
    })
    
    // Should not throw error
    expect(wrapper.vm).toBeTruthy()
  })

  it('saves and loads best score', () => {
    localStorageMock.getItem.mockReturnValue('1000')
    
    const wrapper = mount(GameBoard)
    
    expect(localStorageMock.getItem).toHaveBeenCalledWith('game2048-best-score')
  })

  it('saves game state to localStorage', async () => {
    const wrapper = mount(GameBoard)

    // Wait for component to be fully mounted
    await wrapper.vm.$nextTick()

    // Game should save initial state
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'game2048-state',
      expect.any(String)
    )
  })

  it('clears game state on restart', async () => {
    const wrapper = mount(GameBoard)
    const restartBtn = wrapper.find('.restart-btn')
    
    await restartBtn.trigger('click')
    
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('game2048-state')
  })
})
