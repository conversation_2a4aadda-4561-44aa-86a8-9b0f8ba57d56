/**
 * 自动化部署脚本
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始自动化部署流程...\n');

// 检查是否安装了必要的CLI工具
function checkCLITools() {
  console.log('🔍 检查CLI工具...');
  
  const tools = [
    { name: 'git', command: 'git --version' },
    { name: 'npm', command: 'npm --version' },
    { name: 'node', command: 'node --version' }
  ];
  
  for (const tool of tools) {
    try {
      const version = execSync(tool.command, { encoding: 'utf8' }).trim();
      console.log(`✅ ${tool.name}: ${version}`);
    } catch (error) {
      console.log(`❌ ${tool.name} 未安装或不可用`);
      process.exit(1);
    }
  }
}

// 运行最终测试
function runFinalTests() {
  console.log('\n🧪 运行最终测试...');
  
  try {
    execSync('node final-test.cjs', { stdio: 'inherit' });
    console.log('✅ 所有测试通过');
  } catch (error) {
    console.log('❌ 测试失败，请修复问题后重试');
    process.exit(1);
  }
}

// 构建前端
function buildFrontend() {
  console.log('\n📦 构建前端应用...');
  
  try {
    execSync('npm run build', { stdio: 'inherit' });
    console.log('✅ 前端构建完成');
  } catch (error) {
    console.log('❌ 前端构建失败');
    process.exit(1);
  }
}

// 创建部署指南
function createDeploymentGuide() {
  console.log('\n📋 创建部署指南...');
  
  const guide = `
# 🚀 部署指南

您的2048游戏已准备好部署！请按照以下步骤操作：

## 第1步：部署后端到Railway

1. 访问 https://railway.app
2. 使用GitHub账号登录
3. 点击 "New Project" -> "Deploy from GitHub repo"
4. 选择您的仓库
5. 选择 "backend" 文件夹作为根目录
6. 设置环境变量：
   - NODE_ENV=production
   - PORT=3001
   - CORS_ORIGIN=https://your-frontend-domain.vercel.app (稍后更新)
7. 点击部署
8. 记录生成的API URL (例如: https://your-app.railway.app)

## 第2步：部署前端到Vercel

1. 访问 https://vercel.com
2. 使用GitHub账号登录
3. 点击 "New Project"
4. 选择您的仓库
5. 设置环境变量：
   - VITE_API_BASE_URL=https://your-app.railway.app/api
6. 点击部署
7. 记录生成的前端URL (例如: https://your-app.vercel.app)

## 第3步：更新CORS配置

1. 回到Railway控制台
2. 更新环境变量：
   - CORS_ORIGIN=https://your-app.vercel.app
3. 重新部署后端

## 第4步：测试部署

1. 访问您的前端URL
2. 测试游戏功能
3. 测试分数提交
4. 测试排行榜

## 🎉 完成！

您的2048游戏现在已经在线了！分享URL给朋友们吧！

---

如果遇到问题，请检查：
- 环境变量是否正确设置
- API地址是否正确
- CORS配置是否匹配
`;

  fs.writeFileSync('DEPLOYMENT_GUIDE.md', guide);
  console.log('✅ 部署指南已创建: DEPLOYMENT_GUIDE.md');
}

// 显示下一步操作
function showNextSteps() {
  console.log('\n🎯 下一步操作：');
  console.log('1. 将代码推送到GitHub仓库');
  console.log('2. 按照 DEPLOYMENT_GUIDE.md 进行部署');
  console.log('3. 测试部署后的应用');
  console.log('4. 分享您的游戏URL！');
  
  console.log('\n📋 需要的账号：');
  console.log('- GitHub账号 (存储代码)');
  console.log('- Railway账号 (后端部署) - https://railway.app');
  console.log('- Vercel账号 (前端部署) - https://vercel.com');
  
  console.log('\n🔗 有用的链接：');
  console.log('- Railway文档: https://docs.railway.app');
  console.log('- Vercel文档: https://vercel.com/docs');
  console.log('- 项目部署文档: ./DEPLOYMENT.md');
}

// 主函数
function main() {
  try {
    checkCLITools();
    runFinalTests();
    buildFrontend();
    createDeploymentGuide();
    showNextSteps();
    
    console.log('\n🎉 部署准备完成！');
    console.log('您的2048游戏已准备好发布到互联网！');
    
  } catch (error) {
    console.error('❌ 部署准备失败:', error.message);
    process.exit(1);
  }
}

// 运行主函数
main();
