const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 数据库文件路径
const dbPath = path.join(__dirname, 'game2048.db');

console.log('🧹 开始数据库清理...');

// 连接数据库
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ 数据库连接失败:', err.message);
    process.exit(1);
  }
  console.log('✅ 数据库连接成功');
});

// 清理函数
async function cleanupDatabase() {
  return new Promise((resolve, reject) => {
    // 删除30天前的记录，但保留每个玩家的最高分
    const cleanupQuery = `
      DELETE FROM scores 
      WHERE id NOT IN (
        SELECT id FROM (
          SELECT id, playerName, score, 
                 ROW_NUMBER() OVER (PARTITION BY playerName ORDER BY score DESC, createdAt DESC) as rn
          FROM scores
        ) ranked
        WHERE rn = 1
      )
      AND createdAt < datetime('now', '-30 days')
    `;

    db.run(cleanupQuery, function(err) {
      if (err) {
        console.error('❌ 清理失败:', err.message);
        reject(err);
        return;
      }

      console.log(`🗑️  删除了 ${this.changes} 条旧记录`);

      // 执行VACUUM来回收空间
      db.run('VACUUM', function(err) {
        if (err) {
          console.error('❌ 数据库压缩失败:', err.message);
          reject(err);
          return;
        }

        console.log('✅ 数据库压缩完成');
        resolve();
      });
    });
  });
}

// 获取数据库统计信息
function getStats() {
  return new Promise((resolve, reject) => {
    const queries = [
      'SELECT COUNT(*) as totalRecords FROM scores',
      'SELECT COUNT(DISTINCT playerName) as uniquePlayers FROM scores',
      'SELECT MAX(score) as highestScore FROM scores'
    ];

    Promise.all(queries.map(query => 
      new Promise((resolve, reject) => {
        db.get(query, (err, row) => {
          if (err) reject(err);
          else resolve(row);
        });
      })
    )).then(results => {
      resolve({
        totalRecords: results[0].totalRecords,
        uniquePlayers: results[1].uniquePlayers,
        highestScore: results[2].highestScore || 0
      });
    }).catch(reject);
  });
}

// 主函数
async function main() {
  try {
    // 显示清理前的统计信息
    console.log('\n📊 清理前统计:');
    const statsBefore = await getStats();
    console.log(`   总记录数: ${statsBefore.totalRecords}`);
    console.log(`   独特玩家: ${statsBefore.uniquePlayers}`);
    console.log(`   最高分数: ${statsBefore.highestScore.toLocaleString()}`);

    // 执行清理
    await cleanupDatabase();

    // 显示清理后的统计信息
    console.log('\n📊 清理后统计:');
    const statsAfter = await getStats();
    console.log(`   总记录数: ${statsAfter.totalRecords}`);
    console.log(`   独特玩家: ${statsAfter.uniquePlayers}`);
    console.log(`   最高分数: ${statsAfter.highestScore.toLocaleString()}`);

    console.log('\n🎉 数据库清理完成！');

  } catch (error) {
    console.error('❌ 清理过程中出错:', error.message);
  } finally {
    // 关闭数据库连接
    db.close((err) => {
      if (err) {
        console.error('❌ 关闭数据库连接失败:', err.message);
      } else {
        console.log('✅ 数据库连接已关闭');
      }
      process.exit(0);
    });
  }
}

// 运行主函数
main();
