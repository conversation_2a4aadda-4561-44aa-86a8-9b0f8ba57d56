# is-inside-container

> Check if the process is running inside a container

## Install

```sh
npm install is-inside-container
```

## Usage

```js
import isInsideContainer from 'is-inside-container';

if (isInsideContainer()) {
	console.log('Running inside a container');
}
```

## CLI

```sh
is-inside-container
```

Exits with code 0 if inside a container and 2 if not.

## Supported containers

- Docker
- Podman

## Related

- [is-docker](https://github.com/sindresorhus/is-docker) - Check if the process is running inside a Docker container
