/**
 * 生产环境启动脚本
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 启动2048游戏后端服务...');

// 设置环境变量
process.env.NODE_ENV = process.env.NODE_ENV || 'production';

// 启动服务器
const server = spawn('node', ['server.js'], {
  cwd: __dirname,
  stdio: 'inherit',
  env: process.env
});

server.on('error', (error) => {
  console.error('❌ 服务器启动失败:', error);
  process.exit(1);
});

server.on('close', (code) => {
  console.log(`🔴 服务器进程退出，退出码: ${code}`);
  process.exit(code);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 收到关闭信号，正在关闭服务器...');
  server.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 收到终止信号，正在关闭服务器...');
  server.kill('SIGTERM');
});
