const crypto = require('crypto');

const API_BASE_URL = 'http://localhost:3001/api';

// 验证载荷生成函数（与前端保持一致）
function generateValidationPayload(score, board) {
  const data = `${score}-${JSON.stringify(board)}-${Date.now()}`;
  return crypto.createHash('sha256').update(data).digest('hex');
}

// 测试用的游戏板
const testBoard = [
  [2, 4, 8, 16],
  [32, 64, 128, 256],
  [512, 1024, 2048, 0],
  [0, 0, 0, 0]
];

// 测试重复名字防护
async function testDuplicateNamePrevention() {
  console.log('🧪 测试重复名字防护功能...\n');

  const playerName = '测试重复';

  // 第一次提交 - 分数 1000
  console.log('1️⃣ 第一次提交分数 1000...');
  try {
    const response1 = await fetch(`${API_BASE_URL}/scores`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        playerName,
        score: 1000,
        validationPayload: generateValidationPayload(1000, testBoard)
      })
    });

    const data1 = await response1.json();
    console.log('响应:', data1);

    if (response1.ok) {
      console.log('✅ 第一次提交成功\n');
    } else {
      console.log('❌ 第一次提交失败:', data1.error, '\n');
    }
  } catch (error) {
    console.error('❌ 网络错误:', error.message, '\n');
  }

  // 等待一秒避免频率限制
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 第二次提交 - 分数 800（更低）
  console.log('2️⃣ 第二次提交分数 800（更低）...');
  try {
    const response2 = await fetch(`${API_BASE_URL}/scores`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        playerName,
        score: 800,
        validationPayload: generateValidationPayload(800, testBoard)
      })
    });

    const data2 = await response2.json();
    console.log('响应:', data2);

    if (response2.ok) {
      console.log('✅ 第二次提交成功（不应该发生）\n');
    } else {
      console.log('✅ 第二次提交被正确拒绝:', data2.error, '\n');
    }
  } catch (error) {
    console.error('❌ 网络错误:', error.message, '\n');
  }

  // 等待一秒避免频率限制
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 第三次提交 - 分数 1500（更高）
  console.log('3️⃣ 第三次提交分数 1500（更高）...');
  try {
    const response3 = await fetch(`${API_BASE_URL}/scores`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        playerName,
        score: 1500,
        validationPayload: generateValidationPayload(1500, testBoard)
      })
    });

    const data3 = await response3.json();
    console.log('响应:', data3);

    if (response3.ok) {
      console.log('✅ 第三次提交成功（新纪录）');
      if (data3.isNewRecord) {
        console.log('🎉 正确标记为新纪录！\n');
      } else {
        console.log('⚠️  未标记为新纪录\n');
      }
    } else {
      console.log('❌ 第三次提交失败:', data3.error, '\n');
    }
  } catch (error) {
    console.error('❌ 网络错误:', error.message, '\n');
  }

  // 检查排行榜
  console.log('4️⃣ 检查排行榜...');
  try {
    const response = await fetch(`${API_BASE_URL}/leaderboard?period=alltime&limit=10`);
    const data = await response.json();

    if (response.ok) {
      console.log('当前排行榜:');
      data.leaderboard.forEach((entry, index) => {
        console.log(`${index + 1}. ${entry.playerName}: ${entry.score.toLocaleString()}`);
      });

      // 检查是否有重复名字
      const names = data.leaderboard.map(entry => entry.playerName);
      const uniqueNames = [...new Set(names)];

      if (names.length === uniqueNames.length) {
        console.log('✅ 排行榜中没有重复名字');
      } else {
        console.log('❌ 排行榜中仍有重复名字');
      }
    } else {
      console.log('❌ 获取排行榜失败:', data.error);
    }
  } catch (error) {
    console.error('❌ 网络错误:', error.message);
  }
}

// 运行测试
testDuplicateNamePrevention().catch(console.error);