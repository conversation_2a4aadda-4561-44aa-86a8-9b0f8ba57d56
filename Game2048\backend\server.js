const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const crypto = require('crypto');

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(helmet());
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:5175', 'http://localhost:3000'],
  credentials: true
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));

// 数据库初始化
const dbPath = path.join(__dirname, 'game2048.db');
const db = new sqlite3.Database(dbPath);

// 创建表
db.serialize(() => {
  db.run(`
    CREATE TABLE IF NOT EXISTS scores (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      playerName TEXT NOT NULL,
      score INTEGER NOT NULL,
      validationPayload TEXT NOT NULL,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      ipAddress TEXT
    )
  `);
  
  // 创建索引以提高查询性能
  db.run(`CREATE INDEX IF NOT EXISTS idx_score_desc ON scores(score DESC)`);
  db.run(`CREATE INDEX IF NOT EXISTS idx_created_at ON scores(createdAt DESC)`);
});

// 敏感词过滤
const badWords = ['fuck', 'shit', 'damn', '操', '草', '妈', '傻', '蠢', '死'];

function filterBadWords(text) {
  let filtered = text;
  badWords.forEach(word => {
    const regex = new RegExp(word, 'gi');
    filtered = filtered.replace(regex, '*'.repeat(word.length));
  });
  return filtered;
}

// 验证载荷生成和验证（与前端保持一致）
function generateValidationPayload(score, board) {
  const data = `${score}-${JSON.stringify(board)}-${Date.now()}`;
  // 使用与前端相同的简单哈希函数
  let hash = 0;
  for (let i = 0; i < data.length; i++) {
    const char = data.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  return Math.abs(hash).toString(16);
}

function validatePayload(score, validationPayload) {
  // 基础验证：检查分数是否合理
  if (score < 0 || score > 1000000) {
    return false;
  }

  // 检查载荷格式（简单哈希的长度是可变的）
  if (!validationPayload || validationPayload.length === 0) {
    return false;
  }

  // 检查是否为有效的十六进制字符串
  if (!/^[0-9a-f]+$/i.test(validationPayload)) {
    return false;
  }

  return true;
}

// API 路由

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// 提交分数
app.post('/api/scores', (req, res) => {
  const { playerName, score, validationPayload } = req.body;
  const ipAddress = req.ip || req.connection.remoteAddress;
  
  // 输入验证
  if (!playerName || typeof playerName !== 'string') {
    return res.status(400).json({ error: '玩家名称是必需的' });
  }
  
  if (!score || typeof score !== 'number') {
    return res.status(400).json({ error: '分数是必需的' });
  }
  
  if (!validationPayload || typeof validationPayload !== 'string') {
    return res.status(400).json({ error: '验证载荷是必需的' });
  }
  
  // 验证载荷
  if (!validatePayload(score, validationPayload)) {
    return res.status(400).json({ error: '无效的分数或验证载荷' });
  }
  
  // 过滤玩家名称
  const filteredName = filterBadWords(playerName.trim().substring(0, 20));
  
  if (filteredName.length === 0) {
    return res.status(400).json({ error: '玩家名称不能为空' });
  }
  
  // 检查同一IP的提交频率（防止刷分）
  const oneMinuteAgo = new Date(Date.now() - 60000).toISOString();
  
  db.get(
    'SELECT COUNT(*) as count FROM scores WHERE ipAddress = ? AND createdAt > ?',
    [ipAddress, oneMinuteAgo],
    (err, row) => {
      if (err) {
        console.error('Database error:', err);
        return res.status(500).json({ error: '服务器错误' });
      }
      
      if (row.count >= 3) {
        return res.status(429).json({ error: '提交过于频繁，请稍后再试' });
      }
      
      // 检查是否已存在该玩家名字
      db.get(
        'SELECT id, score FROM scores WHERE playerName = ? ORDER BY score DESC LIMIT 1',
        [filteredName],
        (err, existingRecord) => {
          if (err) {
            console.error('Database error:', err);
            return res.status(500).json({ error: '服务器错误' });
          }

          if (existingRecord) {
            // 如果新分数更高，更新记录
            if (score > existingRecord.score) {
              db.run(
                'UPDATE scores SET score = ?, validationPayload = ?, createdAt = CURRENT_TIMESTAMP, ipAddress = ? WHERE id = ?',
                [score, validationPayload, ipAddress, existingRecord.id],
                function(err) {
                  if (err) {
                    console.error('Database error:', err);
                    if (err.code === 'SQLITE_FULL') {
                      return res.status(507).json({ error: '服务器存储空间不足，请联系管理员' });
                    }
                    return res.status(500).json({ error: '更新分数失败' });
                  }

                  res.json({
                    success: true,
                    id: existingRecord.id,
                    message: '分数更新成功！新纪录！',
                    isNewRecord: true
                  });
                }
              );
            } else {
              // 分数没有更高，返回提示
              return res.status(400).json({
                error: `您的最高分是 ${existingRecord.score.toLocaleString()}，当前分数 ${score.toLocaleString()} 未超过记录`
              });
            }
          } else {
            // 新玩家，插入分数
            db.run(
              'INSERT INTO scores (playerName, score, validationPayload, ipAddress) VALUES (?, ?, ?, ?)',
              [filteredName, score, validationPayload, ipAddress],
              function(err) {
                if (err) {
                  console.error('Database error:', err);
                  if (err.code === 'SQLITE_FULL') {
                    return res.status(507).json({ error: '服务器存储空间不足，请联系管理员' });
                  }
                  return res.status(500).json({ error: '保存分数失败' });
                }

                res.json({
                  success: true,
                  id: this.lastID,
                  message: '分数提交成功',
                  isNewRecord: false
                });
              }
            );
          }
        }
      );
    }
  );
});

// 获取排行榜
app.get('/api/leaderboard', (req, res) => {
  const { period = 'alltime', limit = 10 } = req.query;
  const limitNum = Math.min(parseInt(limit) || 10, 100); // 最多100条
  
  let whereClause = '';
  let params = [limitNum];
  
  if (period === 'daily') {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    whereClause = 'WHERE createdAt >= ?';
    params = [today.toISOString(), limitNum];
  } else if (period === 'weekly') {
    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    whereClause = 'WHERE createdAt >= ?';
    params = [weekAgo.toISOString(), limitNum];
  }
  
  const query = `
    SELECT 
      playerName,
      score,
      createdAt,
      ROW_NUMBER() OVER (ORDER BY score DESC) as rank
    FROM scores 
    ${whereClause}
    ORDER BY score DESC 
    LIMIT ?
  `;
  
  db.all(query, params, (err, rows) => {
    if (err) {
      console.error('Database error:', err);
      return res.status(500).json({ error: '获取排行榜失败' });
    }
    
    res.json({
      period,
      leaderboard: rows.map(row => ({
        rank: row.rank,
        playerName: row.playerName,
        score: row.score,
        createdAt: row.createdAt
      }))
    });
  });
});

// 获取统计信息
app.get('/api/stats', (req, res) => {
  const queries = [
    'SELECT COUNT(*) as totalGames FROM scores',
    'SELECT MAX(score) as highestScore FROM scores',
    'SELECT AVG(score) as averageScore FROM scores',
    'SELECT COUNT(DISTINCT playerName) as uniquePlayers FROM scores'
  ];
  
  Promise.all(queries.map(query => 
    new Promise((resolve, reject) => {
      db.get(query, (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    })
  )).then(results => {
    res.json({
      totalGames: results[0].totalGames,
      highestScore: results[1].highestScore || 0,
      averageScore: Math.round(results[2].averageScore || 0),
      uniquePlayers: results[3].uniquePlayers
    });
  }).catch(err => {
    console.error('Database error:', err);
    res.status(500).json({ error: '获取统计信息失败' });
  });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: '服务器内部错误' });
});

// 404 处理
app.use((req, res) => {
  res.status(404).json({ error: '接口不存在' });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/api/health`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\nShutting down server...');
  db.close((err) => {
    if (err) {
      console.error('Error closing database:', err);
    } else {
      console.log('Database connection closed.');
    }
    process.exit(0);
  });
});
