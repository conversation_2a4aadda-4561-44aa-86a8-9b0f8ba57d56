# 2048游戏部署指南

本文档详细说明如何将2048游戏部署到生产环境。

## 📋 部署前准备

### 1. 运行部署检查
```bash
node deploy-check.cjs
```

### 2. 构建前端应用
```bash
npm run build
npm run preview  # 测试构建结果
```

### 3. 测试后端API
```bash
cd backend
npm start
# 在另一个终端运行测试
cd ..
node tests/api.test.js
```

## 🚀 部署选项

### 选项1: Vercel (前端) + Railway (后端)

#### 部署后端到Railway
1. 访问 [Railway](https://railway.app)
2. 连接GitHub仓库
3. 选择 `backend` 文件夹作为根目录
4. 设置环境变量:
   - `NODE_ENV=production`
   - `PORT=3001`
   - `CORS_ORIGIN=https://your-frontend-domain.vercel.app`
5. 部署完成后记录API URL

#### 部署前端到Vercel
1. 访问 [Vercel](https://vercel.com)
2. 连接GitHub仓库
3. 设置环境变量:
   - `VITE_API_BASE_URL=https://your-backend-domain.railway.app/api`
4. 部署

### 选项2: Netlify (前端) + Render (后端)

#### 部署后端到Render
1. 访问 [Render](https://render.com)
2. 创建新的Web Service
3. 连接GitHub仓库，选择 `backend` 文件夹
4. 设置:
   - Build Command: `npm install`
   - Start Command: `npm start`
   - 环境变量同上
5. 部署

#### 部署前端到Netlify
1. 访问 [Netlify](https://netlify.com)
2. 连接GitHub仓库
3. 设置构建命令: `npm run build`
4. 设置发布目录: `dist`
5. 设置环境变量:
   - `VITE_API_BASE_URL=https://your-backend-domain.onrender.com/api`

### 选项3: Docker部署

#### 构建后端Docker镜像
```bash
cd backend
docker build -t 2048-backend .
docker run -p 3001:3001 -e CORS_ORIGIN=https://your-frontend-domain.com 2048-backend
```

#### 使用Docker Compose
创建 `docker-compose.yml`:
```yaml
version: '3.8'
services:
  backend:
    build: ./backend
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - CORS_ORIGIN=https://your-frontend-domain.com
    volumes:
      - ./data:/app/data
```

## 🔧 环境变量配置

### 前端环境变量
- `VITE_API_BASE_URL`: 后端API的完整URL
- `VITE_APP_TITLE`: 应用标题（可选）

### 后端环境变量
- `NODE_ENV`: 运行环境 (production)
- `PORT`: 服务器端口 (默认3001)
- `CORS_ORIGIN`: 允许的前端域名
- `DATABASE_PATH`: 数据库文件路径

## 🧪 部署后测试

### 1. 健康检查
```bash
curl https://your-backend-domain.com/api/health
```

### 2. 功能测试
- 访问前端URL
- 测试游戏功能
- 测试分数提交
- 测试排行榜显示

### 3. 性能测试
```bash
# 修改tests/stress.test.js中的API_BASE_URL
node tests/stress.test.js
```

## 🔒 安全注意事项

1. **CORS配置**: 确保只允许你的前端域名
2. **速率限制**: 后端已配置基本的速率限制
3. **输入验证**: 所有用户输入都经过验证
4. **HTTPS**: 生产环境必须使用HTTPS

## 📊 监控和维护

### 日志监控
- Railway/Render提供内置日志查看
- 监控错误率和响应时间

### 数据库维护
```bash
# 定期清理旧数据
node backend/cleanup-database.js
```

### 更新部署
1. 推送代码到GitHub
2. 自动触发重新部署
3. 验证部署成功

## 🆘 故障排除

### 常见问题
1. **CORS错误**: 检查后端CORS_ORIGIN配置
2. **API连接失败**: 检查前端VITE_API_BASE_URL配置
3. **数据库错误**: 检查磁盘空间和权限
4. **构建失败**: 检查Node.js版本兼容性

### 调试步骤
1. 检查服务器日志
2. 验证环境变量
3. 测试API端点
4. 检查网络连接

## 📈 性能优化

1. **CDN**: 使用CDN加速静态资源
2. **缓存**: 配置适当的HTTP缓存头
3. **压缩**: 启用gzip压缩
4. **数据库**: 定期清理和优化数据库

---

部署完成后，你的2048游戏就可以在互联网上访问了！🎉
