const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/AboutView-BHcppUv3.js","assets/AboutView-CSIvawM9.css"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(r){if(r.ep)return;r.ep=!0;const o=n(r);fetch(r.href,o)}})();/**
* @vue/shared v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Ls(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const ue={},jt=[],nt=()=>{},Fi=()=>!1,jn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Ns=e=>e.startsWith("onUpdate:"),ye=Object.assign,Ds=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Vi=Object.prototype.hasOwnProperty,se=(e,t)=>Vi.call(e,t),V=Array.isArray,Bt=e=>Bn(e)==="[object Map]",io=e=>Bn(e)==="[object Set]",B=e=>typeof e=="function",pe=e=>typeof e=="string",ht=e=>typeof e=="symbol",he=e=>e!==null&&typeof e=="object",lo=e=>(he(e)||B(e))&&B(e.then)&&B(e.catch),ao=Object.prototype.toString,Bn=e=>ao.call(e),ji=e=>Bn(e).slice(8,-1),co=e=>Bn(e)==="[object Object]",zs=e=>pe(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,rn=Ls(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Un=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Bi=/-(\w)/g,xt=Un(e=>e.replace(Bi,(t,n)=>n?n.toUpperCase():"")),Ui=/\B([A-Z])/g,Pt=Un(e=>e.replace(Ui,"-$1").toLowerCase()),uo=Un(e=>e.charAt(0).toUpperCase()+e.slice(1)),ns=Un(e=>e?`on${uo(e)}`:""),Ct=(e,t)=>!Object.is(e,t),Mn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},bs=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},_s=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Ki=e=>{const t=pe(e)?Number(e):NaN;return isNaN(t)?e:t};let or;const Kn=()=>or||(or=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Hs(e){if(V(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=pe(s)?Yi(s):Hs(s);if(r)for(const o in r)t[o]=r[o]}return t}else if(pe(e)||he(e))return e}const Gi=/;(?![^(]*\))/g,Wi=/:([^]+)/,qi=/\/\*[^]*?\*\//g;function Yi(e){const t={};return e.replace(qi,"").split(Gi).forEach(n=>{if(n){const s=n.split(Wi);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Tt(e){let t="";if(pe(e))t=e;else if(V(e))for(let n=0;n<e.length;n++){const s=Tt(e[n]);s&&(t+=s+" ")}else if(he(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Ji="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Qi=Ls(Ji);function fo(e){return!!e||e===""}const ho=e=>!!(e&&e.__v_isRef===!0),ve=e=>pe(e)?e:e==null?"":V(e)||he(e)&&(e.toString===ao||!B(e.toString))?ho(e)?ve(e.value):JSON.stringify(e,po,2):String(e),po=(e,t)=>ho(t)?po(e,t.value):Bt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],o)=>(n[ss(s,o)+" =>"]=r,n),{})}:io(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>ss(n))}:ht(t)?ss(t):he(t)&&!V(t)&&!co(t)?String(t):t,ss=(e,t="")=>{var n;return ht(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ie;class go{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Ie,!t&&Ie&&(this.index=(Ie.scopes||(Ie.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Ie;try{return Ie=this,t()}finally{Ie=n}}}on(){++this._on===1&&(this.prevScope=Ie,Ie=this)}off(){this._on>0&&--this._on===0&&(Ie=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function Xi(e){return new go(e)}function Zi(){return Ie}let de;const rs=new WeakSet;class mo{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Ie&&Ie.active&&Ie.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,rs.has(this)&&(rs.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||yo(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,ir(this),bo(this);const t=de,n=Ue;de=this,Ue=!0;try{return this.fn()}finally{_o(this),de=t,Ue=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)js(t);this.deps=this.depsTail=void 0,ir(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?rs.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){ws(this)&&this.run()}get dirty(){return ws(this)}}let vo=0,on,ln;function yo(e,t=!1){if(e.flags|=8,t){e.next=ln,ln=e;return}e.next=on,on=e}function Fs(){vo++}function Vs(){if(--vo>0)return;if(ln){let t=ln;for(ln=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;on;){let t=on;for(on=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function bo(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function _o(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),js(s),el(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function ws(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(wo(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function wo(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===pn)||(e.globalVersion=pn,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!ws(e))))return;e.flags|=2;const t=e.dep,n=de,s=Ue;de=e,Ue=!0;try{bo(e);const r=e.fn(e._value);(t.version===0||Ct(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{de=n,Ue=s,_o(e),e.flags&=-3}}function js(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)js(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function el(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Ue=!0;const So=[];function ft(){So.push(Ue),Ue=!1}function dt(){const e=So.pop();Ue=e===void 0?!0:e}function ir(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=de;de=void 0;try{t()}finally{de=n}}}let pn=0;class tl{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Bs{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!de||!Ue||de===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==de)n=this.activeLink=new tl(de,this),de.deps?(n.prevDep=de.depsTail,de.depsTail.nextDep=n,de.depsTail=n):de.deps=de.depsTail=n,Eo(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=de.depsTail,n.nextDep=void 0,de.depsTail.nextDep=n,de.depsTail=n,de.deps===n&&(de.deps=s)}return n}trigger(t){this.version++,pn++,this.notify(t)}notify(t){Fs();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Vs()}}}function Eo(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)Eo(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const Ss=new WeakMap,Lt=Symbol(""),Es=Symbol(""),gn=Symbol("");function xe(e,t,n){if(Ue&&de){let s=Ss.get(e);s||Ss.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new Bs),r.map=s,r.key=n),r.track()}}function at(e,t,n,s,r,o){const i=Ss.get(e);if(!i){pn++;return}const l=a=>{a&&a.trigger()};if(Fs(),t==="clear")i.forEach(l);else{const a=V(e),f=a&&zs(n);if(a&&n==="length"){const c=Number(s);i.forEach((d,m)=>{(m==="length"||m===gn||!ht(m)&&m>=c)&&l(d)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),f&&l(i.get(gn)),t){case"add":a?f&&l(i.get("length")):(l(i.get(Lt)),Bt(e)&&l(i.get(Es)));break;case"delete":a||(l(i.get(Lt)),Bt(e)&&l(i.get(Es)));break;case"set":Bt(e)&&l(i.get(Lt));break}}Vs()}function zt(e){const t=Q(e);return t===e?t:(xe(t,"iterate",gn),Ve(e)?t:t.map(we))}function Gn(e){return xe(e=Q(e),"iterate",gn),e}const nl={__proto__:null,[Symbol.iterator](){return os(this,Symbol.iterator,we)},concat(...e){return zt(this).concat(...e.map(t=>V(t)?zt(t):t))},entries(){return os(this,"entries",e=>(e[1]=we(e[1]),e))},every(e,t){return ot(this,"every",e,t,void 0,arguments)},filter(e,t){return ot(this,"filter",e,t,n=>n.map(we),arguments)},find(e,t){return ot(this,"find",e,t,we,arguments)},findIndex(e,t){return ot(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return ot(this,"findLast",e,t,we,arguments)},findLastIndex(e,t){return ot(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return ot(this,"forEach",e,t,void 0,arguments)},includes(...e){return is(this,"includes",e)},indexOf(...e){return is(this,"indexOf",e)},join(e){return zt(this).join(e)},lastIndexOf(...e){return is(this,"lastIndexOf",e)},map(e,t){return ot(this,"map",e,t,void 0,arguments)},pop(){return Zt(this,"pop")},push(...e){return Zt(this,"push",e)},reduce(e,...t){return lr(this,"reduce",e,t)},reduceRight(e,...t){return lr(this,"reduceRight",e,t)},shift(){return Zt(this,"shift")},some(e,t){return ot(this,"some",e,t,void 0,arguments)},splice(...e){return Zt(this,"splice",e)},toReversed(){return zt(this).toReversed()},toSorted(e){return zt(this).toSorted(e)},toSpliced(...e){return zt(this).toSpliced(...e)},unshift(...e){return Zt(this,"unshift",e)},values(){return os(this,"values",we)}};function os(e,t,n){const s=Gn(e),r=s[t]();return s!==e&&!Ve(e)&&(r._next=r.next,r.next=()=>{const o=r._next();return o.value&&(o.value=n(o.value)),o}),r}const sl=Array.prototype;function ot(e,t,n,s,r,o){const i=Gn(e),l=i!==e&&!Ve(e),a=i[t];if(a!==sl[t]){const d=a.apply(e,o);return l?we(d):d}let f=n;i!==e&&(l?f=function(d,m){return n.call(this,we(d),m,e)}:n.length>2&&(f=function(d,m){return n.call(this,d,m,e)}));const c=a.call(i,f,s);return l&&r?r(c):c}function lr(e,t,n,s){const r=Gn(e);let o=n;return r!==e&&(Ve(e)?n.length>3&&(o=function(i,l,a){return n.call(this,i,l,a,e)}):o=function(i,l,a){return n.call(this,i,we(l),a,e)}),r[t](o,...s)}function is(e,t,n){const s=Q(e);xe(s,"iterate",gn);const r=s[t](...n);return(r===-1||r===!1)&&Gs(n[0])?(n[0]=Q(n[0]),s[t](...n)):r}function Zt(e,t,n=[]){ft(),Fs();const s=Q(e)[t].apply(e,n);return Vs(),dt(),s}const rl=Ls("__proto__,__v_isRef,__isVue"),Co=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(ht));function ol(e){ht(e)||(e=String(e));const t=Q(this);return xe(t,"has",e),t.hasOwnProperty(e)}class xo{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return o;if(n==="__v_raw")return s===(r?o?gl:Po:o?Ro:Ao).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const i=V(t);if(!r){let a;if(i&&(a=nl[n]))return a;if(n==="hasOwnProperty")return ol}const l=Reflect.get(t,n,Re(t)?t:s);return(ht(n)?Co.has(n):rl(n))||(r||xe(t,"get",n),o)?l:Re(l)?i&&zs(n)?l:l.value:he(l)?r?Oo(l):Wn(l):l}}class To extends xo{constructor(t=!1){super(!1,t)}set(t,n,s,r){let o=t[n];if(!this._isShallow){const a=At(o);if(!Ve(s)&&!At(s)&&(o=Q(o),s=Q(s)),!V(t)&&Re(o)&&!Re(s))return a?!1:(o.value=s,!0)}const i=V(t)&&zs(n)?Number(n)<t.length:se(t,n),l=Reflect.set(t,n,s,Re(t)?t:r);return t===Q(r)&&(i?Ct(s,o)&&at(t,"set",n,s):at(t,"add",n,s)),l}deleteProperty(t,n){const s=se(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&at(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!ht(n)||!Co.has(n))&&xe(t,"has",n),s}ownKeys(t){return xe(t,"iterate",V(t)?"length":Lt),Reflect.ownKeys(t)}}class il extends xo{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const ll=new To,al=new il,cl=new To(!0);const Cs=e=>e,Tn=e=>Reflect.getPrototypeOf(e);function ul(e,t,n){return function(...s){const r=this.__v_raw,o=Q(r),i=Bt(o),l=e==="entries"||e===Symbol.iterator&&i,a=e==="keys"&&i,f=r[e](...s),c=n?Cs:t?Ln:we;return!t&&xe(o,"iterate",a?Es:Lt),{next(){const{value:d,done:m}=f.next();return m?{value:d,done:m}:{value:l?[c(d[0]),c(d[1])]:c(d),done:m}},[Symbol.iterator](){return this}}}}function An(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function fl(e,t){const n={get(r){const o=this.__v_raw,i=Q(o),l=Q(r);e||(Ct(r,l)&&xe(i,"get",r),xe(i,"get",l));const{has:a}=Tn(i),f=t?Cs:e?Ln:we;if(a.call(i,r))return f(o.get(r));if(a.call(i,l))return f(o.get(l));o!==i&&o.get(r)},get size(){const r=this.__v_raw;return!e&&xe(Q(r),"iterate",Lt),Reflect.get(r,"size",r)},has(r){const o=this.__v_raw,i=Q(o),l=Q(r);return e||(Ct(r,l)&&xe(i,"has",r),xe(i,"has",l)),r===l?o.has(r):o.has(r)||o.has(l)},forEach(r,o){const i=this,l=i.__v_raw,a=Q(l),f=t?Cs:e?Ln:we;return!e&&xe(a,"iterate",Lt),l.forEach((c,d)=>r.call(o,f(c),f(d),i))}};return ye(n,e?{add:An("add"),set:An("set"),delete:An("delete"),clear:An("clear")}:{add(r){!t&&!Ve(r)&&!At(r)&&(r=Q(r));const o=Q(this);return Tn(o).has.call(o,r)||(o.add(r),at(o,"add",r,r)),this},set(r,o){!t&&!Ve(o)&&!At(o)&&(o=Q(o));const i=Q(this),{has:l,get:a}=Tn(i);let f=l.call(i,r);f||(r=Q(r),f=l.call(i,r));const c=a.call(i,r);return i.set(r,o),f?Ct(o,c)&&at(i,"set",r,o):at(i,"add",r,o),this},delete(r){const o=Q(this),{has:i,get:l}=Tn(o);let a=i.call(o,r);a||(r=Q(r),a=i.call(o,r)),l&&l.call(o,r);const f=o.delete(r);return a&&at(o,"delete",r,void 0),f},clear(){const r=Q(this),o=r.size!==0,i=r.clear();return o&&at(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=ul(r,e,t)}),n}function Us(e,t){const n=fl(e,t);return(s,r,o)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(se(n,r)&&r in s?n:s,r,o)}const dl={get:Us(!1,!1)},hl={get:Us(!1,!0)},pl={get:Us(!0,!1)};const Ao=new WeakMap,Ro=new WeakMap,Po=new WeakMap,gl=new WeakMap;function ml(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function vl(e){return e.__v_skip||!Object.isExtensible(e)?0:ml(ji(e))}function Wn(e){return At(e)?e:Ks(e,!1,ll,dl,Ao)}function Mo(e){return Ks(e,!1,cl,hl,Ro)}function Oo(e){return Ks(e,!0,al,pl,Po)}function Ks(e,t,n,s,r){if(!he(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=vl(e);if(o===0)return e;const i=r.get(e);if(i)return i;const l=new Proxy(e,o===2?s:n);return r.set(e,l),l}function Ut(e){return At(e)?Ut(e.__v_raw):!!(e&&e.__v_isReactive)}function At(e){return!!(e&&e.__v_isReadonly)}function Ve(e){return!!(e&&e.__v_isShallow)}function Gs(e){return e?!!e.__v_raw:!1}function Q(e){const t=e&&e.__v_raw;return t?Q(t):e}function ko(e){return!se(e,"__v_skip")&&Object.isExtensible(e)&&bs(e,"__v_skip",!0),e}const we=e=>he(e)?Wn(e):e,Ln=e=>he(e)?Oo(e):e;function Re(e){return e?e.__v_isRef===!0:!1}function ae(e){return $o(e,!1)}function yl(e){return $o(e,!0)}function $o(e,t){return Re(e)?e:new bl(e,t)}class bl{constructor(t,n){this.dep=new Bs,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:Q(t),this._value=n?t:we(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||Ve(t)||At(t);t=s?t:Q(t),Ct(t,n)&&(this._rawValue=t,this._value=s?t:we(t),this.dep.trigger())}}function Kt(e){return Re(e)?e.value:e}const _l={get:(e,t,n)=>t==="__v_raw"?e:Kt(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return Re(r)&&!Re(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function Io(e){return Ut(e)?e:new Proxy(e,_l)}class wl{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Bs(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=pn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&de!==this)return yo(this,!0),!0}get value(){const t=this.dep.track();return wo(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Sl(e,t,n=!1){let s,r;return B(e)?s=e:(s=e.get,r=e.set),new wl(s,r,n)}const Rn={},Nn=new WeakMap;let $t;function El(e,t=!1,n=$t){if(n){let s=Nn.get(n);s||Nn.set(n,s=[]),s.push(e)}}function Cl(e,t,n=ue){const{immediate:s,deep:r,once:o,scheduler:i,augmentJob:l,call:a}=n,f=L=>r?L:Ve(L)||r===!1||r===0?ct(L,1):ct(L);let c,d,m,y,A=!1,x=!1;if(Re(e)?(d=()=>e.value,A=Ve(e)):Ut(e)?(d=()=>f(e),A=!0):V(e)?(x=!0,A=e.some(L=>Ut(L)||Ve(L)),d=()=>e.map(L=>{if(Re(L))return L.value;if(Ut(L))return f(L);if(B(L))return a?a(L,2):L()})):B(e)?t?d=a?()=>a(e,2):e:d=()=>{if(m){ft();try{m()}finally{dt()}}const L=$t;$t=c;try{return a?a(e,3,[y]):e(y)}finally{$t=L}}:d=nt,t&&r){const L=d,U=r===!0?1/0:r;d=()=>ct(L(),U)}const O=Zi(),R=()=>{c.stop(),O&&O.active&&Ds(O.effects,c)};if(o&&t){const L=t;t=(...U)=>{L(...U),R()}}let k=x?new Array(e.length).fill(Rn):Rn;const D=L=>{if(!(!(c.flags&1)||!c.dirty&&!L))if(t){const U=c.run();if(r||A||(x?U.some((re,Z)=>Ct(re,k[Z])):Ct(U,k))){m&&m();const re=$t;$t=c;try{const Z=[U,k===Rn?void 0:x&&k[0]===Rn?[]:k,y];k=U,a?a(t,3,Z):t(...Z)}finally{$t=re}}}else c.run()};return l&&l(D),c=new mo(d),c.scheduler=i?()=>i(D,!1):D,y=L=>El(L,!1,c),m=c.onStop=()=>{const L=Nn.get(c);if(L){if(a)a(L,4);else for(const U of L)U();Nn.delete(c)}},t?s?D(!0):k=c.run():i?i(D.bind(null,!0),!0):c.run(),R.pause=c.pause.bind(c),R.resume=c.resume.bind(c),R.stop=R,R}function ct(e,t=1/0,n){if(t<=0||!he(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,Re(e))ct(e.value,t,n);else if(V(e))for(let s=0;s<e.length;s++)ct(e[s],t,n);else if(io(e)||Bt(e))e.forEach(s=>{ct(s,t,n)});else if(co(e)){for(const s in e)ct(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&ct(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Cn(e,t,n,s){try{return s?e(...s):e()}catch(r){qn(r,t,n)}}function Ke(e,t,n,s){if(B(e)){const r=Cn(e,t,n,s);return r&&lo(r)&&r.catch(o=>{qn(o,t,n)}),r}if(V(e)){const r=[];for(let o=0;o<e.length;o++)r.push(Ke(e[o],t,n,s));return r}}function qn(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||ue;if(t){let l=t.parent;const a=t.proxy,f=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const c=l.ec;if(c){for(let d=0;d<c.length;d++)if(c[d](e,a,f)===!1)return}l=l.parent}if(o){ft(),Cn(o,null,10,[e,a,f]),dt();return}}xl(e,n,r,s,i)}function xl(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const Me=[];let Ze=-1;const Gt=[];let bt=null,Ht=0;const Lo=Promise.resolve();let Dn=null;function No(e){const t=Dn||Lo;return e?t.then(this?e.bind(this):e):t}function Tl(e){let t=Ze+1,n=Me.length;for(;t<n;){const s=t+n>>>1,r=Me[s],o=mn(r);o<e||o===e&&r.flags&2?t=s+1:n=s}return t}function Ws(e){if(!(e.flags&1)){const t=mn(e),n=Me[Me.length-1];!n||!(e.flags&2)&&t>=mn(n)?Me.push(e):Me.splice(Tl(t),0,e),e.flags|=1,Do()}}function Do(){Dn||(Dn=Lo.then(Ho))}function Al(e){V(e)?Gt.push(...e):bt&&e.id===-1?bt.splice(Ht+1,0,e):e.flags&1||(Gt.push(e),e.flags|=1),Do()}function ar(e,t,n=Ze+1){for(;n<Me.length;n++){const s=Me[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;Me.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function zo(e){if(Gt.length){const t=[...new Set(Gt)].sort((n,s)=>mn(n)-mn(s));if(Gt.length=0,bt){bt.push(...t);return}for(bt=t,Ht=0;Ht<bt.length;Ht++){const n=bt[Ht];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}bt=null,Ht=0}}const mn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Ho(e){try{for(Ze=0;Ze<Me.length;Ze++){const t=Me[Ze];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Cn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Ze<Me.length;Ze++){const t=Me[Ze];t&&(t.flags&=-2)}Ze=-1,Me.length=0,zo(),Dn=null,(Me.length||Gt.length)&&Ho()}}let Se=null,Fo=null;function zn(e){const t=Se;return Se=e,Fo=e&&e.type.__scopeId||null,t}function _e(e,t=Se,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&yr(-1);const o=zn(t);let i;try{i=e(...r)}finally{zn(o),s._d&&yr(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function Rl(e,t){if(Se===null)return e;const n=es(Se),s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[o,i,l,a=ue]=t[r];o&&(B(o)&&(o={mounted:o,updated:o}),o.deep&&ct(i),s.push({dir:o,instance:n,value:i,oldValue:void 0,arg:l,modifiers:a}))}return e}function Mt(e,t,n,s){const r=e.dirs,o=t&&t.dirs;for(let i=0;i<r.length;i++){const l=r[i];o&&(l.oldValue=o[i].value);let a=l.dir[s];a&&(ft(),Ke(a,n,8,[e.el,l,e,t]),dt())}}const Pl=Symbol("_vte"),Vo=e=>e.__isTeleport,_t=Symbol("_leaveCb"),Pn=Symbol("_enterCb");function jo(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Qn(()=>{e.isMounted=!0}),Jo(()=>{e.isUnmounting=!0}),e}const Fe=[Function,Array],Bo={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Fe,onEnter:Fe,onAfterEnter:Fe,onEnterCancelled:Fe,onBeforeLeave:Fe,onLeave:Fe,onAfterLeave:Fe,onLeaveCancelled:Fe,onBeforeAppear:Fe,onAppear:Fe,onAfterAppear:Fe,onAppearCancelled:Fe},Uo=e=>{const t=e.subTree;return t.component?Uo(t.component):t},Ml={name:"BaseTransition",props:Bo,setup(e,{slots:t}){const n=vi(),s=jo();return()=>{const r=t.default&&qs(t.default(),!0);if(!r||!r.length)return;const o=Ko(r),i=Q(e),{mode:l}=i;if(s.isLeaving)return ls(o);const a=cr(o);if(!a)return ls(o);let f=vn(a,i,s,n,d=>f=d);a.type!==Te&&Nt(a,f);let c=n.subTree&&cr(n.subTree);if(c&&c.type!==Te&&!It(a,c)&&Uo(n).type!==Te){let d=vn(c,i,s,n);if(Nt(c,d),l==="out-in"&&a.type!==Te)return s.isLeaving=!0,d.afterLeave=()=>{s.isLeaving=!1,n.job.flags&8||n.update(),delete d.afterLeave,c=void 0},ls(o);l==="in-out"&&a.type!==Te?d.delayLeave=(m,y,A)=>{const x=Go(s,c);x[String(c.key)]=c,m[_t]=()=>{y(),m[_t]=void 0,delete f.delayedLeave,c=void 0},f.delayedLeave=()=>{A(),delete f.delayedLeave,c=void 0}}:c=void 0}else c&&(c=void 0);return o}}};function Ko(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==Te){t=n;break}}return t}const Ol=Ml;function Go(e,t){const{leavingVNodes:n}=e;let s=n.get(t.type);return s||(s=Object.create(null),n.set(t.type,s)),s}function vn(e,t,n,s,r){const{appear:o,mode:i,persisted:l=!1,onBeforeEnter:a,onEnter:f,onAfterEnter:c,onEnterCancelled:d,onBeforeLeave:m,onLeave:y,onAfterLeave:A,onLeaveCancelled:x,onBeforeAppear:O,onAppear:R,onAfterAppear:k,onAppearCancelled:D}=t,L=String(e.key),U=Go(n,e),re=(j,q)=>{j&&Ke(j,s,9,q)},Z=(j,q)=>{const le=q[1];re(j,q),V(j)?j.every(N=>N.length<=1)&&le():j.length<=1&&le()},be={mode:i,persisted:l,beforeEnter(j){let q=a;if(!n.isMounted)if(o)q=O||a;else return;j[_t]&&j[_t](!0);const le=U[L];le&&It(e,le)&&le.el[_t]&&le.el[_t](),re(q,[j])},enter(j){let q=f,le=c,N=d;if(!n.isMounted)if(o)q=R||f,le=k||c,N=D||d;else return;let G=!1;const ge=j[Pn]=Ee=>{G||(G=!0,Ee?re(N,[j]):re(le,[j]),be.delayedLeave&&be.delayedLeave(),j[Pn]=void 0)};q?Z(q,[j,ge]):ge()},leave(j,q){const le=String(e.key);if(j[Pn]&&j[Pn](!0),n.isUnmounting)return q();re(m,[j]);let N=!1;const G=j[_t]=ge=>{N||(N=!0,q(),ge?re(x,[j]):re(A,[j]),j[_t]=void 0,U[le]===e&&delete U[le])};U[le]=e,y?Z(y,[j,G]):G()},clone(j){const q=vn(j,t,n,s,r);return r&&r(q),q}};return be}function ls(e){if(Yn(e))return e=Rt(e),e.children=null,e}function cr(e){if(!Yn(e))return Vo(e.type)&&e.children?Ko(e.children):e;if(e.component)return e.component.subTree;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&B(n.default))return n.default()}}function Nt(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Nt(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function qs(e,t=!1,n){let s=[],r=0;for(let o=0;o<e.length;o++){let i=e[o];const l=n==null?i.key:String(n)+String(i.key!=null?i.key:o);i.type===me?(i.patchFlag&128&&r++,s=s.concat(qs(i.children,t,l))):(t||i.type!==Te)&&s.push(l!=null?Rt(i,{key:l}):i)}if(r>1)for(let o=0;o<s.length;o++)s[o].patchFlag=-2;return s}/*! #__NO_SIDE_EFFECTS__ */function Dt(e,t){return B(e)?ye({name:e.name},t,{setup:e}):e}function Wo(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function an(e,t,n,s,r=!1){if(V(e)){e.forEach((A,x)=>an(A,t&&(V(t)?t[x]:t),n,s,r));return}if(Wt(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&an(e,t,n,s.component.subTree);return}const o=s.shapeFlag&4?es(s.component):s.el,i=r?null:o,{i:l,r:a}=e,f=t&&t.r,c=l.refs===ue?l.refs={}:l.refs,d=l.setupState,m=Q(d),y=d===ue?()=>!1:A=>se(m,A);if(f!=null&&f!==a&&(pe(f)?(c[f]=null,y(f)&&(d[f]=null)):Re(f)&&(f.value=null)),B(a))Cn(a,l,12,[i,c]);else{const A=pe(a),x=Re(a);if(A||x){const O=()=>{if(e.f){const R=A?y(a)?d[a]:c[a]:a.value;r?V(R)&&Ds(R,o):V(R)?R.includes(o)||R.push(o):A?(c[a]=[o],y(a)&&(d[a]=c[a])):(a.value=[o],e.k&&(c[e.k]=a.value))}else A?(c[a]=i,y(a)&&(d[a]=i)):x&&(a.value=i,e.k&&(c[e.k]=i))};i?(O.id=-1,Ne(O,n)):O()}}}Kn().requestIdleCallback;Kn().cancelIdleCallback;const Wt=e=>!!e.type.__asyncLoader,Yn=e=>e.type.__isKeepAlive;function kl(e,t){qo(e,"a",t)}function $l(e,t){qo(e,"da",t)}function qo(e,t,n=Ae){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(Jn(t,s,n),n){let r=n.parent;for(;r&&r.parent;)Yn(r.parent.vnode)&&Il(s,t,n,r),r=r.parent}}function Il(e,t,n,s){const r=Jn(t,e,s,!0);Ys(()=>{Ds(s[t],r)},n)}function Jn(e,t,n=Ae,s=!1){if(n){const r=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{ft();const l=xn(n),a=Ke(t,n,e,i);return l(),dt(),a});return s?r.unshift(o):r.push(o),o}}const pt=e=>(t,n=Ae)=>{(!wn||e==="sp")&&Jn(e,(...s)=>t(...s),n)},Ll=pt("bm"),Qn=pt("m"),Nl=pt("bu"),Yo=pt("u"),Jo=pt("bum"),Ys=pt("um"),Dl=pt("sp"),zl=pt("rtg"),Hl=pt("rtc");function Fl(e,t=Ae){Jn("ec",e,t)}const Vl=Symbol.for("v-ndc");function cn(e,t,n,s){let r;const o=n,i=V(e);if(i||pe(e)){const l=i&&Ut(e);let a=!1,f=!1;l&&(a=!Ve(e),f=At(e),e=Gn(e)),r=new Array(e.length);for(let c=0,d=e.length;c<d;c++)r[c]=t(a?f?Ln(we(e[c])):we(e[c]):e[c],c,void 0,o)}else if(typeof e=="number"){r=new Array(e);for(let l=0;l<e;l++)r[l]=t(l+1,l,void 0,o)}else if(he(e))if(e[Symbol.iterator])r=Array.from(e,(l,a)=>t(l,a,void 0,o));else{const l=Object.keys(e);r=new Array(l.length);for(let a=0,f=l.length;a<f;a++){const c=l[a];r[a]=t(e[c],c,a,o)}}else r=[];return r}function as(e,t,n={},s,r){if(Se.ce||Se.parent&&Wt(Se.parent)&&Se.parent.ce)return t!=="default"&&(n.name=t),K(),bn(me,null,[ce("slot",n,s)],64);let o=e[t];o&&o._c&&(o._d=!1),K();const i=o&&Qo(o(n)),l=n.key||i&&i.key,a=bn(me,{key:(l&&!ht(l)?l:`_${t}`)+""},i||[],i&&e._===1?64:-2);return o&&o._c&&(o._d=!0),a}function Qo(e){return e.some(t=>_n(t)?!(t.type===Te||t.type===me&&!Qo(t.children)):!0)?e:null}const xs=e=>e?yi(e)?es(e):xs(e.parent):null,un=ye(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>xs(e.parent),$root:e=>xs(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Zo(e),$forceUpdate:e=>e.f||(e.f=()=>{Ws(e.update)}),$nextTick:e=>e.n||(e.n=No.bind(e.proxy)),$watch:e=>aa.bind(e)}),cs=(e,t)=>e!==ue&&!e.__isScriptSetup&&se(e,t),jl={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:o,accessCache:i,type:l,appContext:a}=e;let f;if(t[0]!=="$"){const y=i[t];if(y!==void 0)switch(y){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return o[t]}else{if(cs(s,t))return i[t]=1,s[t];if(r!==ue&&se(r,t))return i[t]=2,r[t];if((f=e.propsOptions[0])&&se(f,t))return i[t]=3,o[t];if(n!==ue&&se(n,t))return i[t]=4,n[t];Ts&&(i[t]=0)}}const c=un[t];let d,m;if(c)return t==="$attrs"&&xe(e.attrs,"get",""),c(e);if((d=l.__cssModules)&&(d=d[t]))return d;if(n!==ue&&se(n,t))return i[t]=4,n[t];if(m=a.config.globalProperties,se(m,t))return m[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:o}=e;return cs(r,t)?(r[t]=n,!0):s!==ue&&se(s,t)?(s[t]=n,!0):se(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:o}},i){let l;return!!n[i]||e!==ue&&se(e,i)||cs(t,i)||(l=o[0])&&se(l,i)||se(s,i)||se(un,i)||se(r.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:se(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function ur(e){return V(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Ts=!0;function Bl(e){const t=Zo(e),n=e.proxy,s=e.ctx;Ts=!1,t.beforeCreate&&fr(t.beforeCreate,e,"bc");const{data:r,computed:o,methods:i,watch:l,provide:a,inject:f,created:c,beforeMount:d,mounted:m,beforeUpdate:y,updated:A,activated:x,deactivated:O,beforeDestroy:R,beforeUnmount:k,destroyed:D,unmounted:L,render:U,renderTracked:re,renderTriggered:Z,errorCaptured:be,serverPrefetch:j,expose:q,inheritAttrs:le,components:N,directives:G,filters:ge}=t;if(f&&Ul(f,s,null),i)for(const te in i){const Y=i[te];B(Y)&&(s[te]=Y.bind(n))}if(r){const te=r.call(n,n);he(te)&&(e.data=Wn(te))}if(Ts=!0,o)for(const te in o){const Y=o[te],ze=B(Y)?Y.bind(n,n):B(Y.get)?Y.get.bind(n,n):nt,We=!B(Y)&&B(Y.set)?Y.set.bind(n):nt,He=Be({get:ze,set:We});Object.defineProperty(s,te,{enumerable:!0,configurable:!0,get:()=>He.value,set:Ce=>He.value=Ce})}if(l)for(const te in l)Xo(l[te],s,n,te);if(a){const te=B(a)?a.call(n):a;Reflect.ownKeys(te).forEach(Y=>{On(Y,te[Y])})}c&&fr(c,e,"c");function ee(te,Y){V(Y)?Y.forEach(ze=>te(ze.bind(n))):Y&&te(Y.bind(n))}if(ee(Ll,d),ee(Qn,m),ee(Nl,y),ee(Yo,A),ee(kl,x),ee($l,O),ee(Fl,be),ee(Hl,re),ee(zl,Z),ee(Jo,k),ee(Ys,L),ee(Dl,j),V(q))if(q.length){const te=e.exposed||(e.exposed={});q.forEach(Y=>{Object.defineProperty(te,Y,{get:()=>n[Y],set:ze=>n[Y]=ze})})}else e.exposed||(e.exposed={});U&&e.render===nt&&(e.render=U),le!=null&&(e.inheritAttrs=le),N&&(e.components=N),G&&(e.directives=G),j&&Wo(e)}function Ul(e,t,n=nt){V(e)&&(e=As(e));for(const s in e){const r=e[s];let o;he(r)?"default"in r?o=ut(r.from||s,r.default,!0):o=ut(r.from||s):o=ut(r),Re(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[s]=o}}function fr(e,t,n){Ke(V(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Xo(e,t,n,s){let r=s.includes(".")?di(n,s):()=>n[s];if(pe(e)){const o=t[e];B(o)&&kn(r,o)}else if(B(e))kn(r,e.bind(n));else if(he(e))if(V(e))e.forEach(o=>Xo(o,t,n,s));else{const o=B(e.handler)?e.handler.bind(n):t[e.handler];B(o)&&kn(r,o,e)}}function Zo(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,l=o.get(t);let a;return l?a=l:!r.length&&!n&&!s?a=t:(a={},r.length&&r.forEach(f=>Hn(a,f,i,!0)),Hn(a,t,i)),he(t)&&o.set(t,a),a}function Hn(e,t,n,s=!1){const{mixins:r,extends:o}=t;o&&Hn(e,o,n,!0),r&&r.forEach(i=>Hn(e,i,n,!0));for(const i in t)if(!(s&&i==="expose")){const l=Kl[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const Kl={data:dr,props:hr,emits:hr,methods:sn,computed:sn,beforeCreate:Pe,created:Pe,beforeMount:Pe,mounted:Pe,beforeUpdate:Pe,updated:Pe,beforeDestroy:Pe,beforeUnmount:Pe,destroyed:Pe,unmounted:Pe,activated:Pe,deactivated:Pe,errorCaptured:Pe,serverPrefetch:Pe,components:sn,directives:sn,watch:Wl,provide:dr,inject:Gl};function dr(e,t){return t?e?function(){return ye(B(e)?e.call(this,this):e,B(t)?t.call(this,this):t)}:t:e}function Gl(e,t){return sn(As(e),As(t))}function As(e){if(V(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Pe(e,t){return e?[...new Set([].concat(e,t))]:t}function sn(e,t){return e?ye(Object.create(null),e,t):t}function hr(e,t){return e?V(e)&&V(t)?[...new Set([...e,...t])]:ye(Object.create(null),ur(e),ur(t??{})):t}function Wl(e,t){if(!e)return t;if(!t)return e;const n=ye(Object.create(null),e);for(const s in t)n[s]=Pe(e[s],t[s]);return n}function ei(){return{app:null,config:{isNativeTag:Fi,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let ql=0;function Yl(e,t){return function(s,r=null){B(s)||(s=ye({},s)),r!=null&&!he(r)&&(r=null);const o=ei(),i=new WeakSet,l=[];let a=!1;const f=o.app={_uid:ql++,_component:s,_props:r,_container:null,_context:o,_instance:null,version:Ra,get config(){return o.config},set config(c){},use(c,...d){return i.has(c)||(c&&B(c.install)?(i.add(c),c.install(f,...d)):B(c)&&(i.add(c),c(f,...d))),f},mixin(c){return o.mixins.includes(c)||o.mixins.push(c),f},component(c,d){return d?(o.components[c]=d,f):o.components[c]},directive(c,d){return d?(o.directives[c]=d,f):o.directives[c]},mount(c,d,m){if(!a){const y=f._ceVNode||ce(s,r);return y.appContext=o,m===!0?m="svg":m===!1&&(m=void 0),e(y,c,m),a=!0,f._container=c,c.__vue_app__=f,es(y.component)}},onUnmount(c){l.push(c)},unmount(){a&&(Ke(l,f._instance,16),e(null,f._container),delete f._container.__vue_app__)},provide(c,d){return o.provides[c]=d,f},runWithContext(c){const d=qt;qt=f;try{return c()}finally{qt=d}}};return f}}let qt=null;function On(e,t){if(Ae){let n=Ae.provides;const s=Ae.parent&&Ae.parent.provides;s===n&&(n=Ae.provides=Object.create(s)),n[e]=t}}function ut(e,t,n=!1){const s=Ae||Se;if(s||qt){let r=qt?qt._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&B(t)?t.call(s&&s.proxy):t}}const ti={},ni=()=>Object.create(ti),si=e=>Object.getPrototypeOf(e)===ti;function Jl(e,t,n,s=!1){const r={},o=ni();e.propsDefaults=Object.create(null),ri(e,t,r,o);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);n?e.props=s?r:Mo(r):e.type.props?e.props=r:e.props=o,e.attrs=o}function Ql(e,t,n,s){const{props:r,attrs:o,vnode:{patchFlag:i}}=e,l=Q(r),[a]=e.propsOptions;let f=!1;if((s||i>0)&&!(i&16)){if(i&8){const c=e.vnode.dynamicProps;for(let d=0;d<c.length;d++){let m=c[d];if(Xn(e.emitsOptions,m))continue;const y=t[m];if(a)if(se(o,m))y!==o[m]&&(o[m]=y,f=!0);else{const A=xt(m);r[A]=Rs(a,l,A,y,e,!1)}else y!==o[m]&&(o[m]=y,f=!0)}}}else{ri(e,t,r,o)&&(f=!0);let c;for(const d in l)(!t||!se(t,d)&&((c=Pt(d))===d||!se(t,c)))&&(a?n&&(n[d]!==void 0||n[c]!==void 0)&&(r[d]=Rs(a,l,d,void 0,e,!0)):delete r[d]);if(o!==l)for(const d in o)(!t||!se(t,d))&&(delete o[d],f=!0)}f&&at(e.attrs,"set","")}function ri(e,t,n,s){const[r,o]=e.propsOptions;let i=!1,l;if(t)for(let a in t){if(rn(a))continue;const f=t[a];let c;r&&se(r,c=xt(a))?!o||!o.includes(c)?n[c]=f:(l||(l={}))[c]=f:Xn(e.emitsOptions,a)||(!(a in s)||f!==s[a])&&(s[a]=f,i=!0)}if(o){const a=Q(n),f=l||ue;for(let c=0;c<o.length;c++){const d=o[c];n[d]=Rs(r,a,d,f[d],e,!se(f,d))}}return i}function Rs(e,t,n,s,r,o){const i=e[n];if(i!=null){const l=se(i,"default");if(l&&s===void 0){const a=i.default;if(i.type!==Function&&!i.skipFactory&&B(a)){const{propsDefaults:f}=r;if(n in f)s=f[n];else{const c=xn(r);s=f[n]=a.call(null,t),c()}}else s=a;r.ce&&r.ce._setProp(n,s)}i[0]&&(o&&!l?s=!1:i[1]&&(s===""||s===Pt(n))&&(s=!0))}return s}const Xl=new WeakMap;function oi(e,t,n=!1){const s=n?Xl:t.propsCache,r=s.get(e);if(r)return r;const o=e.props,i={},l=[];let a=!1;if(!B(e)){const c=d=>{a=!0;const[m,y]=oi(d,t,!0);ye(i,m),y&&l.push(...y)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!o&&!a)return he(e)&&s.set(e,jt),jt;if(V(o))for(let c=0;c<o.length;c++){const d=xt(o[c]);pr(d)&&(i[d]=ue)}else if(o)for(const c in o){const d=xt(c);if(pr(d)){const m=o[c],y=i[d]=V(m)||B(m)?{type:m}:ye({},m),A=y.type;let x=!1,O=!0;if(V(A))for(let R=0;R<A.length;++R){const k=A[R],D=B(k)&&k.name;if(D==="Boolean"){x=!0;break}else D==="String"&&(O=!1)}else x=B(A)&&A.name==="Boolean";y[0]=x,y[1]=O,(x||se(y,"default"))&&l.push(d)}}const f=[i,l];return he(e)&&s.set(e,f),f}function pr(e){return e[0]!=="$"&&!rn(e)}const Js=e=>e[0]==="_"||e==="$stable",Qs=e=>V(e)?e.map(et):[et(e)],Zl=(e,t,n)=>{if(t._n)return t;const s=_e((...r)=>Qs(t(...r)),n);return s._c=!1,s},ii=(e,t,n)=>{const s=e._ctx;for(const r in e){if(Js(r))continue;const o=e[r];if(B(o))t[r]=Zl(r,o,s);else if(o!=null){const i=Qs(o);t[r]=()=>i}}},li=(e,t)=>{const n=Qs(t);e.slots.default=()=>n},ai=(e,t,n)=>{for(const s in t)(n||!Js(s))&&(e[s]=t[s])},ea=(e,t,n)=>{const s=e.slots=ni();if(e.vnode.shapeFlag&32){const r=t.__;r&&bs(s,"__",r,!0);const o=t._;o?(ai(s,t,n),n&&bs(s,"_",o,!0)):ii(t,s)}else t&&li(e,t)},ta=(e,t,n)=>{const{vnode:s,slots:r}=e;let o=!0,i=ue;if(s.shapeFlag&32){const l=t._;l?n&&l===1?o=!1:ai(r,t,n):(o=!t.$stable,ii(t,r)),i=t}else t&&(li(e,t),i={default:1});if(o)for(const l in r)!Js(l)&&i[l]==null&&delete r[l]},Ne=ga;function na(e){return sa(e)}function sa(e,t){const n=Kn();n.__VUE__=!0;const{insert:s,remove:r,patchProp:o,createElement:i,createText:l,createComment:a,setText:f,setElementText:c,parentNode:d,nextSibling:m,setScopeId:y=nt,insertStaticContent:A}=e,x=(u,h,v,w=null,g=null,p=null,b=void 0,S=null,C=!!h.dynamicChildren)=>{if(u===h)return;u&&!It(u,h)&&(w=_(u),Ce(u,g,p,!0),u=null),h.patchFlag===-2&&(C=!1,h.dynamicChildren=null);const{type:E,ref:I,shapeFlag:M}=h;switch(E){case Zn:O(u,h,v,w);break;case Te:R(u,h,v,w);break;case fs:u==null&&k(h,v,w,b);break;case me:N(u,h,v,w,g,p,b,S,C);break;default:M&1?U(u,h,v,w,g,p,b,S,C):M&6?G(u,h,v,w,g,p,b,S,C):(M&64||M&128)&&E.process(u,h,v,w,g,p,b,S,C,z)}I!=null&&g?an(I,u&&u.ref,p,h||u,!h):I==null&&u&&u.ref!=null&&an(u.ref,null,p,u,!0)},O=(u,h,v,w)=>{if(u==null)s(h.el=l(h.children),v,w);else{const g=h.el=u.el;h.children!==u.children&&f(g,h.children)}},R=(u,h,v,w)=>{u==null?s(h.el=a(h.children||""),v,w):h.el=u.el},k=(u,h,v,w)=>{[u.el,u.anchor]=A(u.children,h,v,w,u.el,u.anchor)},D=({el:u,anchor:h},v,w)=>{let g;for(;u&&u!==h;)g=m(u),s(u,v,w),u=g;s(h,v,w)},L=({el:u,anchor:h})=>{let v;for(;u&&u!==h;)v=m(u),r(u),u=v;r(h)},U=(u,h,v,w,g,p,b,S,C)=>{h.type==="svg"?b="svg":h.type==="math"&&(b="mathml"),u==null?re(h,v,w,g,p,b,S,C):j(u,h,g,p,b,S,C)},re=(u,h,v,w,g,p,b,S)=>{let C,E;const{props:I,shapeFlag:M,transition:H,dirs:F}=u;if(C=u.el=i(u.type,p,I&&I.is,I),M&8?c(C,u.children):M&16&&be(u.children,C,null,w,g,us(u,p),b,S),F&&Mt(u,null,w,"created"),Z(C,u,u.scopeId,b,w),I){for(const fe in I)fe!=="value"&&!rn(fe)&&o(C,fe,null,I[fe],p,w);"value"in I&&o(C,"value",null,I.value,p),(E=I.onVnodeBeforeMount)&&Qe(E,w,u)}F&&Mt(u,null,w,"beforeMount");const W=ra(g,H);W&&H.beforeEnter(C),s(C,h,v),((E=I&&I.onVnodeMounted)||W||F)&&Ne(()=>{E&&Qe(E,w,u),W&&H.enter(C),F&&Mt(u,null,w,"mounted")},g)},Z=(u,h,v,w,g)=>{if(v&&y(u,v),w)for(let p=0;p<w.length;p++)y(u,w[p]);if(g){let p=g.subTree;if(h===p||pi(p.type)&&(p.ssContent===h||p.ssFallback===h)){const b=g.vnode;Z(u,b,b.scopeId,b.slotScopeIds,g.parent)}}},be=(u,h,v,w,g,p,b,S,C=0)=>{for(let E=C;E<u.length;E++){const I=u[E]=S?wt(u[E]):et(u[E]);x(null,I,h,v,w,g,p,b,S)}},j=(u,h,v,w,g,p,b)=>{const S=h.el=u.el;let{patchFlag:C,dynamicChildren:E,dirs:I}=h;C|=u.patchFlag&16;const M=u.props||ue,H=h.props||ue;let F;if(v&&Ot(v,!1),(F=H.onVnodeBeforeUpdate)&&Qe(F,v,h,u),I&&Mt(h,u,v,"beforeUpdate"),v&&Ot(v,!0),(M.innerHTML&&H.innerHTML==null||M.textContent&&H.textContent==null)&&c(S,""),E?q(u.dynamicChildren,E,S,v,w,us(h,g),p):b||Y(u,h,S,null,v,w,us(h,g),p,!1),C>0){if(C&16)le(S,M,H,v,g);else if(C&2&&M.class!==H.class&&o(S,"class",null,H.class,g),C&4&&o(S,"style",M.style,H.style,g),C&8){const W=h.dynamicProps;for(let fe=0;fe<W.length;fe++){const ie=W[fe],ke=M[ie],$e=H[ie];($e!==ke||ie==="value")&&o(S,ie,ke,$e,g,v)}}C&1&&u.children!==h.children&&c(S,h.children)}else!b&&E==null&&le(S,M,H,v,g);((F=H.onVnodeUpdated)||I)&&Ne(()=>{F&&Qe(F,v,h,u),I&&Mt(h,u,v,"updated")},w)},q=(u,h,v,w,g,p,b)=>{for(let S=0;S<h.length;S++){const C=u[S],E=h[S],I=C.el&&(C.type===me||!It(C,E)||C.shapeFlag&198)?d(C.el):v;x(C,E,I,null,w,g,p,b,!0)}},le=(u,h,v,w,g)=>{if(h!==v){if(h!==ue)for(const p in h)!rn(p)&&!(p in v)&&o(u,p,h[p],null,g,w);for(const p in v){if(rn(p))continue;const b=v[p],S=h[p];b!==S&&p!=="value"&&o(u,p,S,b,g,w)}"value"in v&&o(u,"value",h.value,v.value,g)}},N=(u,h,v,w,g,p,b,S,C)=>{const E=h.el=u?u.el:l(""),I=h.anchor=u?u.anchor:l("");let{patchFlag:M,dynamicChildren:H,slotScopeIds:F}=h;F&&(S=S?S.concat(F):F),u==null?(s(E,v,w),s(I,v,w),be(h.children||[],v,I,g,p,b,S,C)):M>0&&M&64&&H&&u.dynamicChildren?(q(u.dynamicChildren,H,v,g,p,b,S),(h.key!=null||g&&h===g.subTree)&&ci(u,h,!0)):Y(u,h,v,I,g,p,b,S,C)},G=(u,h,v,w,g,p,b,S,C)=>{h.slotScopeIds=S,u==null?h.shapeFlag&512?g.ctx.activate(h,v,w,b,C):ge(h,v,w,g,p,b,C):Ee(u,h,C)},ge=(u,h,v,w,g,p,b)=>{const S=u.component=Sa(u,w,g);if(Yn(u)&&(S.ctx.renderer=z),Ea(S,!1,b),S.asyncDep){if(g&&g.registerDep(S,ee,b),!u.el){const C=S.subTree=ce(Te);R(null,C,h,v)}}else ee(S,u,h,v,g,p,b)},Ee=(u,h,v)=>{const w=h.component=u.component;if(ha(u,h,v))if(w.asyncDep&&!w.asyncResolved){te(w,h,v);return}else w.next=h,w.update();else h.el=u.el,w.vnode=h},ee=(u,h,v,w,g,p,b)=>{const S=()=>{if(u.isMounted){let{next:M,bu:H,u:F,parent:W,vnode:fe}=u;{const Ye=ui(u);if(Ye){M&&(M.el=fe.el,te(u,M,b)),Ye.asyncDep.then(()=>{u.isUnmounted||S()});return}}let ie=M,ke;Ot(u,!1),M?(M.el=fe.el,te(u,M,b)):M=fe,H&&Mn(H),(ke=M.props&&M.props.onVnodeBeforeUpdate)&&Qe(ke,W,M,fe),Ot(u,!0);const $e=mr(u),qe=u.subTree;u.subTree=$e,x(qe,$e,d(qe.el),_(qe),u,g,p),M.el=$e.el,ie===null&&pa(u,$e.el),F&&Ne(F,g),(ke=M.props&&M.props.onVnodeUpdated)&&Ne(()=>Qe(ke,W,M,fe),g)}else{let M;const{el:H,props:F}=h,{bm:W,m:fe,parent:ie,root:ke,type:$e}=u,qe=Wt(h);Ot(u,!1),W&&Mn(W),!qe&&(M=F&&F.onVnodeBeforeMount)&&Qe(M,ie,h),Ot(u,!0);{ke.ce&&ke.ce._def.shadowRoot!==!1&&ke.ce._injectChildStyle($e);const Ye=u.subTree=mr(u);x(null,Ye,v,w,u,g,p),h.el=Ye.el}if(fe&&Ne(fe,g),!qe&&(M=F&&F.onVnodeMounted)){const Ye=h;Ne(()=>Qe(M,ie,Ye),g)}(h.shapeFlag&256||ie&&Wt(ie.vnode)&&ie.vnode.shapeFlag&256)&&u.a&&Ne(u.a,g),u.isMounted=!0,h=v=w=null}};u.scope.on();const C=u.effect=new mo(S);u.scope.off();const E=u.update=C.run.bind(C),I=u.job=C.runIfDirty.bind(C);I.i=u,I.id=u.uid,C.scheduler=()=>Ws(I),Ot(u,!0),E()},te=(u,h,v)=>{h.component=u;const w=u.vnode.props;u.vnode=h,u.next=null,Ql(u,h.props,w,v),ta(u,h.children,v),ft(),ar(u),dt()},Y=(u,h,v,w,g,p,b,S,C=!1)=>{const E=u&&u.children,I=u?u.shapeFlag:0,M=h.children,{patchFlag:H,shapeFlag:F}=h;if(H>0){if(H&128){We(E,M,v,w,g,p,b,S,C);return}else if(H&256){ze(E,M,v,w,g,p,b,S,C);return}}F&8?(I&16&&Oe(E,g,p),M!==E&&c(v,M)):I&16?F&16?We(E,M,v,w,g,p,b,S,C):Oe(E,g,p,!0):(I&8&&c(v,""),F&16&&be(M,v,w,g,p,b,S,C))},ze=(u,h,v,w,g,p,b,S,C)=>{u=u||jt,h=h||jt;const E=u.length,I=h.length,M=Math.min(E,I);let H;for(H=0;H<M;H++){const F=h[H]=C?wt(h[H]):et(h[H]);x(u[H],F,v,null,g,p,b,S,C)}E>I?Oe(u,g,p,!0,!1,M):be(h,v,w,g,p,b,S,C,M)},We=(u,h,v,w,g,p,b,S,C)=>{let E=0;const I=h.length;let M=u.length-1,H=I-1;for(;E<=M&&E<=H;){const F=u[E],W=h[E]=C?wt(h[E]):et(h[E]);if(It(F,W))x(F,W,v,null,g,p,b,S,C);else break;E++}for(;E<=M&&E<=H;){const F=u[M],W=h[H]=C?wt(h[H]):et(h[H]);if(It(F,W))x(F,W,v,null,g,p,b,S,C);else break;M--,H--}if(E>M){if(E<=H){const F=H+1,W=F<I?h[F].el:w;for(;E<=H;)x(null,h[E]=C?wt(h[E]):et(h[E]),v,W,g,p,b,S,C),E++}}else if(E>H)for(;E<=M;)Ce(u[E],g,p,!0),E++;else{const F=E,W=E,fe=new Map;for(E=W;E<=H;E++){const Le=h[E]=C?wt(h[E]):et(h[E]);Le.key!=null&&fe.set(Le.key,E)}let ie,ke=0;const $e=H-W+1;let qe=!1,Ye=0;const Xt=new Array($e);for(E=0;E<$e;E++)Xt[E]=0;for(E=F;E<=M;E++){const Le=u[E];if(ke>=$e){Ce(Le,g,p,!0);continue}let Je;if(Le.key!=null)Je=fe.get(Le.key);else for(ie=W;ie<=H;ie++)if(Xt[ie-W]===0&&It(Le,h[ie])){Je=ie;break}Je===void 0?Ce(Le,g,p,!0):(Xt[Je-W]=E+1,Je>=Ye?Ye=Je:qe=!0,x(Le,h[Je],v,null,g,p,b,S,C),ke++)}const sr=qe?oa(Xt):jt;for(ie=sr.length-1,E=$e-1;E>=0;E--){const Le=W+E,Je=h[Le],rr=Le+1<I?h[Le+1].el:w;Xt[E]===0?x(null,Je,v,rr,g,p,b,S,C):qe&&(ie<0||E!==sr[ie]?He(Je,v,rr,2):ie--)}}},He=(u,h,v,w,g=null)=>{const{el:p,type:b,transition:S,children:C,shapeFlag:E}=u;if(E&6){He(u.component.subTree,h,v,w);return}if(E&128){u.suspense.move(h,v,w);return}if(E&64){b.move(u,h,v,z);return}if(b===me){s(p,h,v);for(let M=0;M<C.length;M++)He(C[M],h,v,w);s(u.anchor,h,v);return}if(b===fs){D(u,h,v);return}if(w!==2&&E&1&&S)if(w===0)S.beforeEnter(p),s(p,h,v),Ne(()=>S.enter(p),g);else{const{leave:M,delayLeave:H,afterLeave:F}=S,W=()=>{u.ctx.isUnmounted?r(p):s(p,h,v)},fe=()=>{M(p,()=>{W(),F&&F()})};H?H(p,W,fe):fe()}else s(p,h,v)},Ce=(u,h,v,w=!1,g=!1)=>{const{type:p,props:b,ref:S,children:C,dynamicChildren:E,shapeFlag:I,patchFlag:M,dirs:H,cacheIndex:F}=u;if(M===-2&&(g=!1),S!=null&&(ft(),an(S,null,v,u,!0),dt()),F!=null&&(h.renderCache[F]=void 0),I&256){h.ctx.deactivate(u);return}const W=I&1&&H,fe=!Wt(u);let ie;if(fe&&(ie=b&&b.onVnodeBeforeUnmount)&&Qe(ie,h,u),I&6)je(u.component,v,w);else{if(I&128){u.suspense.unmount(v,w);return}W&&Mt(u,null,h,"beforeUnmount"),I&64?u.type.remove(u,h,v,z,w):E&&!E.hasOnce&&(p!==me||M>0&&M&64)?Oe(E,h,v,!1,!0):(p===me&&M&384||!g&&I&16)&&Oe(C,h,v),w&&st(u)}(fe&&(ie=b&&b.onVnodeUnmounted)||W)&&Ne(()=>{ie&&Qe(ie,h,u),W&&Mt(u,null,h,"unmounted")},v)},st=u=>{const{type:h,el:v,anchor:w,transition:g}=u;if(h===me){rt(v,w);return}if(h===fs){L(u);return}const p=()=>{r(v),g&&!g.persisted&&g.afterLeave&&g.afterLeave()};if(u.shapeFlag&1&&g&&!g.persisted){const{leave:b,delayLeave:S}=g,C=()=>b(v,p);S?S(u.el,p,C):C()}else p()},rt=(u,h)=>{let v;for(;u!==h;)v=m(u),r(u),u=v;r(h)},je=(u,h,v)=>{const{bum:w,scope:g,job:p,subTree:b,um:S,m:C,a:E,parent:I,slots:{__:M}}=u;gr(C),gr(E),w&&Mn(w),I&&V(M)&&M.forEach(H=>{I.renderCache[H]=void 0}),g.stop(),p&&(p.flags|=8,Ce(b,u,h,v)),S&&Ne(S,h),Ne(()=>{u.isUnmounted=!0},h),h&&h.pendingBranch&&!h.isUnmounted&&u.asyncDep&&!u.asyncResolved&&u.suspenseId===h.pendingId&&(h.deps--,h.deps===0&&h.resolve())},Oe=(u,h,v,w=!1,g=!1,p=0)=>{for(let b=p;b<u.length;b++)Ce(u[b],h,v,w,g)},_=u=>{if(u.shapeFlag&6)return _(u.component.subTree);if(u.shapeFlag&128)return u.suspense.next();const h=m(u.anchor||u.el),v=h&&h[Pl];return v?m(v):h};let $=!1;const P=(u,h,v)=>{u==null?h._vnode&&Ce(h._vnode,null,null,!0):x(h._vnode||null,u,h,null,null,null,v),h._vnode=u,$||($=!0,ar(),zo(),$=!1)},z={p:x,um:Ce,m:He,r:st,mt:ge,mc:be,pc:Y,pbc:q,n:_,o:e};return{render:P,hydrate:void 0,createApp:Yl(P)}}function us({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Ot({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function ra(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function ci(e,t,n=!1){const s=e.children,r=t.children;if(V(s)&&V(r))for(let o=0;o<s.length;o++){const i=s[o];let l=r[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[o]=wt(r[o]),l.el=i.el),!n&&l.patchFlag!==-2&&ci(i,l)),l.type===Zn&&(l.el=i.el),l.type===Te&&!l.el&&(l.el=i.el)}}function oa(e){const t=e.slice(),n=[0];let s,r,o,i,l;const a=e.length;for(s=0;s<a;s++){const f=e[s];if(f!==0){if(r=n[n.length-1],e[r]<f){t[s]=r,n.push(s);continue}for(o=0,i=n.length-1;o<i;)l=o+i>>1,e[n[l]]<f?o=l+1:i=l;f<e[n[o]]&&(o>0&&(t[s]=n[o-1]),n[o]=s)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function ui(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:ui(t)}function gr(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const ia=Symbol.for("v-scx"),la=()=>ut(ia);function kn(e,t,n){return fi(e,t,n)}function fi(e,t,n=ue){const{immediate:s,deep:r,flush:o,once:i}=n,l=ye({},n),a=t&&s||!t&&o!=="post";let f;if(wn){if(o==="sync"){const y=la();f=y.__watcherHandles||(y.__watcherHandles=[])}else if(!a){const y=()=>{};return y.stop=nt,y.resume=nt,y.pause=nt,y}}const c=Ae;l.call=(y,A,x)=>Ke(y,c,A,x);let d=!1;o==="post"?l.scheduler=y=>{Ne(y,c&&c.suspense)}:o!=="sync"&&(d=!0,l.scheduler=(y,A)=>{A?y():Ws(y)}),l.augmentJob=y=>{t&&(y.flags|=4),d&&(y.flags|=2,c&&(y.id=c.uid,y.i=c))};const m=Cl(e,t,l);return wn&&(f?f.push(m):a&&m()),m}function aa(e,t,n){const s=this.proxy,r=pe(e)?e.includes(".")?di(s,e):()=>s[e]:e.bind(s,s);let o;B(t)?o=t:(o=t.handler,n=t);const i=xn(this),l=fi(r,o.bind(s),n);return i(),l}function di(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const ca=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${xt(t)}Modifiers`]||e[`${Pt(t)}Modifiers`];function ua(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||ue;let r=n;const o=t.startsWith("update:"),i=o&&ca(s,t.slice(7));i&&(i.trim&&(r=n.map(c=>pe(c)?c.trim():c)),i.number&&(r=n.map(_s)));let l,a=s[l=ns(t)]||s[l=ns(xt(t))];!a&&o&&(a=s[l=ns(Pt(t))]),a&&Ke(a,e,6,r);const f=s[l+"Once"];if(f){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Ke(f,e,6,r)}}function hi(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const o=e.emits;let i={},l=!1;if(!B(e)){const a=f=>{const c=hi(f,t,!0);c&&(l=!0,ye(i,c))};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!o&&!l?(he(e)&&s.set(e,null),null):(V(o)?o.forEach(a=>i[a]=null):ye(i,o),he(e)&&s.set(e,i),i)}function Xn(e,t){return!e||!jn(t)?!1:(t=t.slice(2).replace(/Once$/,""),se(e,t[0].toLowerCase()+t.slice(1))||se(e,Pt(t))||se(e,t))}function mr(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[o],slots:i,attrs:l,emit:a,render:f,renderCache:c,props:d,data:m,setupState:y,ctx:A,inheritAttrs:x}=e,O=zn(e);let R,k;try{if(n.shapeFlag&4){const L=r||s,U=L;R=et(f.call(U,L,c,d,y,m,A)),k=l}else{const L=t;R=et(L.length>1?L(d,{attrs:l,slots:i,emit:a}):L(d,null)),k=t.props?l:fa(l)}}catch(L){fn.length=0,qn(L,e,1),R=ce(Te)}let D=R;if(k&&x!==!1){const L=Object.keys(k),{shapeFlag:U}=D;L.length&&U&7&&(o&&L.some(Ns)&&(k=da(k,o)),D=Rt(D,k,!1,!0))}return n.dirs&&(D=Rt(D,null,!1,!0),D.dirs=D.dirs?D.dirs.concat(n.dirs):n.dirs),n.transition&&Nt(D,n.transition),R=D,zn(O),R}const fa=e=>{let t;for(const n in e)(n==="class"||n==="style"||jn(n))&&((t||(t={}))[n]=e[n]);return t},da=(e,t)=>{const n={};for(const s in e)(!Ns(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function ha(e,t,n){const{props:s,children:r,component:o}=e,{props:i,children:l,patchFlag:a}=t,f=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&a>=0){if(a&1024)return!0;if(a&16)return s?vr(s,i,f):!!i;if(a&8){const c=t.dynamicProps;for(let d=0;d<c.length;d++){const m=c[d];if(i[m]!==s[m]&&!Xn(f,m))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:s===i?!1:s?i?vr(s,i,f):!0:!!i;return!1}function vr(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const o=s[r];if(t[o]!==e[o]&&!Xn(n,o))return!0}return!1}function pa({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const pi=e=>e.__isSuspense;function ga(e,t){t&&t.pendingBranch?V(e)?t.effects.push(...e):t.effects.push(e):Al(e)}const me=Symbol.for("v-fgt"),Zn=Symbol.for("v-txt"),Te=Symbol.for("v-cmt"),fs=Symbol.for("v-stc"),fn=[];let De=null;function K(e=!1){fn.push(De=e?null:[])}function ma(){fn.pop(),De=fn[fn.length-1]||null}let yn=1;function yr(e,t=!1){yn+=e,e<0&&De&&t&&(De.hasOnce=!0)}function gi(e){return e.dynamicChildren=yn>0?De||jt:null,ma(),yn>0&&De&&De.push(e),e}function J(e,t,n,s,r,o){return gi(T(e,t,n,s,r,o,!0))}function bn(e,t,n,s,r){return gi(ce(e,t,n,s,r,!0))}function _n(e){return e?e.__v_isVNode===!0:!1}function It(e,t){return e.type===t.type&&e.key===t.key}const mi=({key:e})=>e??null,$n=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?pe(e)||Re(e)||B(e)?{i:Se,r:e,k:t,f:!!n}:e:null);function T(e,t=null,n=null,s=0,r=null,o=e===me?0:1,i=!1,l=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&mi(t),ref:t&&$n(t),scopeId:Fo,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Se};return l?(Xs(a,n),o&128&&e.normalize(a)):n&&(a.shapeFlag|=pe(n)?8:16),yn>0&&!i&&De&&(a.patchFlag>0||o&6)&&a.patchFlag!==32&&De.push(a),a}const ce=va;function va(e,t=null,n=null,s=0,r=null,o=!1){if((!e||e===Vl)&&(e=Te),_n(e)){const l=Rt(e,t,!0);return n&&Xs(l,n),yn>0&&!o&&De&&(l.shapeFlag&6?De[De.indexOf(e)]=l:De.push(l)),l.patchFlag=-2,l}if(Aa(e)&&(e=e.__vccOpts),t){t=ya(t);let{class:l,style:a}=t;l&&!pe(l)&&(t.class=Tt(l)),he(a)&&(Gs(a)&&!V(a)&&(a=ye({},a)),t.style=Hs(a))}const i=pe(e)?1:pi(e)?128:Vo(e)?64:he(e)?4:B(e)?2:0;return T(e,t,n,s,r,i,o,!0)}function ya(e){return e?Gs(e)||si(e)?ye({},e):e:null}function Rt(e,t,n=!1,s=!1){const{props:r,ref:o,patchFlag:i,children:l,transition:a}=e,f=t?ba(r||{},t):r,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:f,key:f&&mi(f),ref:t&&t.ref?n&&o?V(o)?o.concat($n(t)):[o,$n(t)]:$n(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==me?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Rt(e.ssContent),ssFallback:e.ssFallback&&Rt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&s&&Nt(c,a.clone(c)),c}function X(e=" ",t=0){return ce(Zn,null,e,t)}function Et(e="",t=!1){return t?(K(),bn(Te,null,e)):ce(Te,null,e)}function et(e){return e==null||typeof e=="boolean"?ce(Te):V(e)?ce(me,null,e.slice()):_n(e)?wt(e):ce(Zn,null,String(e))}function wt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Rt(e)}function Xs(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(V(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),Xs(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!si(t)?t._ctx=Se:r===3&&Se&&(Se.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else B(t)?(t={default:t,_ctx:Se},n=32):(t=String(t),s&64?(n=16,t=[X(t)]):n=8);e.children=t,e.shapeFlag|=n}function ba(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=Tt([t.class,s.class]));else if(r==="style")t.style=Hs([t.style,s.style]);else if(jn(r)){const o=t[r],i=s[r];i&&o!==i&&!(V(o)&&o.includes(i))&&(t[r]=o?[].concat(o,i):i)}else r!==""&&(t[r]=s[r])}return t}function Qe(e,t,n,s=null){Ke(e,t,7,[n,s])}const _a=ei();let wa=0;function Sa(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||_a,o={uid:wa++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new go(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:oi(s,r),emitsOptions:hi(s,r),emit:null,emitted:null,propsDefaults:ue,inheritAttrs:s.inheritAttrs,ctx:ue,data:ue,props:ue,attrs:ue,slots:ue,refs:ue,setupState:ue,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=ua.bind(null,o),e.ce&&e.ce(o),o}let Ae=null;const vi=()=>Ae||Se;let Fn,Ps;{const e=Kn(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),o=>{r.length>1?r.forEach(i=>i(o)):r[0](o)}};Fn=t("__VUE_INSTANCE_SETTERS__",n=>Ae=n),Ps=t("__VUE_SSR_SETTERS__",n=>wn=n)}const xn=e=>{const t=Ae;return Fn(e),e.scope.on(),()=>{e.scope.off(),Fn(t)}},br=()=>{Ae&&Ae.scope.off(),Fn(null)};function yi(e){return e.vnode.shapeFlag&4}let wn=!1;function Ea(e,t=!1,n=!1){t&&Ps(t);const{props:s,children:r}=e.vnode,o=yi(e);Jl(e,s,o,t),ea(e,r,n||t);const i=o?Ca(e,t):void 0;return t&&Ps(!1),i}function Ca(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,jl);const{setup:s}=n;if(s){ft();const r=e.setupContext=s.length>1?Ta(e):null,o=xn(e),i=Cn(s,e,0,[e.props,r]),l=lo(i);if(dt(),o(),(l||e.sp)&&!Wt(e)&&Wo(e),l){if(i.then(br,br),t)return i.then(a=>{_r(e,a)}).catch(a=>{qn(a,e,0)});e.asyncDep=i}else _r(e,i)}else bi(e)}function _r(e,t,n){B(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:he(t)&&(e.setupState=Io(t)),bi(e)}function bi(e,t,n){const s=e.type;e.render||(e.render=s.render||nt);{const r=xn(e);ft();try{Bl(e)}finally{dt(),r()}}}const xa={get(e,t){return xe(e,"get",""),e[t]}};function Ta(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,xa),slots:e.slots,emit:e.emit,expose:t}}function es(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Io(ko(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in un)return un[n](e)},has(t,n){return n in t||n in un}})):e.proxy}function Aa(e){return B(e)&&"__vccOpts"in e}const Be=(e,t)=>Sl(e,t,wn);function Zs(e,t,n){const s=arguments.length;return s===2?he(t)&&!V(t)?_n(t)?ce(e,null,[t]):ce(e,t):ce(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&_n(n)&&(n=[n]),ce(e,t,n))}const Ra="3.5.17";/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ms;const wr=typeof window<"u"&&window.trustedTypes;if(wr)try{Ms=wr.createPolicy("vue",{createHTML:e=>e})}catch{}const _i=Ms?e=>Ms.createHTML(e):e=>e,Pa="http://www.w3.org/2000/svg",Ma="http://www.w3.org/1998/Math/MathML",lt=typeof document<"u"?document:null,Sr=lt&&lt.createElement("template"),Oa={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?lt.createElementNS(Pa,e):t==="mathml"?lt.createElementNS(Ma,e):n?lt.createElement(e,{is:n}):lt.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>lt.createTextNode(e),createComment:e=>lt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>lt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,o){const i=n?n.previousSibling:t.lastChild;if(r&&(r===o||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===o||!(r=r.nextSibling)););else{Sr.innerHTML=_i(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=Sr.content;if(s==="svg"||s==="mathml"){const a=l.firstChild;for(;a.firstChild;)l.appendChild(a.firstChild);l.removeChild(a)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},mt="transition",en="animation",Yt=Symbol("_vtc"),wi={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Si=ye({},Bo,wi),ka=e=>(e.displayName="Transition",e.props=Si,e),$a=ka((e,{slots:t})=>Zs(Ol,Ei(e),t)),kt=(e,t=[])=>{V(e)?e.forEach(n=>n(...t)):e&&e(...t)},Er=e=>e?V(e)?e.some(t=>t.length>1):e.length>1:!1;function Ei(e){const t={};for(const N in e)N in wi||(t[N]=e[N]);if(e.css===!1)return t;const{name:n="v",type:s,duration:r,enterFromClass:o=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:l=`${n}-enter-to`,appearFromClass:a=o,appearActiveClass:f=i,appearToClass:c=l,leaveFromClass:d=`${n}-leave-from`,leaveActiveClass:m=`${n}-leave-active`,leaveToClass:y=`${n}-leave-to`}=e,A=Ia(r),x=A&&A[0],O=A&&A[1],{onBeforeEnter:R,onEnter:k,onEnterCancelled:D,onLeave:L,onLeaveCancelled:U,onBeforeAppear:re=R,onAppear:Z=k,onAppearCancelled:be=D}=t,j=(N,G,ge,Ee)=>{N._enterCancelled=Ee,yt(N,G?c:l),yt(N,G?f:i),ge&&ge()},q=(N,G)=>{N._isLeaving=!1,yt(N,d),yt(N,y),yt(N,m),G&&G()},le=N=>(G,ge)=>{const Ee=N?Z:k,ee=()=>j(G,N,ge);kt(Ee,[G,ee]),Cr(()=>{yt(G,N?a:o),Xe(G,N?c:l),Er(Ee)||xr(G,s,x,ee)})};return ye(t,{onBeforeEnter(N){kt(R,[N]),Xe(N,o),Xe(N,i)},onBeforeAppear(N){kt(re,[N]),Xe(N,a),Xe(N,f)},onEnter:le(!1),onAppear:le(!0),onLeave(N,G){N._isLeaving=!0;const ge=()=>q(N,G);Xe(N,d),N._enterCancelled?(Xe(N,m),Os()):(Os(),Xe(N,m)),Cr(()=>{N._isLeaving&&(yt(N,d),Xe(N,y),Er(L)||xr(N,s,O,ge))}),kt(L,[N,ge])},onEnterCancelled(N){j(N,!1,void 0,!0),kt(D,[N])},onAppearCancelled(N){j(N,!0,void 0,!0),kt(be,[N])},onLeaveCancelled(N){q(N),kt(U,[N])}})}function Ia(e){if(e==null)return null;if(he(e))return[ds(e.enter),ds(e.leave)];{const t=ds(e);return[t,t]}}function ds(e){return Ki(e)}function Xe(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[Yt]||(e[Yt]=new Set)).add(t)}function yt(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.remove(s));const n=e[Yt];n&&(n.delete(t),n.size||(e[Yt]=void 0))}function Cr(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let La=0;function xr(e,t,n,s){const r=e._endId=++La,o=()=>{r===e._endId&&s()};if(n!=null)return setTimeout(o,n);const{type:i,timeout:l,propCount:a}=Ci(e,t);if(!i)return s();const f=i+"end";let c=0;const d=()=>{e.removeEventListener(f,m),o()},m=y=>{y.target===e&&++c>=a&&d()};setTimeout(()=>{c<a&&d()},l+1),e.addEventListener(f,m)}function Ci(e,t){const n=window.getComputedStyle(e),s=A=>(n[A]||"").split(", "),r=s(`${mt}Delay`),o=s(`${mt}Duration`),i=Tr(r,o),l=s(`${en}Delay`),a=s(`${en}Duration`),f=Tr(l,a);let c=null,d=0,m=0;t===mt?i>0&&(c=mt,d=i,m=o.length):t===en?f>0&&(c=en,d=f,m=a.length):(d=Math.max(i,f),c=d>0?i>f?mt:en:null,m=c?c===mt?o.length:a.length:0);const y=c===mt&&/\b(transform|all)(,|$)/.test(s(`${mt}Property`).toString());return{type:c,timeout:d,propCount:m,hasTransform:y}}function Tr(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,s)=>Ar(n)+Ar(e[s])))}function Ar(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function Os(){return document.body.offsetHeight}function Na(e,t,n){const s=e[Yt];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Rr=Symbol("_vod"),Da=Symbol("_vsh"),za=Symbol(""),Ha=/(^|;)\s*display\s*:/;function Fa(e,t,n){const s=e.style,r=pe(n);let o=!1;if(n&&!r){if(t)if(pe(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&In(s,l,"")}else for(const i in t)n[i]==null&&In(s,i,"");for(const i in n)i==="display"&&(o=!0),In(s,i,n[i])}else if(r){if(t!==n){const i=s[za];i&&(n+=";"+i),s.cssText=n,o=Ha.test(n)}}else t&&e.removeAttribute("style");Rr in e&&(e[Rr]=o?s.display:"",e[Da]&&(s.display="none"))}const Pr=/\s*!important$/;function In(e,t,n){if(V(n))n.forEach(s=>In(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=Va(e,t);Pr.test(n)?e.setProperty(Pt(s),n.replace(Pr,""),"important"):e[s]=n}}const Mr=["Webkit","Moz","ms"],hs={};function Va(e,t){const n=hs[t];if(n)return n;let s=xt(t);if(s!=="filter"&&s in e)return hs[t]=s;s=uo(s);for(let r=0;r<Mr.length;r++){const o=Mr[r]+s;if(o in e)return hs[t]=o}return t}const Or="http://www.w3.org/1999/xlink";function kr(e,t,n,s,r,o=Qi(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Or,t.slice(6,t.length)):e.setAttributeNS(Or,t,n):n==null||o&&!fo(n)?e.removeAttribute(t):e.setAttribute(t,o?"":ht(n)?String(n):n)}function $r(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?_i(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,a=n==null?e.type==="checkbox"?"on":"":String(n);(l!==a||!("_value"in e))&&(e.value=a),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=fo(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(r||t)}function Ft(e,t,n,s){e.addEventListener(t,n,s)}function ja(e,t,n,s){e.removeEventListener(t,n,s)}const Ir=Symbol("_vei");function Ba(e,t,n,s,r=null){const o=e[Ir]||(e[Ir]={}),i=o[t];if(s&&i)i.value=s;else{const[l,a]=Ua(t);if(s){const f=o[t]=Wa(s,r);Ft(e,l,f,a)}else i&&(ja(e,l,i,a),o[t]=void 0)}}const Lr=/(?:Once|Passive|Capture)$/;function Ua(e){let t;if(Lr.test(e)){t={};let s;for(;s=e.match(Lr);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Pt(e.slice(2)),t]}let ps=0;const Ka=Promise.resolve(),Ga=()=>ps||(Ka.then(()=>ps=0),ps=Date.now());function Wa(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;Ke(qa(s,n.value),t,5,[s])};return n.value=e,n.attached=Ga(),n}function qa(e,t){if(V(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const Nr=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Ya=(e,t,n,s,r,o)=>{const i=r==="svg";t==="class"?Na(e,s,i):t==="style"?Fa(e,n,s):jn(t)?Ns(t)||Ba(e,t,n,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Ja(e,t,s,i))?($r(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&kr(e,t,s,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!pe(s))?$r(e,xt(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),kr(e,t,s,i))};function Ja(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&Nr(t)&&B(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return Nr(t)&&pe(n)?!1:t in e}const xi=new WeakMap,Ti=new WeakMap,Vn=Symbol("_moveCb"),Dr=Symbol("_enterCb"),Qa=e=>(delete e.props.mode,e),Xa=Qa({name:"TransitionGroup",props:ye({},Si,{tag:String,moveClass:String}),setup(e,{slots:t}){const n=vi(),s=jo();let r,o;return Yo(()=>{if(!r.length)return;const i=e.moveClass||`${e.name||"v"}-move`;if(!sc(r[0].el,n.vnode.el,i)){r=[];return}r.forEach(ec),r.forEach(tc);const l=r.filter(nc);Os(),l.forEach(a=>{const f=a.el,c=f.style;Xe(f,i),c.transform=c.webkitTransform=c.transitionDuration="";const d=f[Vn]=m=>{m&&m.target!==f||(!m||/transform$/.test(m.propertyName))&&(f.removeEventListener("transitionend",d),f[Vn]=null,yt(f,i))};f.addEventListener("transitionend",d)}),r=[]}),()=>{const i=Q(e),l=Ei(i);let a=i.tag||me;if(r=[],o)for(let f=0;f<o.length;f++){const c=o[f];c.el&&c.el instanceof Element&&(r.push(c),Nt(c,vn(c,l,s,n)),xi.set(c,c.el.getBoundingClientRect()))}o=t.default?qs(t.default()):[];for(let f=0;f<o.length;f++){const c=o[f];c.key!=null&&Nt(c,vn(c,l,s,n))}return ce(a,null,o)}}}),Za=Xa;function ec(e){const t=e.el;t[Vn]&&t[Vn](),t[Dr]&&t[Dr]()}function tc(e){Ti.set(e,e.el.getBoundingClientRect())}function nc(e){const t=xi.get(e),n=Ti.get(e),s=t.left-n.left,r=t.top-n.top;if(s||r){const o=e.el.style;return o.transform=o.webkitTransform=`translate(${s}px,${r}px)`,o.transitionDuration="0s",e}}function sc(e,t,n){const s=e.cloneNode(),r=e[Yt];r&&r.forEach(l=>{l.split(/\s+/).forEach(a=>a&&s.classList.remove(a))}),n.split(/\s+/).forEach(l=>l&&s.classList.add(l)),s.style.display="none";const o=t.nodeType===1?t:t.parentNode;o.appendChild(s);const{hasTransform:i}=Ci(s);return o.removeChild(s),i}const zr=e=>{const t=e.props["onUpdate:modelValue"]||!1;return V(t)?n=>Mn(t,n):t};function rc(e){e.target.composing=!0}function Hr(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const gs=Symbol("_assign"),oc={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e[gs]=zr(r);const o=s||r.props&&r.props.type==="number";Ft(e,t?"change":"input",i=>{if(i.target.composing)return;let l=e.value;n&&(l=l.trim()),o&&(l=_s(l)),e[gs](l)}),n&&Ft(e,"change",()=>{e.value=e.value.trim()}),t||(Ft(e,"compositionstart",rc),Ft(e,"compositionend",Hr),Ft(e,"change",Hr))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:r,number:o}},i){if(e[gs]=zr(i),e.composing)return;const l=(o||e.type==="number")&&!/^0\d/.test(e.value)?_s(e.value):e.value,a=t??"";l!==a&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||r&&e.value.trim()===a)||(e.value=a))}},ic={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},lc=(e,t)=>{const n=e._withKeys||(e._withKeys={}),s=t.join(".");return n[s]||(n[s]=r=>{if(!("key"in r))return;const o=Pt(r.key);if(t.some(i=>i===o||ic[i]===o))return e(r)})},ac=ye({patchProp:Ya},Oa);let Fr;function cc(){return Fr||(Fr=na(ac))}const uc=(...e)=>{const t=cc().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=dc(s);if(!r)return;const o=t._component;!B(o)&&!o.render&&!o.template&&(o.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=n(r,!1,fc(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function fc(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function dc(e){return pe(e)?document.querySelector(e):e}/*!
 * pinia v3.0.3
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const hc=Symbol();var Vr;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(Vr||(Vr={}));function pc(){const e=Xi(!0),t=e.run(()=>ae({}));let n=[],s=[];const r=ko({install(o){r._a=o,o.provide(hc,r),o.config.globalProperties.$pinia=r,s.forEach(i=>n.push(i)),s=[]},use(o){return this._a?n.push(o):s.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}class gc{session=null;isEnabled=!1;apiEndpoint="";constructor(){this.isEnabled=!0,this.apiEndpoint=""}startSession(){this.isEnabled&&(this.session={sessionId:this.generateSessionId(),startTime:Date.now(),events:[]},this.track("session_start"))}endSession(){!this.isEnabled||!this.session||(this.session.endTime=Date.now(),this.track("session_end",{duration:this.session.endTime-this.session.startTime,eventCount:this.session.events.length}),this.sendSessionData(),this.session=null)}track(t,n){if(!this.isEnabled||!this.session)return;const s={event:t,properties:n,timestamp:Date.now()};this.session.events.push(s),this.isImportantEvent(t)&&this.sendEvent(s)}trackGameStart(){this.track("game_start")}trackGameEnd(t,n,s){this.track("game_end",{final_score:t,total_moves:n,game_duration:s,reached_2048:t>=2048})}trackMove(t,n){this.track("move",{direction:t,score_gained:n})}trackScoreSubmission(t,n){this.track("score_submission",{score:t,success:n})}trackLeaderboardView(t){this.track("leaderboard_view",{period:t})}generateSessionId(){return Date.now().toString(36)+Math.random().toString(36).substr(2)}isImportantEvent(t){return["game_end","score_submission","error"].includes(t)}async sendEvent(t){if(this.apiEndpoint)try{await fetch(this.apiEndpoint,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:"event",sessionId:this.session?.sessionId,...t})})}catch(n){console.debug("Analytics event failed to send:",n)}}async sendSessionData(){if(!(!this.apiEndpoint||!this.session))try{await fetch(this.apiEndpoint,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:"session",...this.session})})}catch(t){console.debug("Analytics session failed to send:",t)}}}const tt=new gc;typeof window<"u"&&(tt.startSession(),window.addEventListener("beforeunload",()=>{tt.endSession()}),document.addEventListener("visibilitychange",()=>{document.visibilityState==="hidden"?tt.endSession():document.visibilityState==="visible"&&tt.startSession()}));const mc={class:"game-container"},vc={class:"score-container"},yc={class:"score-box"},bc={class:"score-box"},_c={class:"score-value"},wc={class:"grid-container"},Sc=["data-id"],Ec={class:"game-controls"},Cc={key:0,class:"test-controls"},xc={class:"test-buttons"},Tc={key:0,class:"restore-notification"},Ac={key:0,class:"game-over-overlay"},Rc={class:"game-over-message"},Pc={key:0,class:"game-over-actions"},Mc={key:1,class:"score-submission"},Oc={class:"submission-actions"},kc=["disabled"],$c={key:0,class:"submission-error"},Ic={key:1,class:"submission-success"},Lc=Dt({__name:"GameBoard",setup(e){const t=ae([]),n=ae([]),s=ae(0),r=ae(0),o=ae(!1),i=ae(),l=ae();let a=1,f=0,c=0;const d=ae(!1),m=ae(""),y=ae(!1),A=ae(""),x=ae(!1),O=ae("分数提交成功！"),R=ae(!1),k=ae(0),D=ae(0),L=ae(Date.now()),U=ae(""),re=ae(0),Z=ae(!1),be=g=>g<=1e3?Math.min(g/1e3*.2,.2):g<=5e3?.2+(g-1e3)/4e3*.3:g<=15e3?.5+(g-5e3)/1e4*.3:Math.min(.8+(g-15e3)/2e4*.2,1),j=g=>{const p=240-g*180,b=60+g*40,S=50+g*10;return`hsl(${p}, ${b}%, ${S}%)`},q=g=>g<.3?`linear-gradient(135deg,
      hsl(240, 60%, 70%) 0%,
      hsl(260, 50%, 75%) 100%)`:g<.6?`linear-gradient(135deg,
      hsl(280, 70%, 65%) 0%,
      hsl(320, 60%, 70%) 100%)`:g<.8?`linear-gradient(135deg,
      hsl(20, 80%, 60%) 0%,
      hsl(40, 75%, 65%) 100%)`:`linear-gradient(135deg,
      hsl(0, 90%, 55%) 0%,
      hsl(15, 85%, 60%) 50%,
      hsl(30, 80%, 65%) 100%)`,le="http://localhost:3001/api",N=(g=!1)=>{if(oe(),!g&&$()){R.value=!0,setTimeout(()=>{R.value=!1},3e3),h();return}t.value=Array(4).fill(null).map(()=>Array(4).fill(0)),n.value=[],s.value=0,o.value=!1,a=1,D.value=0,L.value=Date.now(),G(),G(),h(),_(),tt.trackGameStart()},G=()=>{const g=[];for(let p=0;p<4;p++)for(let b=0;b<4;b++)t.value[p][b]===0&&g.push({row:p,col:b});if(g.length>0){const p=g[Math.floor(Math.random()*g.length)],b=Math.random()<.9?2:4;t.value[p.row][p.col]=b,n.value.push({id:a++,value:b,row:p.row,col:p.col})}},ge=()=>{let g=!1;const p=t.value.map(b=>[...b]);for(let b=0;b<4;b++){const S=p[b].filter(I=>I!==0),C=[];let E=0;for(;E<S.length;)E<S.length-1&&S[E]===S[E+1]?(C.push(S[E]*2),s.value+=S[E]*2,E+=2):(C.push(S[E]),E++);for(;C.length<4;)C.push(0);for(let I=0;I<4;I++)p[b][I]!==C[I]&&(g=!0);p[b]=C}return g?(t.value=p,te(),!0):!1},Ee=g=>{const p=g.length,b=Array(p).fill(null).map(()=>Array(p).fill(0));for(let S=0;S<p;S++)for(let C=0;C<p;C++)b[C][p-1-S]=g[S][C];return b},ee=g=>{let p=!1,b=0;switch(g){case"left":b=0;break;case"up":b=1;break;case"right":b=2;break;case"down":b=3;break}let S=t.value;for(let C=0;C<b;C++)S=Ee(S);t.value=S,p=ge();for(let C=0;C<(4-b)%4;C++)t.value=Ee(t.value);return p},te=()=>{const g=[];for(let p=0;p<4;p++)for(let b=0;b<4;b++)t.value[p][b]!==0&&g.push({id:a++,value:t.value[p][b],row:p,col:b});n.value=g},Y=()=>{for(let g=0;g<4;g++)for(let p=0;p<4;p++)if(t.value[g][p]===0)return!1;for(let g=0;g<4;g++)for(let p=0;p<4;p++){const b=t.value[g][p];if(p<3&&t.value[g][p+1]===b||g<3&&t.value[g+1][p]===b)return!1}return!0},ze=g=>{if(o.value)return;let p=!1;switch(g.key){case"ArrowLeft":g.preventDefault(),U.value="left",p=ee("left");break;case"ArrowUp":g.preventDefault(),U.value="up",p=ee("up");break;case"ArrowRight":g.preventDefault(),U.value="right",p=ee("right");break;case"ArrowDown":g.preventDefault(),U.value="down",p=ee("down");break}p?(G(),v(),u(),_(),Y()&&(o.value=!0,_())):w()},We=g=>{g.touches.length===1&&(f=g.touches[0].clientX,c=g.touches[0].clientY)},He=g=>{if(o.value||g.changedTouches.length!==1)return;const p=g.changedTouches[0].clientX,b=g.changedTouches[0].clientY,S=p-f,C=b-c,E=30;if(Math.abs(S)<E&&Math.abs(C)<E)return;let I=!1;Math.abs(S)>Math.abs(C)?S>0?I=ee("right"):I=ee("left"):C>0?I=ee("down"):I=ee("up"),I?(D.value++,G(),v(),u(),h(),_(),tt.trackMove(U.value,re.value),Y()&&(o.value=!0,_(),tt.trackGameEnd(s.value,D.value,Date.now()-L.value))):w()},Ce=(g,p)=>{const b=`${g}-${JSON.stringify(p)}-${Date.now()}`;let S=0;for(let C=0;C<b.length;C++){const E=b.charCodeAt(C);S=(S<<5)-S+E,S=S&S}return Math.abs(S).toString(16)},st=async()=>{if(!m.value.trim()){A.value="请输入昵称";return}y.value=!0,A.value="",x.value=!1;try{const g=Ce(s.value,t.value),p=await fetch(`${le}/scores`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({playerName:m.value.trim(),score:s.value,validationPayload:g})}),b=await p.json();if(!p.ok)throw new Error(b.error||"提交失败");x.value=!0,A.value="",tt.trackScoreSubmission(s.value,!0),b.isNewRecord?(O.value="🎉 新纪录！分数更新成功！",setTimeout(()=>{d.value=!1,x.value=!1},4e3)):(O.value="分数提交成功！",setTimeout(()=>{d.value=!1,x.value=!1},3e3))}catch(g){let p="网络错误";g instanceof Error&&(p=g.message,p.includes("服务器存储空间不足")&&(p="🚫 服务器存储空间不足，请稍后再试或联系管理员")),A.value=p,tt.trackScoreSubmission(s.value,!1)}finally{y.value=!1}},rt=()=>{P(),d.value=!1,m.value="",A.value="",x.value=!1,N(!0)},je=g=>{s.value=g,h(),v(),console.log(`🎨 设置测试分数: ${g}, 强度: ${k.value.toFixed(2)}`)},Oe=()=>{console.log("🎨 开始自动颜色测试...");const g=[0,500,1500,3e3,8e3,12e3,2e4,5e4];let p=0;const b=setInterval(()=>{if(p>=g.length){clearInterval(b),console.log("✅ 动态颜色测试完成");return}je(g[p]),p++},2e3)},_=()=>{const g={board:t.value,tiles:n.value,score:s.value,isGameOver:o.value,nextTileId:a};localStorage.setItem("game2048-state",JSON.stringify(g))},$=()=>{const g=localStorage.getItem("game2048-state");if(g)try{const p=JSON.parse(g);return t.value=p.board,n.value=p.tiles,s.value=p.score,o.value=p.isGameOver,a=p.nextTileId,!0}catch(p){return console.error("Failed to load game state:",p),!1}return!1},P=()=>{localStorage.removeItem("game2048-state")},z=()=>{localStorage.setItem("game2048-best-score",r.value.toString())},oe=()=>{const g=localStorage.getItem("game2048-best-score");g&&(r.value=parseInt(g,10))},u=()=>{s.value>r.value&&(r.value=s.value,z())},h=()=>{k.value=be(s.value);const g=document.querySelector(".score-container"),p=document.querySelector(".game-container");if(g&&p){const b=k.value,S=j(b),C=q(b),E=g.querySelectorAll(".score-value"),I=g.querySelectorAll(".score-label");E.forEach(F=>{F.style.color=S});const M=b>.5?"rgba(255, 255, 255, 0.9)":"rgba(102, 126, 234, 0.8)";I.forEach(F=>{F.style.color=M}),g.querySelectorAll(".score-box").forEach(F=>{F.style.background=C,b>.8?F.classList.add("score-pulse-intense"):b>.6?F.classList.add("score-pulse-medium"):F.classList.remove("score-pulse-intense","score-pulse-medium")}),b>.7?p.classList.add("game-intense"):b>.4?p.classList.add("game-heated"):p.classList.remove("game-intense","game-heated")}},v=()=>{if(l.value){const g=k.value;g>.8?l.value.classList.add("score-increase-intense"):g>.5?l.value.classList.add("score-increase-heated"):l.value.classList.add("score-increase"),setTimeout(()=>{l.value?.classList.remove("score-increase","score-increase-heated","score-increase-intense")},500)}},w=()=>{i.value&&(i.value.classList.add("shake"),setTimeout(()=>{i.value?.classList.remove("shake")},500))};return Qn(()=>{N(),window.addEventListener("keydown",ze),typeof window<"u"&&(window.testDynamicColors=()=>{console.log("🎨 测试动态颜色系统...");const g=[0,500,1500,3e3,8e3,12e3,2e4,5e4];let p=0;const b=setInterval(()=>{if(p>=g.length){clearInterval(b),console.log("✅ 动态颜色测试完成");return}s.value=g[p],h(),v(),console.log(`分数: ${s.value}, 强度: ${k.value.toFixed(2)}`),p++},2e3)})}),Ys(()=>{window.removeEventListener("keydown",ze)}),(g,p)=>(K(),J("div",mc,[T("div",vc,[T("div",yc,[p[8]||(p[8]=T("div",{class:"score-label"},"分数",-1)),T("div",{class:"score-value",ref_key:"scoreValue",ref:l},ve(s.value),513)]),T("div",bc,[p[9]||(p[9]=T("div",{class:"score-label"},"最高分",-1)),T("div",_c,ve(r.value),1)])]),T("div",{class:"game-board",ref_key:"gameBoard",ref:i,onTouchstart:We,onTouchend:He},[T("div",wc,[(K(),J(me,null,cn(4,b=>T("div",{class:"grid-row",key:b},[(K(),J(me,null,cn(4,S=>T("div",{class:"grid-cell",key:S})),64))])),64))]),ce(Za,{name:"tile",tag:"div",class:"tile-container"},{default:_e(()=>[(K(!0),J(me,null,cn(n.value,b=>(K(),J("div",{key:b.id,"data-id":b.id,class:Tt(["tile",`tile-${b.value}`,`tile-position-${b.row}-${b.col}`])},ve(b.value),11,Sc))),128))]),_:1})],544),T("div",Ec,[T("button",{onClick:rt,class:"restart-btn"},"新游戏"),p[11]||(p[11]=T("p",{class:"auto-save-hint"},"游戏进度自动保存",-1)),Z.value?(K(),J("div",Cc,[p[10]||(p[10]=T("h4",null,"🎨 动态颜色测试",-1)),T("div",xc,[T("button",{onClick:p[0]||(p[0]=b=>je(500)),class:"test-btn"},"500分"),T("button",{onClick:p[1]||(p[1]=b=>je(2e3)),class:"test-btn"},"2K分"),T("button",{onClick:p[2]||(p[2]=b=>je(8e3)),class:"test-btn"},"8K分"),T("button",{onClick:p[3]||(p[3]=b=>je(15e3)),class:"test-btn"},"15K分"),T("button",{onClick:p[4]||(p[4]=b=>je(3e4)),class:"test-btn"},"30K分"),T("button",{onClick:Oe,class:"test-btn-auto"},"自动测试")])])):Et("",!0)]),ce($a,{name:"notification"},{default:_e(()=>[R.value?(K(),J("div",Tc,p[12]||(p[12]=[T("div",{class:"notification-content"},[T("span",{class:"notification-icon"},"🎮"),T("span",{class:"notification-text"},"游戏进度已恢复")],-1)]))):Et("",!0)]),_:1}),o.value?(K(),J("div",Ac,[T("div",Rc,[p[13]||(p[13]=T("h2",null,"游戏结束!",-1)),T("p",null,"最终分数: "+ve(s.value),1),d.value?(K(),J("div",Mc,[Rl(T("input",{"onUpdate:modelValue":p[6]||(p[6]=b=>m.value=b),type:"text",placeholder:"输入你的昵称",maxlength:"20",class:"player-name-input",onKeyup:lc(st,["enter"])},null,544),[[oc,m.value]]),T("div",Oc,[T("button",{onClick:st,disabled:y.value||!m.value.trim(),class:"submit-btn"},ve(y.value?"提交中...":"提交"),9,kc),T("button",{onClick:p[7]||(p[7]=b=>d.value=!1),class:"cancel-btn"},"取消")]),A.value?(K(),J("div",$c,ve(A.value),1)):Et("",!0),x.value?(K(),J("div",Ic,ve(O.value),1)):Et("",!0)])):(K(),J("div",Pc,[T("button",{onClick:p[5]||(p[5]=b=>d.value=!0),class:"submit-score-btn"},"提交分数"),T("button",{onClick:rt,class:"restart-btn"},"再来一局")]))])])):Et("",!0)]))}}),gt=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n},Nc=gt(Lc,[["__scopeId","data-v-833bf148"]]),Dc={class:"leaderboard-container"},zc={class:"period-selector"},Hc=["onClick"],Fc={key:0,class:"loading"},Vc={key:1,class:"error"},jc={key:2,class:"leaderboard-list"},Bc={key:0,class:"empty-state"},Uc={key:1},Kc={class:"rank"},Gc={key:0,class:"medal"},Wc={key:1,class:"rank-number"},qc={class:"player-info"},Yc={class:"player-name"},Jc={class:"play-time"},Qc={class:"score"},Xc={key:3,class:"stats-section"},Zc={class:"stats-grid"},eu={class:"stat-item"},tu={class:"stat-value"},nu={class:"stat-item"},su={class:"stat-value"},ru={class:"stat-item"},ou={class:"stat-value"},iu={class:"stat-item"},lu={class:"stat-value"},au=Dt({__name:"Leaderboard",setup(e){const t="http://localhost:3001/api",n=ae([]),s=ae(null),r=ae(!1),o=ae(""),i=ae("alltime"),l=[{value:"alltime",label:"总榜"},{value:"weekly",label:"周榜"},{value:"daily",label:"日榜"}],a=async()=>{r.value=!0,o.value="";try{const x=await fetch(`${t}/leaderboard?period=${i.value}&limit=10`);if(!x.ok)throw new Error("获取排行榜失败");const O=await x.json();n.value=O.leaderboard||[]}catch(x){o.value=x instanceof Error?x.message:"网络错误",console.error("Failed to fetch leaderboard:",x)}finally{r.value=!1}},f=async()=>{try{const x=await fetch(`${t}/stats`);if(!x.ok)throw new Error("获取统计信息失败");const O=await x.json();s.value=O}catch(x){console.error("Failed to fetch stats:",x)}},c=x=>{i.value=x,a(),tt.trackLeaderboardView(x)},d=x=>x===1?"first-place":x===2?"second-place":x===3?"third-place":"",m=x=>["🥇","🥈","🥉"][x-1]||"",y=x=>x.toLocaleString(),A=x=>{const O=new Date(x),k=new Date().getTime()-O.getTime(),D=Math.floor(k/(1e3*60*60*24));return D===0?"今天":D===1?"昨天":D<7?`${D}天前`:O.toLocaleDateString("zh-CN")};return Qn(()=>{a(),f()}),(x,O)=>(K(),J("div",Dc,[O[5]||(O[5]=T("h2",null,"排行榜",-1)),T("div",zc,[(K(),J(me,null,cn(l,R=>T("button",{key:R.value,class:Tt(["period-btn",{active:i.value===R.value}]),onClick:k=>c(R.value)},ve(R.label),11,Hc)),64))]),r.value?(K(),J("div",Fc," 加载中... ")):o.value?(K(),J("div",Vc,[X(ve(o.value)+" ",1),T("button",{onClick:a,class:"retry-btn"},"重试")])):(K(),J("div",jc,[n.value.length===0?(K(),J("div",Bc," 暂无排行榜数据 ")):(K(),J("div",Uc,[(K(!0),J(me,null,cn(n.value,R=>(K(),J("div",{key:R.rank,class:Tt(["leaderboard-entry",d(R.rank)])},[T("div",Kc,[R.rank<=3?(K(),J("span",Gc,ve(m(R.rank)),1)):(K(),J("span",Wc,ve(R.rank),1))]),T("div",qc,[T("div",Yc,ve(R.playerName),1),T("div",Jc,ve(A(R.createdAt)),1)]),T("div",Qc,ve(y(R.score)),1)],2))),128))]))])),s.value?(K(),J("div",Xc,[O[4]||(O[4]=T("h3",null,"游戏统计",-1)),T("div",Zc,[T("div",eu,[T("div",tu,ve(s.value.totalGames),1),O[0]||(O[0]=T("div",{class:"stat-label"},"总游戏数",-1))]),T("div",nu,[T("div",su,ve(y(s.value.highestScore)),1),O[1]||(O[1]=T("div",{class:"stat-label"},"最高分",-1))]),T("div",ru,[T("div",ou,ve(y(s.value.averageScore)),1),O[2]||(O[2]=T("div",{class:"stat-label"},"平均分",-1))]),T("div",iu,[T("div",lu,ve(s.value.uniquePlayers),1),O[3]||(O[3]=T("div",{class:"stat-label"},"玩家数",-1))])])])):Et("",!0)]))}}),cu=gt(au,[["__scopeId","data-v-059c1fb9"]]),uu={id:"app"},fu={class:"main-nav"},du=Dt({__name:"App",setup(e){const t=ae("game"),n=()=>{t.value="game"},s=()=>{t.value="leaderboard"};return(r,o)=>(K(),J("div",uu,[T("header",null,[o[0]||(o[0]=T("h1",null,"2048 游戏",-1)),o[1]||(o[1]=T("p",null,"使用方向键移动方块，合并相同数字达到2048！",-1)),T("nav",fu,[T("button",{class:Tt(["nav-btn",{active:t.value==="game"}]),onClick:n}," 游戏 ",2),T("button",{class:Tt(["nav-btn",{active:t.value==="leaderboard"}]),onClick:s}," 排行榜 ",2)])]),T("main",null,[t.value==="game"?(K(),bn(Nc,{key:0})):Et("",!0),t.value==="leaderboard"?(K(),bn(cu,{key:1})):Et("",!0)])]))}}),hu=gt(du,[["__scopeId","data-v-eb64361f"]]),pu="modulepreload",gu=function(e){return"/"+e},jr={},mu=function(t,n,s){let r=Promise.resolve();if(n&&n.length>0){let a=function(f){return Promise.all(f.map(c=>Promise.resolve(c).then(d=>({status:"fulfilled",value:d}),d=>({status:"rejected",reason:d}))))};document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),l=i?.nonce||i?.getAttribute("nonce");r=a(n.map(f=>{if(f=gu(f),f in jr)return;jr[f]=!0;const c=f.endsWith(".css"),d=c?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${f}"]${d}`))return;const m=document.createElement("link");if(m.rel=c?"stylesheet":pu,c||(m.as="script"),m.crossOrigin="",m.href=f,l&&m.setAttribute("nonce",l),document.head.appendChild(m),c)return new Promise((y,A)=>{m.addEventListener("load",y),m.addEventListener("error",()=>A(new Error(`Unable to preload CSS for ${f}`)))})}))}function o(i){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=i,window.dispatchEvent(l),!l.defaultPrevented)throw i}return r.then(i=>{for(const l of i||[])l.status==="rejected"&&o(l.reason);return t().catch(o)})};/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Vt=typeof document<"u";function Ai(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function vu(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Ai(e.default)}const ne=Object.assign;function ms(e,t){const n={};for(const s in t){const r=t[s];n[s]=Ge(r)?r.map(e):e(r)}return n}const dn=()=>{},Ge=Array.isArray,Ri=/#/g,yu=/&/g,bu=/\//g,_u=/=/g,wu=/\?/g,Pi=/\+/g,Su=/%5B/g,Eu=/%5D/g,Mi=/%5E/g,Cu=/%60/g,Oi=/%7B/g,xu=/%7C/g,ki=/%7D/g,Tu=/%20/g;function er(e){return encodeURI(""+e).replace(xu,"|").replace(Su,"[").replace(Eu,"]")}function Au(e){return er(e).replace(Oi,"{").replace(ki,"}").replace(Mi,"^")}function ks(e){return er(e).replace(Pi,"%2B").replace(Tu,"+").replace(Ri,"%23").replace(yu,"%26").replace(Cu,"`").replace(Oi,"{").replace(ki,"}").replace(Mi,"^")}function Ru(e){return ks(e).replace(_u,"%3D")}function Pu(e){return er(e).replace(Ri,"%23").replace(wu,"%3F")}function Mu(e){return e==null?"":Pu(e).replace(bu,"%2F")}function Sn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Ou=/\/$/,ku=e=>e.replace(Ou,"");function vs(e,t,n="/"){let s,r={},o="",i="";const l=t.indexOf("#");let a=t.indexOf("?");return l<a&&l>=0&&(a=-1),a>-1&&(s=t.slice(0,a),o=t.slice(a+1,l>-1?l:t.length),r=e(o)),l>-1&&(s=s||t.slice(0,l),i=t.slice(l,t.length)),s=Nu(s??t,n),{fullPath:s+(o&&"?")+o+i,path:s,query:r,hash:Sn(i)}}function $u(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Br(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Iu(e,t,n){const s=t.matched.length-1,r=n.matched.length-1;return s>-1&&s===r&&Jt(t.matched[s],n.matched[r])&&$i(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Jt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function $i(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Lu(e[n],t[n]))return!1;return!0}function Lu(e,t){return Ge(e)?Ur(e,t):Ge(t)?Ur(t,e):e===t}function Ur(e,t){return Ge(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function Nu(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),r=s[s.length-1];(r===".."||r===".")&&s.push("");let o=n.length-1,i,l;for(i=0;i<s.length;i++)if(l=s[i],l!==".")if(l==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+s.slice(i).join("/")}const vt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var En;(function(e){e.pop="pop",e.push="push"})(En||(En={}));var hn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(hn||(hn={}));function Du(e){if(!e)if(Vt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),ku(e)}const zu=/^[^#]+#/;function Hu(e,t){return e.replace(zu,"#")+t}function Fu(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const ts=()=>({left:window.scrollX,top:window.scrollY});function Vu(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=Fu(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Kr(e,t){return(history.state?history.state.position-t:-1)+e}const $s=new Map;function ju(e,t){$s.set(e,t)}function Bu(e){const t=$s.get(e);return $s.delete(e),t}let Uu=()=>location.protocol+"//"+location.host;function Ii(e,t){const{pathname:n,search:s,hash:r}=t,o=e.indexOf("#");if(o>-1){let l=r.includes(e.slice(o))?e.slice(o).length:1,a=r.slice(l);return a[0]!=="/"&&(a="/"+a),Br(a,"")}return Br(n,e)+s+r}function Ku(e,t,n,s){let r=[],o=[],i=null;const l=({state:m})=>{const y=Ii(e,location),A=n.value,x=t.value;let O=0;if(m){if(n.value=y,t.value=m,i&&i===A){i=null;return}O=x?m.position-x.position:0}else s(y);r.forEach(R=>{R(n.value,A,{delta:O,type:En.pop,direction:O?O>0?hn.forward:hn.back:hn.unknown})})};function a(){i=n.value}function f(m){r.push(m);const y=()=>{const A=r.indexOf(m);A>-1&&r.splice(A,1)};return o.push(y),y}function c(){const{history:m}=window;m.state&&m.replaceState(ne({},m.state,{scroll:ts()}),"")}function d(){for(const m of o)m();o=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:a,listen:f,destroy:d}}function Gr(e,t,n,s=!1,r=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:r?ts():null}}function Gu(e){const{history:t,location:n}=window,s={value:Ii(e,n)},r={value:t.state};r.value||o(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(a,f,c){const d=e.indexOf("#"),m=d>-1?(n.host&&document.querySelector("base")?e:e.slice(d))+a:Uu()+e+a;try{t[c?"replaceState":"pushState"](f,"",m),r.value=f}catch(y){console.error(y),n[c?"replace":"assign"](m)}}function i(a,f){const c=ne({},t.state,Gr(r.value.back,a,r.value.forward,!0),f,{position:r.value.position});o(a,c,!0),s.value=a}function l(a,f){const c=ne({},r.value,t.state,{forward:a,scroll:ts()});o(c.current,c,!0);const d=ne({},Gr(s.value,a,null),{position:c.position+1},f);o(a,d,!1),s.value=a}return{location:s,state:r,push:l,replace:i}}function Wu(e){e=Du(e);const t=Gu(e),n=Ku(e,t.state,t.location,t.replace);function s(o,i=!0){i||n.pauseListeners(),history.go(o)}const r=ne({location:"",base:e,go:s,createHref:Hu.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function qu(e){return typeof e=="string"||e&&typeof e=="object"}function Li(e){return typeof e=="string"||typeof e=="symbol"}const Ni=Symbol("");var Wr;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Wr||(Wr={}));function Qt(e,t){return ne(new Error,{type:e,[Ni]:!0},t)}function it(e,t){return e instanceof Error&&Ni in e&&(t==null||!!(e.type&t))}const qr="[^/]+?",Yu={sensitive:!1,strict:!1,start:!0,end:!0},Ju=/[.+*?^${}()[\]/\\]/g;function Qu(e,t){const n=ne({},Yu,t),s=[];let r=n.start?"^":"";const o=[];for(const f of e){const c=f.length?[]:[90];n.strict&&!f.length&&(r+="/");for(let d=0;d<f.length;d++){const m=f[d];let y=40+(n.sensitive?.25:0);if(m.type===0)d||(r+="/"),r+=m.value.replace(Ju,"\\$&"),y+=40;else if(m.type===1){const{value:A,repeatable:x,optional:O,regexp:R}=m;o.push({name:A,repeatable:x,optional:O});const k=R||qr;if(k!==qr){y+=10;try{new RegExp(`(${k})`)}catch(L){throw new Error(`Invalid custom RegExp for param "${A}" (${k}): `+L.message)}}let D=x?`((?:${k})(?:/(?:${k}))*)`:`(${k})`;d||(D=O&&f.length<2?`(?:/${D})`:"/"+D),O&&(D+="?"),r+=D,y+=20,O&&(y+=-8),x&&(y+=-20),k===".*"&&(y+=-50)}c.push(y)}s.push(c)}if(n.strict&&n.end){const f=s.length-1;s[f][s[f].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const i=new RegExp(r,n.sensitive?"":"i");function l(f){const c=f.match(i),d={};if(!c)return null;for(let m=1;m<c.length;m++){const y=c[m]||"",A=o[m-1];d[A.name]=y&&A.repeatable?y.split("/"):y}return d}function a(f){let c="",d=!1;for(const m of e){(!d||!c.endsWith("/"))&&(c+="/"),d=!1;for(const y of m)if(y.type===0)c+=y.value;else if(y.type===1){const{value:A,repeatable:x,optional:O}=y,R=A in f?f[A]:"";if(Ge(R)&&!x)throw new Error(`Provided param "${A}" is an array but it is not repeatable (* or + modifiers)`);const k=Ge(R)?R.join("/"):R;if(!k)if(O)m.length<2&&(c.endsWith("/")?c=c.slice(0,-1):d=!0);else throw new Error(`Missing required param "${A}"`);c+=k}}return c||"/"}return{re:i,score:s,keys:o,parse:l,stringify:a}}function Xu(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Di(e,t){let n=0;const s=e.score,r=t.score;for(;n<s.length&&n<r.length;){const o=Xu(s[n],r[n]);if(o)return o;n++}if(Math.abs(r.length-s.length)===1){if(Yr(s))return 1;if(Yr(r))return-1}return r.length-s.length}function Yr(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Zu={type:0,value:""},ef=/[a-zA-Z0-9_]/;function tf(e){if(!e)return[[]];if(e==="/")return[[Zu]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(y){throw new Error(`ERR (${n})/"${f}": ${y}`)}let n=0,s=n;const r=[];let o;function i(){o&&r.push(o),o=[]}let l=0,a,f="",c="";function d(){f&&(n===0?o.push({type:0,value:f}):n===1||n===2||n===3?(o.length>1&&(a==="*"||a==="+")&&t(`A repeatable param (${f}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:f,regexp:c,repeatable:a==="*"||a==="+",optional:a==="*"||a==="?"})):t("Invalid state to consume buffer"),f="")}function m(){f+=a}for(;l<e.length;){if(a=e[l++],a==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:a==="/"?(f&&d(),i()):a===":"?(d(),n=1):m();break;case 4:m(),n=s;break;case 1:a==="("?n=2:ef.test(a)?m():(d(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--);break;case 2:a===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+a:n=3:c+=a;break;case 3:d(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--,c="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${f}"`),d(),i(),r}function nf(e,t,n){const s=Qu(tf(e.path),n),r=ne(s,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function sf(e,t){const n=[],s=new Map;t=Zr({strict:!1,end:!0,sensitive:!1},t);function r(d){return s.get(d)}function o(d,m,y){const A=!y,x=Qr(d);x.aliasOf=y&&y.record;const O=Zr(t,d),R=[x];if("alias"in d){const L=typeof d.alias=="string"?[d.alias]:d.alias;for(const U of L)R.push(Qr(ne({},x,{components:y?y.record.components:x.components,path:U,aliasOf:y?y.record:x})))}let k,D;for(const L of R){const{path:U}=L;if(m&&U[0]!=="/"){const re=m.record.path,Z=re[re.length-1]==="/"?"":"/";L.path=m.record.path+(U&&Z+U)}if(k=nf(L,m,O),y?y.alias.push(k):(D=D||k,D!==k&&D.alias.push(k),A&&d.name&&!Xr(k)&&i(d.name)),zi(k)&&a(k),x.children){const re=x.children;for(let Z=0;Z<re.length;Z++)o(re[Z],k,y&&y.children[Z])}y=y||k}return D?()=>{i(D)}:dn}function i(d){if(Li(d)){const m=s.get(d);m&&(s.delete(d),n.splice(n.indexOf(m),1),m.children.forEach(i),m.alias.forEach(i))}else{const m=n.indexOf(d);m>-1&&(n.splice(m,1),d.record.name&&s.delete(d.record.name),d.children.forEach(i),d.alias.forEach(i))}}function l(){return n}function a(d){const m=lf(d,n);n.splice(m,0,d),d.record.name&&!Xr(d)&&s.set(d.record.name,d)}function f(d,m){let y,A={},x,O;if("name"in d&&d.name){if(y=s.get(d.name),!y)throw Qt(1,{location:d});O=y.record.name,A=ne(Jr(m.params,y.keys.filter(D=>!D.optional).concat(y.parent?y.parent.keys.filter(D=>D.optional):[]).map(D=>D.name)),d.params&&Jr(d.params,y.keys.map(D=>D.name))),x=y.stringify(A)}else if(d.path!=null)x=d.path,y=n.find(D=>D.re.test(x)),y&&(A=y.parse(x),O=y.record.name);else{if(y=m.name?s.get(m.name):n.find(D=>D.re.test(m.path)),!y)throw Qt(1,{location:d,currentLocation:m});O=y.record.name,A=ne({},m.params,d.params),x=y.stringify(A)}const R=[];let k=y;for(;k;)R.unshift(k.record),k=k.parent;return{name:O,path:x,params:A,matched:R,meta:of(R)}}e.forEach(d=>o(d));function c(){n.length=0,s.clear()}return{addRoute:o,resolve:f,removeRoute:i,clearRoutes:c,getRoutes:l,getRecordMatcher:r}}function Jr(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function Qr(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:rf(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function rf(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function Xr(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function of(e){return e.reduce((t,n)=>ne(t,n.meta),{})}function Zr(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function lf(e,t){let n=0,s=t.length;for(;n!==s;){const o=n+s>>1;Di(e,t[o])<0?s=o:n=o+1}const r=af(e);return r&&(s=t.lastIndexOf(r,s-1)),s}function af(e){let t=e;for(;t=t.parent;)if(zi(t)&&Di(e,t)===0)return t}function zi({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function cf(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<s.length;++r){const o=s[r].replace(Pi," "),i=o.indexOf("="),l=Sn(i<0?o:o.slice(0,i)),a=i<0?null:Sn(o.slice(i+1));if(l in t){let f=t[l];Ge(f)||(f=t[l]=[f]),f.push(a)}else t[l]=a}return t}function eo(e){let t="";for(let n in e){const s=e[n];if(n=Ru(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(Ge(s)?s.map(o=>o&&ks(o)):[s&&ks(s)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function uf(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=Ge(s)?s.map(r=>r==null?null:""+r):s==null?s:""+s)}return t}const ff=Symbol(""),to=Symbol(""),tr=Symbol(""),Hi=Symbol(""),Is=Symbol("");function tn(){let e=[];function t(s){return e.push(s),()=>{const r=e.indexOf(s);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function St(e,t,n,s,r,o=i=>i()){const i=s&&(s.enterCallbacks[r]=s.enterCallbacks[r]||[]);return()=>new Promise((l,a)=>{const f=m=>{m===!1?a(Qt(4,{from:n,to:t})):m instanceof Error?a(m):qu(m)?a(Qt(2,{from:t,to:m})):(i&&s.enterCallbacks[r]===i&&typeof m=="function"&&i.push(m),l())},c=o(()=>e.call(s&&s.instances[r],t,n,f));let d=Promise.resolve(c);e.length<3&&(d=d.then(f)),d.catch(m=>a(m))})}function ys(e,t,n,s,r=o=>o()){const o=[];for(const i of e)for(const l in i.components){let a=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(Ai(a)){const c=(a.__vccOpts||a)[t];c&&o.push(St(c,n,s,i,l,r))}else{let f=a();o.push(()=>f.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const d=vu(c)?c.default:c;i.mods[l]=c,i.components[l]=d;const y=(d.__vccOpts||d)[t];return y&&St(y,n,s,i,l,r)()}))}}return o}function no(e){const t=ut(tr),n=ut(Hi),s=Be(()=>{const a=Kt(e.to);return t.resolve(a)}),r=Be(()=>{const{matched:a}=s.value,{length:f}=a,c=a[f-1],d=n.matched;if(!c||!d.length)return-1;const m=d.findIndex(Jt.bind(null,c));if(m>-1)return m;const y=so(a[f-2]);return f>1&&so(c)===y&&d[d.length-1].path!==y?d.findIndex(Jt.bind(null,a[f-2])):m}),o=Be(()=>r.value>-1&&mf(n.params,s.value.params)),i=Be(()=>r.value>-1&&r.value===n.matched.length-1&&$i(n.params,s.value.params));function l(a={}){if(gf(a)){const f=t[Kt(e.replace)?"replace":"push"](Kt(e.to)).catch(dn);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>f),f}return Promise.resolve()}return{route:s,href:Be(()=>s.value.href),isActive:o,isExactActive:i,navigate:l}}function df(e){return e.length===1?e[0]:e}const hf=Dt({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:no,setup(e,{slots:t}){const n=Wn(no(e)),{options:s}=ut(tr),r=Be(()=>({[ro(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[ro(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&df(t.default(n));return e.custom?o:Zs("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}}),pf=hf;function gf(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function mf(e,t){for(const n in t){const s=t[n],r=e[n];if(typeof s=="string"){if(s!==r)return!1}else if(!Ge(r)||r.length!==s.length||s.some((o,i)=>o!==r[i]))return!1}return!0}function so(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const ro=(e,t,n)=>e??t??n,vf=Dt({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=ut(Is),r=Be(()=>e.route||s.value),o=ut(to,0),i=Be(()=>{let f=Kt(o);const{matched:c}=r.value;let d;for(;(d=c[f])&&!d.components;)f++;return f}),l=Be(()=>r.value.matched[i.value]);On(to,Be(()=>i.value+1)),On(ff,l),On(Is,r);const a=ae();return kn(()=>[a.value,l.value,e.name],([f,c,d],[m,y,A])=>{c&&(c.instances[d]=f,y&&y!==c&&f&&f===m&&(c.leaveGuards.size||(c.leaveGuards=y.leaveGuards),c.updateGuards.size||(c.updateGuards=y.updateGuards))),f&&c&&(!y||!Jt(c,y)||!m)&&(c.enterCallbacks[d]||[]).forEach(x=>x(f))},{flush:"post"}),()=>{const f=r.value,c=e.name,d=l.value,m=d&&d.components[c];if(!m)return oo(n.default,{Component:m,route:f});const y=d.props[c],A=y?y===!0?f.params:typeof y=="function"?y(f):y:null,O=Zs(m,ne({},A,t,{onVnodeUnmounted:R=>{R.component.isUnmounted&&(d.instances[c]=null)},ref:a}));return oo(n.default,{Component:O,route:f})||O}}});function oo(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const yf=vf;function bf(e){const t=sf(e.routes,e),n=e.parseQuery||cf,s=e.stringifyQuery||eo,r=e.history,o=tn(),i=tn(),l=tn(),a=yl(vt);let f=vt;Vt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=ms.bind(null,_=>""+_),d=ms.bind(null,Mu),m=ms.bind(null,Sn);function y(_,$){let P,z;return Li(_)?(P=t.getRecordMatcher(_),z=$):z=_,t.addRoute(z,P)}function A(_){const $=t.getRecordMatcher(_);$&&t.removeRoute($)}function x(){return t.getRoutes().map(_=>_.record)}function O(_){return!!t.getRecordMatcher(_)}function R(_,$){if($=ne({},$||a.value),typeof _=="string"){const v=vs(n,_,$.path),w=t.resolve({path:v.path},$),g=r.createHref(v.fullPath);return ne(v,w,{params:m(w.params),hash:Sn(v.hash),redirectedFrom:void 0,href:g})}let P;if(_.path!=null)P=ne({},_,{path:vs(n,_.path,$.path).path});else{const v=ne({},_.params);for(const w in v)v[w]==null&&delete v[w];P=ne({},_,{params:d(v)}),$.params=d($.params)}const z=t.resolve(P,$),oe=_.hash||"";z.params=c(m(z.params));const u=$u(s,ne({},_,{hash:Au(oe),path:z.path})),h=r.createHref(u);return ne({fullPath:u,hash:oe,query:s===eo?uf(_.query):_.query||{}},z,{redirectedFrom:void 0,href:h})}function k(_){return typeof _=="string"?vs(n,_,a.value.path):ne({},_)}function D(_,$){if(f!==_)return Qt(8,{from:$,to:_})}function L(_){return Z(_)}function U(_){return L(ne(k(_),{replace:!0}))}function re(_){const $=_.matched[_.matched.length-1];if($&&$.redirect){const{redirect:P}=$;let z=typeof P=="function"?P(_):P;return typeof z=="string"&&(z=z.includes("?")||z.includes("#")?z=k(z):{path:z},z.params={}),ne({query:_.query,hash:_.hash,params:z.path!=null?{}:_.params},z)}}function Z(_,$){const P=f=R(_),z=a.value,oe=_.state,u=_.force,h=_.replace===!0,v=re(P);if(v)return Z(ne(k(v),{state:typeof v=="object"?ne({},oe,v.state):oe,force:u,replace:h}),$||P);const w=P;w.redirectedFrom=$;let g;return!u&&Iu(s,z,P)&&(g=Qt(16,{to:w,from:z}),He(z,z,!0,!1)),(g?Promise.resolve(g):q(w,z)).catch(p=>it(p)?it(p,2)?p:We(p):Y(p,w,z)).then(p=>{if(p){if(it(p,2))return Z(ne({replace:h},k(p.to),{state:typeof p.to=="object"?ne({},oe,p.to.state):oe,force:u}),$||w)}else p=N(w,z,!0,h,oe);return le(w,z,p),p})}function be(_,$){const P=D(_,$);return P?Promise.reject(P):Promise.resolve()}function j(_){const $=rt.values().next().value;return $&&typeof $.runWithContext=="function"?$.runWithContext(_):_()}function q(_,$){let P;const[z,oe,u]=_f(_,$);P=ys(z.reverse(),"beforeRouteLeave",_,$);for(const v of z)v.leaveGuards.forEach(w=>{P.push(St(w,_,$))});const h=be.bind(null,_,$);return P.push(h),Oe(P).then(()=>{P=[];for(const v of o.list())P.push(St(v,_,$));return P.push(h),Oe(P)}).then(()=>{P=ys(oe,"beforeRouteUpdate",_,$);for(const v of oe)v.updateGuards.forEach(w=>{P.push(St(w,_,$))});return P.push(h),Oe(P)}).then(()=>{P=[];for(const v of u)if(v.beforeEnter)if(Ge(v.beforeEnter))for(const w of v.beforeEnter)P.push(St(w,_,$));else P.push(St(v.beforeEnter,_,$));return P.push(h),Oe(P)}).then(()=>(_.matched.forEach(v=>v.enterCallbacks={}),P=ys(u,"beforeRouteEnter",_,$,j),P.push(h),Oe(P))).then(()=>{P=[];for(const v of i.list())P.push(St(v,_,$));return P.push(h),Oe(P)}).catch(v=>it(v,8)?v:Promise.reject(v))}function le(_,$,P){l.list().forEach(z=>j(()=>z(_,$,P)))}function N(_,$,P,z,oe){const u=D(_,$);if(u)return u;const h=$===vt,v=Vt?history.state:{};P&&(z||h?r.replace(_.fullPath,ne({scroll:h&&v&&v.scroll},oe)):r.push(_.fullPath,oe)),a.value=_,He(_,$,P,h),We()}let G;function ge(){G||(G=r.listen((_,$,P)=>{if(!je.listening)return;const z=R(_),oe=re(z);if(oe){Z(ne(oe,{replace:!0,force:!0}),z).catch(dn);return}f=z;const u=a.value;Vt&&ju(Kr(u.fullPath,P.delta),ts()),q(z,u).catch(h=>it(h,12)?h:it(h,2)?(Z(ne(k(h.to),{force:!0}),z).then(v=>{it(v,20)&&!P.delta&&P.type===En.pop&&r.go(-1,!1)}).catch(dn),Promise.reject()):(P.delta&&r.go(-P.delta,!1),Y(h,z,u))).then(h=>{h=h||N(z,u,!1),h&&(P.delta&&!it(h,8)?r.go(-P.delta,!1):P.type===En.pop&&it(h,20)&&r.go(-1,!1)),le(z,u,h)}).catch(dn)}))}let Ee=tn(),ee=tn(),te;function Y(_,$,P){We(_);const z=ee.list();return z.length?z.forEach(oe=>oe(_,$,P)):console.error(_),Promise.reject(_)}function ze(){return te&&a.value!==vt?Promise.resolve():new Promise((_,$)=>{Ee.add([_,$])})}function We(_){return te||(te=!_,ge(),Ee.list().forEach(([$,P])=>_?P(_):$()),Ee.reset()),_}function He(_,$,P,z){const{scrollBehavior:oe}=e;if(!Vt||!oe)return Promise.resolve();const u=!P&&Bu(Kr(_.fullPath,0))||(z||!P)&&history.state&&history.state.scroll||null;return No().then(()=>oe(_,$,u)).then(h=>h&&Vu(h)).catch(h=>Y(h,_,$))}const Ce=_=>r.go(_);let st;const rt=new Set,je={currentRoute:a,listening:!0,addRoute:y,removeRoute:A,clearRoutes:t.clearRoutes,hasRoute:O,getRoutes:x,resolve:R,options:e,push:L,replace:U,go:Ce,back:()=>Ce(-1),forward:()=>Ce(1),beforeEach:o.add,beforeResolve:i.add,afterEach:l.add,onError:ee.add,isReady:ze,install(_){const $=this;_.component("RouterLink",pf),_.component("RouterView",yf),_.config.globalProperties.$router=$,Object.defineProperty(_.config.globalProperties,"$route",{enumerable:!0,get:()=>Kt(a)}),Vt&&!st&&a.value===vt&&(st=!0,L(r.location).catch(oe=>{}));const P={};for(const oe in vt)Object.defineProperty(P,oe,{get:()=>a.value[oe],enumerable:!0});_.provide(tr,$),_.provide(Hi,Mo(P)),_.provide(Is,a);const z=_.unmount;rt.add(_),_.unmount=function(){rt.delete(_),rt.size<1&&(f=vt,G&&G(),G=null,a.value=vt,st=!1,te=!1),z()}}};function Oe(_){return _.reduce(($,P)=>$.then(()=>j(P)),Promise.resolve())}return je}function _f(e,t){const n=[],s=[],r=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const l=t.matched[i];l&&(e.matched.find(f=>Jt(f,l))?s.push(l):n.push(l));const a=e.matched[i];a&&(t.matched.find(f=>Jt(f,a))||r.push(a))}return[n,s,r]}const wf={},Sf={class:"item"},Ef={class:"details"};function Cf(e,t){return K(),J("div",Sf,[T("i",null,[as(e.$slots,"icon",{},void 0)]),T("div",Ef,[T("h3",null,[as(e.$slots,"heading",{},void 0)]),as(e.$slots,"default",{},void 0)])])}const nn=gt(wf,[["render",Cf],["__scopeId","data-v-fd0742eb"]]),xf={},Tf={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"17",fill:"currentColor"};function Af(e,t){return K(),J("svg",Tf,t[0]||(t[0]=[T("path",{d:"M11 2.253a1 1 0 1 0-2 0h2zm-2 13a1 1 0 1 0 2 0H9zm.447-12.167a1 1 0 1 0 1.107-1.666L9.447 3.086zM1 2.253L.447 1.42A1 1 0 0 0 0 2.253h1zm0 13H0a1 1 0 0 0 1.553.833L1 15.253zm8.447.833a1 1 0 1 0 1.107-1.666l-1.107 1.666zm0-14.666a1 1 0 1 0 1.107 1.666L9.447 1.42zM19 2.253h1a1 1 0 0 0-.447-.833L19 2.253zm0 13l-.553.833A1 1 0 0 0 20 15.253h-1zm-9.553-.833a1 1 0 1 0 1.107 1.666L9.447 14.42zM9 2.253v13h2v-13H9zm1.553-.833C9.203.523 7.42 0 5.5 0v2c1.572 0 2.961.431 3.947 1.086l1.107-1.666zM5.5 0C3.58 0 1.797.523.447 1.42l1.107 1.666C2.539 2.431 3.928 2 5.5 2V0zM0 2.253v13h2v-13H0zm1.553 13.833C2.539 15.431 3.928 15 5.5 15v-2c-1.92 0-3.703.523-5.053 1.42l1.107 1.666zM5.5 15c1.572 0 2.961.431 3.947 1.086l1.107-1.666C9.203 13.523 7.42 13 5.5 13v2zm5.053-11.914C11.539 2.431 12.928 2 14.5 2V0c-1.92 0-3.703.523-5.053 1.42l1.107 1.666zM14.5 2c1.573 0 2.961.431 3.947 1.086l1.107-1.666C18.203.523 16.421 0 14.5 0v2zm3.5.253v13h2v-13h-2zm1.553 12.167C18.203 13.523 16.421 13 14.5 13v2c1.573 0 2.961.431 3.947 1.086l1.107-1.666zM14.5 13c-1.92 0-3.703.523-5.053 1.42l1.107 1.666C11.539 15.431 12.928 15 14.5 15v-2z"},null,-1)]))}const Rf=gt(xf,[["render",Af]]),Pf={},Mf={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink","aria-hidden":"true",role:"img",class:"iconify iconify--mdi",width:"24",height:"24",preserveAspectRatio:"xMidYMid meet",viewBox:"0 0 24 24"};function Of(e,t){return K(),J("svg",Mf,t[0]||(t[0]=[T("path",{d:"M20 18v-4h-3v1h-2v-1H9v1H7v-1H4v4h16M6.33 8l-1.74 4H7v-1h2v1h6v-1h2v1h2.41l-1.74-4H6.33M9 5v1h6V5H9m12.84 7.61c.1.22.16.48.16.8V18c0 .53-.21 1-.6 1.41c-.4.4-.85.59-1.4.59H4c-.55 0-1-.19-1.4-.59C2.21 19 2 18.53 2 18v-4.59c0-.32.06-.58.16-.8L4.5 7.22C4.84 6.41 5.45 6 6.33 6H7V5c0-.55.18-1 .57-1.41C7.96 3.2 8.44 3 9 3h6c.56 0 1.04.2 1.43.59c.39.41.57.86.57 1.41v1h.67c.88 0 1.49.41 1.83 1.22l2.34 5.39z",fill:"currentColor"},null,-1)]))}const kf=gt(Pf,[["render",Of]]),$f={},If={xmlns:"http://www.w3.org/2000/svg",width:"18",height:"20",fill:"currentColor"};function Lf(e,t){return K(),J("svg",If,t[0]||(t[0]=[T("path",{d:"M11.447 8.894a1 1 0 1 0-.894-1.789l.894 1.789zm-2.894-.789a1 1 0 1 0 .894 1.789l-.894-1.789zm0 1.789a1 1 0 1 0 .894-1.789l-.894 1.789zM7.447 7.106a1 1 0 1 0-.894 1.789l.894-1.789zM10 9a1 1 0 1 0-2 0h2zm-2 2.5a1 1 0 1 0 2 0H8zm9.447-5.606a1 1 0 1 0-.894-1.789l.894 1.789zm-2.894-.789a1 1 0 1 0 .894 1.789l-.894-1.789zm2 .789a1 1 0 1 0 .894-1.789l-.894 1.789zm-1.106-2.789a1 1 0 1 0-.894 1.789l.894-1.789zM18 5a1 1 0 1 0-2 0h2zm-2 2.5a1 1 0 1 0 2 0h-2zm-5.447-4.606a1 1 0 1 0 .894-1.789l-.894 1.789zM9 1l.447-.894a1 1 0 0 0-.894 0L9 1zm-2.447.106a1 1 0 1 0 .894 1.789l-.894-1.789zm-6 3a1 1 0 1 0 .894 1.789L.553 4.106zm2.894.789a1 1 0 1 0-.894-1.789l.894 1.789zm-2-.789a1 1 0 1 0-.894 1.789l.894-1.789zm1.106 2.789a1 1 0 1 0 .894-1.789l-.894 1.789zM2 5a1 1 0 1 0-2 0h2zM0 7.5a1 1 0 1 0 2 0H0zm8.553 12.394a1 1 0 1 0 .894-1.789l-.894 1.789zm-1.106-2.789a1 1 0 1 0-.894 1.789l.894-1.789zm1.106 1a1 1 0 1 0 .894 1.789l-.894-1.789zm2.894.789a1 1 0 1 0-.894-1.789l.894 1.789zM8 19a1 1 0 1 0 2 0H8zm2-2.5a1 1 0 1 0-2 0h2zm-7.447.394a1 1 0 1 0 .894-1.789l-.894 1.789zM1 15H0a1 1 0 0 0 .553.894L1 15zm1-2.5a1 1 0 1 0-2 0h2zm12.553 2.606a1 1 0 1 0 .894 1.789l-.894-1.789zM17 15l.447.894A1 1 0 0 0 18 15h-1zm1-2.5a1 1 0 1 0-2 0h2zm-7.447-5.394l-2 1 .894 1.789 2-1-.894-1.789zm-1.106 1l-2-1-.894 1.789 2 1 .894-1.789zM8 9v2.5h2V9H8zm8.553-4.894l-2 1 .894 1.789 2-1-.894-1.789zm.894 0l-2-1-.894 1.789 2 1 .894-1.789zM16 5v2.5h2V5h-2zm-4.553-3.894l-2-1-.894 1.789 2 1 .894-1.789zm-2.894-1l-2 1 .894 1.789 2-1L8.553.106zM1.447 5.894l2-1-.894-1.789-2 1 .894 1.789zm-.894 0l2 1 .894-1.789-2-1-.894 1.789zM0 5v2.5h2V5H0zm9.447 13.106l-2-1-.894 1.789 2 1 .894-1.789zm0 1.789l2-1-.894-1.789-2 1 .894 1.789zM10 19v-2.5H8V19h2zm-6.553-3.894l-2-1-.894 1.789 2 1 .894-1.789zM2 15v-2.5H0V15h2zm13.447 1.894l2-1-.894-1.789-2 1 .894 1.789zM18 15v-2.5h-2V15h2z"},null,-1)]))}const Nf=gt($f,[["render",Lf]]),Df={},zf={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"currentColor"};function Hf(e,t){return K(),J("svg",zf,t[0]||(t[0]=[T("path",{d:"M15 4a1 1 0 1 0 0 2V4zm0 11v-1a1 1 0 0 0-1 1h1zm0 4l-.707.707A1 1 0 0 0 16 19h-1zm-4-4l.707-.707A1 1 0 0 0 11 14v1zm-4.707-1.293a1 1 0 0 0-1.414 1.414l1.414-1.414zm-.707.707l-.707-.707.707.707zM9 11v-1a1 1 0 0 0-.707.293L9 11zm-4 0h1a1 1 0 0 0-1-1v1zm0 4H4a1 1 0 0 0 1.707.707L5 15zm10-9h2V4h-2v2zm2 0a1 1 0 0 1 1 1h2a3 3 0 0 0-3-3v2zm1 1v6h2V7h-2zm0 6a1 1 0 0 1-1 1v2a3 3 0 0 0 3-3h-2zm-1 1h-2v2h2v-2zm-3 1v4h2v-4h-2zm1.707 3.293l-4-4-1.414 1.414 4 4 1.414-1.414zM11 14H7v2h4v-2zm-4 0c-.276 0-.525-.111-.707-.293l-1.414 1.414C5.42 15.663 6.172 16 7 16v-2zm-.707 1.121l3.414-3.414-1.414-1.414-3.414 3.414 1.414 1.414zM9 12h4v-2H9v2zm4 0a3 3 0 0 0 3-3h-2a1 1 0 0 1-1 1v2zm3-3V3h-2v6h2zm0-6a3 3 0 0 0-3-3v2a1 1 0 0 1 1 1h2zm-3-3H3v2h10V0zM3 0a3 3 0 0 0-3 3h2a1 1 0 0 1 1-1V0zM0 3v6h2V3H0zm0 6a3 3 0 0 0 3 3v-2a1 1 0 0 1-1-1H0zm3 3h2v-2H3v2zm1-1v4h2v-4H4zm1.707 4.707l.586-.586-1.414-1.414-.586.586 1.414 1.414z"},null,-1)]))}const Ff=gt(Df,[["render",Hf]]),Vf={},jf={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"currentColor"};function Bf(e,t){return K(),J("svg",jf,t[0]||(t[0]=[T("path",{d:"M10 3.22l-.61-.6a5.5 5.5 0 0 0-7.666.105 5.5 5.5 0 0 0-.114 7.665L10 18.78l8.39-8.4a5.5 5.5 0 0 0-.114-7.665 5.5 5.5 0 0 0-7.666-.105l-.61.61z"},null,-1)]))}const Uf=gt(Vf,[["render",Bf]]),Kf=Dt({__name:"TheWelcome",setup(e){const t=()=>fetch("/__open-in-editor?file=README.md");return(n,s)=>(K(),J(me,null,[ce(nn,null,{icon:_e(()=>[ce(Rf)]),heading:_e(()=>s[0]||(s[0]=[X("Documentation")])),default:_e(()=>[s[1]||(s[1]=X(" Vue’s ")),s[2]||(s[2]=T("a",{href:"https://vuejs.org/",target:"_blank",rel:"noopener"},"official documentation",-1)),s[3]||(s[3]=X(" provides you with all information you need to get started. "))]),_:1,__:[1,2,3]}),ce(nn,null,{icon:_e(()=>[ce(kf)]),heading:_e(()=>s[4]||(s[4]=[X("Tooling")])),default:_e(()=>[s[6]||(s[6]=X(" This project is served and bundled with ")),s[7]||(s[7]=T("a",{href:"https://vite.dev/guide/features.html",target:"_blank",rel:"noopener"},"Vite",-1)),s[8]||(s[8]=X(". The recommended IDE setup is ")),s[9]||(s[9]=T("a",{href:"https://code.visualstudio.com/",target:"_blank",rel:"noopener"},"VSCode",-1)),s[10]||(s[10]=X(" + ")),s[11]||(s[11]=T("a",{href:"https://github.com/vuejs/language-tools",target:"_blank",rel:"noopener"},"Vue - Official",-1)),s[12]||(s[12]=X(". If you need to test your components and web pages, check out ")),s[13]||(s[13]=T("a",{href:"https://vitest.dev/",target:"_blank",rel:"noopener"},"Vitest",-1)),s[14]||(s[14]=X(" and ")),s[15]||(s[15]=T("a",{href:"https://www.cypress.io/",target:"_blank",rel:"noopener"},"Cypress",-1)),s[16]||(s[16]=X(" / ")),s[17]||(s[17]=T("a",{href:"https://playwright.dev/",target:"_blank",rel:"noopener"},"Playwright",-1)),s[18]||(s[18]=X(". ")),s[19]||(s[19]=T("br",null,null,-1)),s[20]||(s[20]=X(" More instructions are available in ")),T("a",{href:"javascript:void(0)",onClick:t},s[5]||(s[5]=[T("code",null,"README.md",-1)])),s[21]||(s[21]=X(". "))]),_:1,__:[6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21]}),ce(nn,null,{icon:_e(()=>[ce(Nf)]),heading:_e(()=>s[22]||(s[22]=[X("Ecosystem")])),default:_e(()=>[s[23]||(s[23]=X(" Get official tools and libraries for your project: ")),s[24]||(s[24]=T("a",{href:"https://pinia.vuejs.org/",target:"_blank",rel:"noopener"},"Pinia",-1)),s[25]||(s[25]=X(", ")),s[26]||(s[26]=T("a",{href:"https://router.vuejs.org/",target:"_blank",rel:"noopener"},"Vue Router",-1)),s[27]||(s[27]=X(", ")),s[28]||(s[28]=T("a",{href:"https://test-utils.vuejs.org/",target:"_blank",rel:"noopener"},"Vue Test Utils",-1)),s[29]||(s[29]=X(", and ")),s[30]||(s[30]=T("a",{href:"https://github.com/vuejs/devtools",target:"_blank",rel:"noopener"},"Vue Dev Tools",-1)),s[31]||(s[31]=X(". If you need more resources, we suggest paying ")),s[32]||(s[32]=T("a",{href:"https://github.com/vuejs/awesome-vue",target:"_blank",rel:"noopener"},"Awesome Vue",-1)),s[33]||(s[33]=X(" a visit. "))]),_:1,__:[23,24,25,26,27,28,29,30,31,32,33]}),ce(nn,null,{icon:_e(()=>[ce(Ff)]),heading:_e(()=>s[34]||(s[34]=[X("Community")])),default:_e(()=>[s[35]||(s[35]=X(" Got stuck? Ask your question on ")),s[36]||(s[36]=T("a",{href:"https://chat.vuejs.org",target:"_blank",rel:"noopener"},"Vue Land",-1)),s[37]||(s[37]=X(" (our official Discord server), or ")),s[38]||(s[38]=T("a",{href:"https://stackoverflow.com/questions/tagged/vue.js",target:"_blank",rel:"noopener"},"StackOverflow",-1)),s[39]||(s[39]=X(". You should also follow the official ")),s[40]||(s[40]=T("a",{href:"https://bsky.app/profile/vuejs.org",target:"_blank",rel:"noopener"},"@vuejs.org",-1)),s[41]||(s[41]=X(" Bluesky account or the ")),s[42]||(s[42]=T("a",{href:"https://x.com/vuejs",target:"_blank",rel:"noopener"},"@vuejs",-1)),s[43]||(s[43]=X(" X account for latest news in the Vue world. "))]),_:1,__:[35,36,37,38,39,40,41,42,43]}),ce(nn,null,{icon:_e(()=>[ce(Uf)]),heading:_e(()=>s[44]||(s[44]=[X("Support Vue")])),default:_e(()=>[s[45]||(s[45]=X(" As an independent project, Vue relies on community backing for its sustainability. You can help us by ")),s[46]||(s[46]=T("a",{href:"https://vuejs.org/sponsor/",target:"_blank",rel:"noopener"},"becoming a sponsor",-1)),s[47]||(s[47]=X(". "))]),_:1,__:[45,46,47]})],64))}}),Gf=Dt({__name:"HomeView",setup(e){return(t,n)=>(K(),J("main",null,[ce(Kf)]))}}),Wf=bf({history:Wu("/"),routes:[{path:"/",name:"home",component:Gf},{path:"/about",name:"about",component:()=>mu(()=>import("./AboutView-BHcppUv3.js"),__vite__mapDeps([0,1]))}]}),nr=uc(hu);nr.use(pc());nr.use(Wf);nr.mount("#app");export{gt as _,T as a,J as c,K as o};
